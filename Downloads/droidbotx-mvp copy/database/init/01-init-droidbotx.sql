-- DroidBotX Database Initialization Script
-- This script sets up the basic database structure for DroidBotX generated applications

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schemas for organization
CREATE SCHEMA IF NOT EXISTS droidbotx;
CREATE SCHEMA IF NOT EXISTS generated_apps;

-- Set default search path
ALTER DATABASE droidbotx_db SET search_path TO droidbotx, generated_apps, public;

-- Create DroidBotX metadata tables
CREATE TABLE IF NOT EXISTS droidbotx.workflow_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    requirements JSONB,
    context JSONB,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS droidbotx.phase_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES droidbotx.workflow_sessions(id) ON DELETE CASCADE,
    phase_name VARCHAR(100) NOT NULL,
    agent_name VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_ms INTEGER,
    score INTEGER,
    error_message TEXT,
    output JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS droidbotx.generated_projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES droidbotx.workflow_sessions(id) ON DELETE CASCADE,
    project_name VARCHAR(255) NOT NULL,
    project_path TEXT,
    file_count INTEGER DEFAULT 0,
    production_score INTEGER DEFAULT 0,
    phases_completed INTEGER DEFAULT 0,
    total_phases INTEGER DEFAULT 10,
    files_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_workflow_sessions_session_id ON droidbotx.workflow_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_workflow_sessions_status ON droidbotx.workflow_sessions(status);
CREATE INDEX IF NOT EXISTS idx_workflow_sessions_created_at ON droidbotx.workflow_sessions(created_at);

CREATE INDEX IF NOT EXISTS idx_phase_executions_session_id ON droidbotx.phase_executions(session_id);
CREATE INDEX IF NOT EXISTS idx_phase_executions_phase_name ON droidbotx.phase_executions(phase_name);
CREATE INDEX IF NOT EXISTS idx_phase_executions_status ON droidbotx.phase_executions(status);

CREATE INDEX IF NOT EXISTS idx_generated_projects_session_id ON droidbotx.generated_projects(session_id);
CREATE INDEX IF NOT EXISTS idx_generated_projects_project_name ON droidbotx.generated_projects(project_name);

-- Create common tables that generated applications might need
-- These serve as templates and can be customized per application

-- Users table template
CREATE TABLE IF NOT EXISTS generated_apps.users_template (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table template
CREATE TABLE IF NOT EXISTS generated_apps.products_template (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    sku VARCHAR(100) UNIQUE,
    category_id UUID,
    stock_quantity INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table template
CREATE TABLE IF NOT EXISTS generated_apps.categories_template (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES generated_apps.categories_template(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table template
CREATE TABLE IF NOT EXISTS generated_apps.orders_template (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    status VARCHAR(50) DEFAULT 'pending',
    total_amount DECIMAL(10,2),
    shipping_address JSONB,
    billing_address JSONB,
    payment_status VARCHAR(50) DEFAULT 'pending',
    payment_method VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items table template
CREATE TABLE IF NOT EXISTS generated_apps.order_items_template (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID,
    product_id UUID,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tasks table template (for task management apps)
CREATE TABLE IF NOT EXISTS generated_apps.tasks_template (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    assigned_to UUID,
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create functions for common operations
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_workflow_sessions_updated_at BEFORE UPDATE ON droidbotx.workflow_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_generated_projects_updated_at BEFORE UPDATE ON droidbotx.generated_projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT USAGE ON SCHEMA droidbotx TO droidbotx_user;
GRANT USAGE ON SCHEMA generated_apps TO droidbotx_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA droidbotx TO droidbotx_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA generated_apps TO droidbotx_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA droidbotx TO droidbotx_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA generated_apps TO droidbotx_user;

-- Insert initial data
INSERT INTO droidbotx.workflow_sessions (session_id, description, status) 
VALUES ('system-init', 'System initialization session', 'completed')
ON CONFLICT (session_id) DO NOTHING;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'DroidBotX database initialization completed successfully';
    RAISE NOTICE 'Database: droidbotx_db';
    RAISE NOTICE 'User: droidbotx_user';
    RAISE NOTICE 'Schemas: droidbotx, generated_apps, public';
END $$;

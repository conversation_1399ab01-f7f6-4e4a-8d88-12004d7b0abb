-- DroidBotX Test Database Initialization Script
-- This script sets up the test database structure for DroidBotX testing

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schemas for testing
CREATE SCHEMA IF NOT EXISTS droidbotx_test;
CREATE SCHEMA IF NOT EXISTS test_apps;

-- Set default search path
ALTER DATABASE droidbotx_test_db SET search_path TO droidbotx_test, test_apps, public;

-- Create test-specific tables
CREATE TABLE IF NOT EXISTS droidbotx_test.test_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    test_name VARCHAR(255) NOT NULL,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    test_type VARCHAR(100) DEFAULT 'integration',
    status VARCHAR(50) DEFAULT 'running',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_ms INTEGER,
    assertions_passed INTEGER DEFAULT 0,
    assertions_failed INTEGER DEFAULT 0,
    error_details JSONB,
    test_data JSONB
);

-- Create test data tables
CREATE TABLE IF NOT EXISTS test_apps.test_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) DEFAULT 'Test',
    last_name VARCHAR(100) DEFAULT 'User',
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS test_apps.test_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT DEFAULT 'Test product description',
    price DECIMAL(10,2) DEFAULT 99.99,
    sku VARCHAR(100) UNIQUE,
    stock_quantity INTEGER DEFAULT 100,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS test_apps.test_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES test_apps.test_users(id),
    status VARCHAR(50) DEFAULT 'pending',
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS test_apps.test_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT DEFAULT 'Test task description',
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for test performance
CREATE INDEX IF NOT EXISTS idx_test_sessions_session_id ON droidbotx_test.test_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_test_sessions_status ON droidbotx_test.test_sessions(status);
CREATE INDEX IF NOT EXISTS idx_test_users_email ON test_apps.test_users(email);
CREATE INDEX IF NOT EXISTS idx_test_products_sku ON test_apps.test_products(sku);

-- Grant permissions to test user
GRANT USAGE ON SCHEMA droidbotx_test TO droidbotx_test_user;
GRANT USAGE ON SCHEMA test_apps TO droidbotx_test_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA droidbotx_test TO droidbotx_test_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA test_apps TO droidbotx_test_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA droidbotx_test TO droidbotx_test_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA test_apps TO droidbotx_test_user;

-- Insert test data
INSERT INTO test_apps.test_users (email, password_hash, first_name, last_name) VALUES
('<EMAIL>', '$2b$10$test.hash.1', 'John', 'Doe'),
('<EMAIL>', '$2b$10$test.hash.2', 'Jane', 'Smith'),
('<EMAIL>', '$2b$10$test.hash.admin', 'Admin', 'User')
ON CONFLICT (email) DO NOTHING;

INSERT INTO test_apps.test_products (name, description, price, sku) VALUES
('Test Product 1', 'First test product', 29.99, 'TEST-001'),
('Test Product 2', 'Second test product', 49.99, 'TEST-002'),
('Test Product 3', 'Third test product', 19.99, 'TEST-003')
ON CONFLICT (sku) DO NOTHING;

INSERT INTO test_apps.test_tasks (title, description, status, priority) VALUES
('Test Task 1', 'First test task', 'pending', 'high'),
('Test Task 2', 'Second test task', 'in_progress', 'medium'),
('Test Task 3', 'Third test task', 'completed', 'low')
ON CONFLICT DO NOTHING;

-- Create test helper functions
CREATE OR REPLACE FUNCTION droidbotx_test.cleanup_test_data()
RETURNS void AS $$
BEGIN
    -- Clean up test data but preserve structure
    DELETE FROM test_apps.test_orders;
    DELETE FROM test_apps.test_products WHERE sku LIKE 'TEMP-%';
    DELETE FROM test_apps.test_users WHERE email LIKE '%@temp.test';
    DELETE FROM test_apps.test_tasks WHERE title LIKE 'TEMP_%';
    
    -- Reset sequences
    ALTER SEQUENCE IF EXISTS test_apps.test_users_id_seq RESTART WITH 1;
    ALTER SEQUENCE IF EXISTS test_apps.test_products_id_seq RESTART WITH 1;
    ALTER SEQUENCE IF EXISTS test_apps.test_orders_id_seq RESTART WITH 1;
    ALTER SEQUENCE IF EXISTS test_apps.test_tasks_id_seq RESTART WITH 1;
    
    RAISE NOTICE 'Test data cleanup completed';
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION droidbotx_test.create_test_session(
    p_test_name VARCHAR(255),
    p_session_id VARCHAR(255),
    p_test_type VARCHAR(100) DEFAULT 'integration'
)
RETURNS UUID AS $$
DECLARE
    session_uuid UUID;
BEGIN
    INSERT INTO droidbotx_test.test_sessions (test_name, session_id, test_type)
    VALUES (p_test_name, p_session_id, p_test_type)
    RETURNING id INTO session_uuid;
    
    RETURN session_uuid;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION droidbotx_test.complete_test_session(
    p_session_id VARCHAR(255),
    p_assertions_passed INTEGER DEFAULT 0,
    p_assertions_failed INTEGER DEFAULT 0,
    p_error_details JSONB DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    UPDATE droidbotx_test.test_sessions 
    SET 
        status = CASE WHEN p_assertions_failed = 0 THEN 'passed' ELSE 'failed' END,
        completed_at = NOW(),
        duration_ms = EXTRACT(EPOCH FROM (NOW() - started_at)) * 1000,
        assertions_passed = p_assertions_passed,
        assertions_failed = p_assertions_failed,
        error_details = p_error_details
    WHERE session_id = p_session_id;
END;
$$ LANGUAGE plpgsql;

-- Log test database initialization
DO $$
BEGIN
    RAISE NOTICE 'DroidBotX test database initialization completed successfully';
    RAISE NOTICE 'Database: droidbotx_test_db';
    RAISE NOTICE 'User: droidbotx_test_user';
    RAISE NOTICE 'Schemas: droidbotx_test, test_apps, public';
    RAISE NOTICE 'Test data: 3 users, 3 products, 3 tasks';
END $$;

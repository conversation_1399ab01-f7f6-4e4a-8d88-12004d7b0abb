import { BaseAgent, AgentTask, AgentResult } from '../core/BaseAgent';
import { TechnicalSpecification } from './PlanningAgent';
export interface BusinessLogicResult {
    technicalSpec: TechnicalSpecification;
    projectPath: string;
    domainAPIs: {
        [filePath: string]: string;
    };
    databaseSchema: {
        tables: any[];
        indexes: string[];
        seedData: string[];
        migrations: string[];
        initScript: string;
    };
    applicationFlow: {
        routes: any[];
        navigation: any[];
        appComponent: string;
        dashboardComponent: string;
        sidebarComponent: string;
    };
    enhancedComponents: {
        [filePath: string]: string;
    };
    businessServices: {
        [filePath: string]: string;
    };
    dataModels: {
        [filePath: string]: string;
    };
    businessLogic: {
        domainAPIs: {
            [filePath: string]: string;
        };
        databaseSchema: {
            tables: any[];
            indexes: string[];
            seedData: string[];
            migrations: string[];
            initScript: string;
        };
        applicationFlow: {
            routes: any[];
            navigation: any[];
            appComponent: string;
            dashboardComponent: string;
            sidebarComponent: string;
        };
    };
}
export declare class BusinessLogicAgent extends BaseAgent {
    private domainAPIGenerator;
    private databaseSchemaGenerator;
    private applicationFlowGenerator;
    constructor();
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    private extractComponentNames;
    private generateEnhancedComponents;
    private generatePOSTerminalComponent;
    private generateInventoryDashboardComponent;
    private generateCustomerManagerComponent;
    private generateWarrantyTrackerComponent;
    private generateSalesReportsComponent;
    private extractBusinessServices;
    private extractDataModels;
    private analyzeDomainRequirements;
    private generateFallbackDomainAnalysis;
    private extractEntitiesFromRequirements;
    private extractWorkflowsFromRequirements;
    private extractUserRolesFromRequirements;
    private generateAPIEndpointsFromEntities;
    private extractStepsFromRequirement;
    private getDefaultAttributesForEntity;
    private capitalizeFirst;
    /**
     * Write generated code to disk immediately after generation
     */
    private writeGeneratedCodeToDisk;
}
//# sourceMappingURL=BusinessLogicAgent.d.ts.map
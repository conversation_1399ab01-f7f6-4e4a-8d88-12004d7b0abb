"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessLogicAgent = void 0;
const BaseAgent_1 = require("../core/BaseAgent");
const DomainAPIGenerator_1 = require("../generators/DomainAPIGenerator");
const DatabaseSchemaGenerator_1 = require("../generators/DatabaseSchemaGenerator");
const ApplicationFlowGenerator_1 = require("../generators/ApplicationFlowGenerator");
const ContractFirstAPIGenerator_1 = require("../core/ContractFirstAPIGenerator");
const CrossLayerIntegrationFramework_1 = require("../core/CrossLayerIntegrationFramework");
const AdvancedQualityGatesSystem_1 = require("../core/AdvancedQualityGatesSystem");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class BusinessLogicAgent extends BaseAgent_1.BaseAgent {
    constructor() {
        super('BusinessLogicAgent', 'Generates domain-specific business logic, APIs, database schemas, and application flow integration', `You are a senior full-stack developer and domain expert responsible for implementing complete business logic for applications.

Your role is to:
1. Analyze technical specifications and requirements to identify business domains
2. Generate functional APIs with proper business logic, not just CRUD operations
3. Design comprehensive database schemas with proper relationships and constraints
4. Create complete application flows that integrate all components seamlessly
5. Implement actual business logic in components, replacing placeholders with functional code
6. Ensure all generated code follows best practices and is production-ready

You must generate:
- Complete REST APIs with business logic, validation, and error handling
- Database schemas with proper relationships, indexes, and seed data
- Application routing and navigation that connects all components
- Enhanced components with actual functionality, not placeholders
- Business services that implement domain-specific operations
- Data models with proper TypeScript interfaces

Focus on creating functional, production-ready business applications that work immediately without additional development.`);
        this.domainAPIGenerator = new DomainAPIGenerator_1.DomainAPIGenerator();
        this.databaseSchemaGenerator = new DatabaseSchemaGenerator_1.DatabaseSchemaGenerator();
        this.applicationFlowGenerator = new ApplicationFlowGenerator_1.ApplicationFlowGenerator();
        this.contractFirstAPIGenerator = new ContractFirstAPIGenerator_1.ContractFirstAPIGenerator();
        this.integrationFramework = new CrossLayerIntegrationFramework_1.CrossLayerIntegrationFramework();
        this.qualityGatesSystem = new AdvancedQualityGatesSystem_1.AdvancedQualityGatesSystem();
    }
    canHandle(task) {
        return task.type === 'business_logic';
    }
    async execute(task) {
        try {
            this.logInfo('Starting business logic generation phase', { taskId: task.id });
            // Extract data from previous phase result - handle multiple parameter formats
            const previousResult = task.parameters.previousResult;
            const technicalSpecFromParam = task.parameters.technicalSpec;
            const planResult = task.parameters.planResult;
            // Try multiple sources for technical specification
            const technicalSpec = technicalSpecFromParam ||
                previousResult?.data ||
                previousResult ||
                planResult?.data ||
                planResult;
            if (!technicalSpec) {
                throw new Error('Technical specification not provided from planning phase');
            }
            const requirements = task.parameters.requirements ||
                technicalSpec.requirements ||
                [];
            if (!requirements || requirements.length === 0) {
                throw new Error('Requirements not provided for business logic generation');
            }
            // Perform domain analysis from requirements and technical specification
            this.logInfo('Analyzing domain requirements', {
                taskId: task.id,
                requirementsCount: requirements.length,
                projectName: technicalSpec.projectName,
                businessDomain: technicalSpec.businessDomain?.name
            });
            const domainAnalysis = await this.analyzeDomainRequirements(requirements, technicalSpec);
            // Generate domain-specific APIs using enhanced analysis
            this.logInfo('Generating domain-specific APIs', {
                taskId: task.id,
                entities: domainAnalysis.entities.length,
                workflows: domainAnalysis.workflows.length
            });
            const domainAPIs = await this.domainAPIGenerator.generateAPIsFromRequirements(requirements, technicalSpec, domainAnalysis) || {};
            // Generate database schema
            this.logInfo('Generating database schema', { taskId: task.id });
            const databaseSchema = await this.databaseSchemaGenerator.generateSchemaFromRequirements(requirements, technicalSpec);
            const initScript = this.databaseSchemaGenerator.generateDatabaseInitScript(databaseSchema);
            // Generate application flow
            this.logInfo('Generating application flow', { taskId: task.id });
            const generatedComponents = this.extractComponentNames(technicalSpec);
            const applicationFlow = this.applicationFlowGenerator.generateApplicationFlow(requirements, technicalSpec, generatedComponents);
            const sidebarComponent = this.applicationFlowGenerator.generateSidebarComponent(applicationFlow.navigation);
            // Generate enhanced components
            this.logInfo('Generating enhanced components', { taskId: task.id });
            const enhancedComponents = await this.generateEnhancedComponents(requirements, technicalSpec) || {};
            // Generate OpenAPI specification for contract-first development
            this.logInfo('Generating OpenAPI specification', { taskId: task.id });
            const tempBusinessLogic = {
                projectName: technicalSpec.projectName,
                description: technicalSpec.description,
                domainAPIs: domainAPIs || {},
                databaseSchema: {
                    ...databaseSchema,
                    initScript
                }
            };
            const openAPISpec = await this.contractFirstAPIGenerator.generateOpenAPISpec(tempBusinessLogic);
            // Create project path for consistency with CodingAgent
            const projectPath = path.join(process.cwd(), 'generated-projects', technicalSpec.projectName);
            // Prepare business logic result with safety checks
            const businessLogicResult = {
                technicalSpec: technicalSpec, // Pass through technical specification
                projectPath: projectPath, // Add project path for subsequent phases
                domainAPIs: domainAPIs || {},
                databaseSchema: {
                    ...databaseSchema,
                    initScript
                },
                applicationFlow: {
                    ...applicationFlow,
                    sidebarComponent
                },
                openAPISpec: openAPISpec, // Add OpenAPI specification for contract-first development
                enhancedComponents: enhancedComponents || {},
                businessServices: this.extractBusinessServices(domainAPIs || {}),
                dataModels: this.extractDataModels(domainAPIs || {}),
                businessLogic: {
                    domainAPIs: domainAPIs || {},
                    databaseSchema: {
                        ...databaseSchema,
                        initScript
                    },
                    applicationFlow: {
                        ...applicationFlow,
                        sidebarComponent
                    }
                }
            };
            // Write generated code to disk immediately
            await this.writeGeneratedCodeToDisk(domainAPIs, databaseSchema, technicalSpec);
            // Register business logic layer context for integration
            const businessLayerContext = {
                layer: 'business',
                data: businessLogicResult,
                metadata: {
                    timestamp: new Date(),
                    version: '1.0.0',
                    dependencies: ['database'],
                    contracts: {
                        openAPISpec: openAPISpec,
                        domainAPIs: domainAPIs
                    }
                }
            };
            this.integrationFramework.registerLayerContext(businessLayerContext);
            // Execute quality validation for business logic phase
            const qualityReport = await this.qualityGatesSystem.executeQualityValidation(businessLogicResult, 'business_logic');
            this.logInfo('Business logic generation completed successfully', {
                taskId: task.id,
                apisGenerated: Object.keys(domainAPIs).length,
                tablesGenerated: databaseSchema.tables.length,
                routesGenerated: applicationFlow.routes.length,
                componentsEnhanced: Object.keys(enhancedComponents).length,
                qualityScore: qualityReport.overallScore,
                qualityPassed: qualityReport.overallPassed
            });
            return {
                success: true,
                data: businessLogicResult,
                metadata: {
                    phase: 'business_logic',
                    timestamp: new Date().toISOString(),
                    domainAnalysis: domainAnalysis, // Pass domain analysis to next phase
                    technicalSpec: technicalSpec, // Pass technical spec to next phase
                    metrics: {
                        apisGenerated: Object.keys(domainAPIs).length,
                        tablesGenerated: databaseSchema.tables.length,
                        routesGenerated: applicationFlow.routes.length,
                        componentsEnhanced: Object.keys(enhancedComponents).length
                    }
                },
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown business logic generation error';
            this.logError('Business logic generation failed', { taskId: task.id, error: errorMessage });
            return {
                success: false,
                error: errorMessage,
            };
        }
    }
    extractComponentNames(spec) {
        const components = [];
        // Extract from frontend components list
        if (spec.architecture?.frontend?.components) {
            components.push(...spec.architecture.frontend.components);
        }
        // Extract from features
        if (spec.features) {
            spec.features.forEach(feature => {
                const featureLower = feature.toLowerCase();
                if (featureLower.includes('barcode') || featureLower.includes('scanner')) {
                    components.push('ProductScanner', 'BarcodeScanner');
                }
                if ((featureLower.includes('pos terminal') || featureLower.includes('point of sale')) &&
                    !featureLower.includes('ecommerce') && !featureLower.includes('e-commerce') &&
                    !featureLower.includes('online')) {
                    components.push('POSTerminal');
                }
                if (featureLower.includes('inventory')) {
                    components.push('InventoryManager', 'StockAlerts');
                }
                if (featureLower.includes('customer')) {
                    components.push('CustomerManager');
                }
                if (featureLower.includes('payment')) {
                    components.push('PaymentProcessor');
                }
                if (featureLower.includes('warranty')) {
                    components.push('WarrantyTracker');
                }
                if (featureLower.includes('report')) {
                    components.push('SalesReports', 'AnalyticsDashboard');
                }
            });
        }
        return [...new Set(components)]; // Remove duplicates
    }
    async generateEnhancedComponents(requirements, spec) {
        const enhancedComponents = {};
        // Generate enhanced POS Terminal if needed (only for physical retail, not e-commerce)
        const hasPhysicalPOS = requirements.some(req => {
            const reqLower = req.toLowerCase();
            return (reqLower.includes('pos terminal') || reqLower.includes('point of sale')) &&
                !reqLower.includes('ecommerce') && !reqLower.includes('e-commerce') &&
                !reqLower.includes('online');
        });
        if (hasPhysicalPOS) {
            enhancedComponents['frontend/src/components/POS/POSTerminal.tsx'] = this.generatePOSTerminalComponent();
            enhancedComponents['frontend/src/components/Inventory/InventoryDashboard.tsx'] = this.generateInventoryDashboardComponent();
        }
        // Generate enhanced Customer Manager if needed
        if (requirements.some(req => req.toLowerCase().includes('customer'))) {
            enhancedComponents['frontend/src/components/Customers/CustomerManager.tsx'] = this.generateCustomerManagerComponent();
        }
        // Generate enhanced Warranty Tracker if needed
        if (requirements.some(req => req.toLowerCase().includes('warranty'))) {
            enhancedComponents['frontend/src/components/Warranty/WarrantyTracker.tsx'] = this.generateWarrantyTrackerComponent();
        }
        // Generate enhanced Reports if needed
        if (requirements.some(req => req.toLowerCase().includes('report') || req.toLowerCase().includes('analytics'))) {
            enhancedComponents['frontend/src/components/reports/SalesReports.tsx'] = this.generateSalesReportsComponent();
        }
        return enhancedComponents;
    }
    generatePOSTerminalComponent() {
        return `import React, { useState, useEffect } from 'react';
import { ProductScanner } from '../scanning/ProductScanner';
import { ShoppingCart } from '../POS/ShoppingCart';
import { PaymentProcessor } from '../payments/PaymentProcessor';
import api from '../../services/api';
import './POSTerminal.css';

interface Product {
  id: string;
  name: string;
  barcode: string;
  price: number;
  category: string;
}

interface CartItem extends Product {
  quantity: number;
  total: number;
}

const POSTerminal: React.FC = () => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [currentCustomer, setCurrentCustomer] = useState<any>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [showScanner, setShowScanner] = useState(false);

  const handleProductFound = (product: Product) => {
    const existingItem = cart.find(item => item.id === product.id);
    
    if (existingItem) {
      setCart(cart.map(item => 
        item.id === product.id 
          ? { ...item, quantity: item.quantity + 1, total: (item.quantity + 1) * item.price }
          : item
      ));
    } else {
      setCart([...cart, { ...product, quantity: 1, total: product.price }]);
    }
    
    setShowScanner(false);
  };

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setCart(cart.filter(item => item.id !== productId));
    } else {
      setCart(cart.map(item => 
        item.id === productId 
          ? { ...item, quantity: newQuantity, total: newQuantity * item.price }
          : item
      ));
    }
  };

  const handleRemoveItem = (productId: string) => {
    setCart(cart.filter(item => item.id !== productId));
  };

  const calculateSubtotal = () => {
    return cart.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTax = (subtotal: number) => {
    return subtotal * 0.08; // 8% tax rate
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax(subtotal);
    return subtotal + tax;
  };

  const handlePaymentComplete = async (paymentData: any) => {
    setIsProcessingPayment(true);
    
    try {
      const saleData = {
        items: cart.map(item => ({
          productId: item.id,
          name: item.name,
          barcode: item.barcode,
          price: item.price,
          quantity: item.quantity,
          total: item.total
        })),
        subtotal: calculateSubtotal(),
        tax: calculateTax(calculateSubtotal()),
        total: calculateTotal(),
        paymentMethod: paymentData.method,
        customerId: currentCustomer?.id
      };

      const response = await api.post('/sales', saleData);
      
      // Clear cart and reset state
      setCart([]);
      setCurrentCustomer(null);
      
      alert(\`Sale completed successfully! Transaction ID: \${response.data.id}\`);
    } catch (error) {
      console.error('Error processing sale:', error);
      alert('Error processing sale. Please try again.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  return (
    <div className="pos-terminal">
      <div className="pos-header">
        <h1>POS Terminal</h1>
        <button 
          className="scan-button"
          onClick={() => setShowScanner(true)}
        >
          Scan Product
        </button>
      </div>

      <div className="pos-content">
        <div className="pos-left">
          {showScanner && (
            <div className="scanner-modal">
              <div className="scanner-content">
                <ProductScanner onProductFound={handleProductFound} />
                <button onClick={() => setShowScanner(false)}>Close Scanner</button>
              </div>
            </div>
          )}
          
          <ShoppingCart
            items={cart}
            onQuantityChange={handleQuantityChange}
            onRemoveItem={handleRemoveItem}
          />
        </div>

        <div className="pos-right">
          <div className="order-summary">
            <h3>Order Summary</h3>
            <div className="summary-line">
              <span>Subtotal:</span>
              <span>\${calculateSubtotal().toFixed(2)}</span>
            </div>
            <div className="summary-line">
              <span>Tax:</span>
              <span>\${calculateTax(calculateSubtotal()).toFixed(2)}</span>
            </div>
            <div className="summary-line total">
              <span>Total:</span>
              <span>\${calculateTotal().toFixed(2)}</span>
            </div>
          </div>

          {cart.length > 0 && (
            <PaymentProcessor
              amount={calculateTotal()}
              onPaymentComplete={handlePaymentComplete}
              disabled={isProcessingPayment}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default POSTerminal;`;
    }
    generateInventoryDashboardComponent() {
        return `import React, { useState, useEffect } from 'react';
import api from '../../services/api';
import './InventoryDashboard.css';

interface InventoryItem {
  id: string;
  product_id: string;
  product_name: string;
  barcode: string;
  quantity: number;
  reorder_point: number;
}

const InventoryDashboard: React.FC = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [lowStockItems, setLowStockItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showLowStockOnly, setShowLowStockOnly] = useState(false);

  useEffect(() => {
    fetchInventory();
    fetchLowStockAlerts();
  }, [currentPage, showLowStockOnly]);

  const fetchInventory = async () => {
    try {
      setLoading(true);
      const response = await api.get('/inventory', {
        params: {
          page: currentPage,
          limit: 20,
          lowStock: showLowStockOnly
        }
      });

      setInventory(response.data.inventory);
      setTotalPages(response.data.totalPages);
    } catch (error) {
      console.error('Error fetching inventory:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchLowStockAlerts = async () => {
    try {
      const response = await api.get('/inventory/alerts');
      setLowStockItems(response.data);
    } catch (error) {
      console.error('Error fetching low stock alerts:', error);
    }
  };

  const handleStockUpdate = async (productId: string, operation: string, quantity: number, reason: string) => {
    try {
      await api.put(\`/inventory/\${productId}\`, {
        quantity,
        operation,
        reason
      });

      fetchInventory();
      fetchLowStockAlerts();
    } catch (error) {
      console.error('Error updating stock:', error);
      alert('Error updating stock. Please try again.');
    }
  };

  const getStockStatus = (item: InventoryItem) => {
    if (item.quantity <= 0) return 'out-of-stock';
    if (item.quantity <= item.reorder_point) return 'low-stock';
    return 'in-stock';
  };

  return (
    <div className="inventory-dashboard">
      <div className="dashboard-header">
        <h1>Inventory Management</h1>
        <div className="header-controls">
          <label>
            <input
              type="checkbox"
              checked={showLowStockOnly}
              onChange={(e) => setShowLowStockOnly(e.target.checked)}
            />
            Show Low Stock Only
          </label>
        </div>
      </div>

      {lowStockItems.length > 0 && (
        <div className="alerts-section">
          <h2>Low Stock Alerts ({lowStockItems.length})</h2>
          <div className="alert-items">
            {lowStockItems.slice(0, 5).map(item => (
              <div key={item.id} className="alert-item">
                <span className="product-name">{item.product_name}</span>
                <span className="stock-level">Stock: {item.quantity}</span>
                <span className="reorder-point">Reorder at: {item.reorder_point}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="inventory-table">
        <table>
          <thead>
            <tr>
              <th>Product</th>
              <th>Barcode</th>
              <th>Current Stock</th>
              <th>Reorder Point</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {inventory.map(item => (
              <tr key={item.id} className={getStockStatus(item)}>
                <td>{item.product_name}</td>
                <td>{item.barcode}</td>
                <td>{item.quantity}</td>
                <td>{item.reorder_point}</td>
                <td>
                  <span className={\`status-badge \${getStockStatus(item)}\`}>
                    {getStockStatus(item).replace('-', ' ').toUpperCase()}
                  </span>
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      onClick={() => {
                        const qty = prompt('Enter quantity to add:');
                        const reason = prompt('Enter reason:');
                        if (qty && reason) {
                          handleStockUpdate(item.product_id, 'add', parseInt(qty), reason);
                        }
                      }}
                    >
                      Add Stock
                    </button>
                    <button
                      onClick={() => {
                        const qty = prompt('Enter quantity to remove:');
                        const reason = prompt('Enter reason:');
                        if (qty && reason) {
                          handleStockUpdate(item.product_id, 'subtract', parseInt(qty), reason);
                        }
                      }}
                    >
                      Remove Stock
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button
          disabled={currentPage === 1}
          onClick={() => setCurrentPage(currentPage - 1)}
        >
          Previous
        </button>
        <span>Page {currentPage} of {totalPages}</span>
        <button
          disabled={currentPage === totalPages}
          onClick={() => setCurrentPage(currentPage + 1)}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default InventoryDashboard;`;
    }
    generateCustomerManagerComponent() {
        return `import React, { useState, useEffect } from 'react';
import api from '../../services/api';
import './CustomerManager.css';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  created_at: string;
}

const CustomerManager: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    email: '',
    phone: '',
    address: ''
  });

  useEffect(() => {
    fetchCustomers();
  }, [currentPage, searchTerm]);

  const fetchCustomers = async () => {
    try {
      const response = await api.get('/customers', {
        params: {
          page: currentPage,
          limit: 20,
          search: searchTerm
        }
      });

      setCustomers(response.data.customers);
      setTotalPages(response.data.totalPages);
    } catch (error) {
      console.error('Error fetching customers:', error);
    }
  };

  const handleAddCustomer = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await api.post('/customers', newCustomer);
      setCustomers([response.data, ...customers]);
      setNewCustomer({ name: '', email: '', phone: '', address: '' });
      setShowAddForm(false);
    } catch (error) {
      console.error('Error adding customer:', error);
      alert('Error adding customer. Please try again.');
    }
  };

  const viewCustomerHistory = async (customer: Customer) => {
    setSelectedCustomer(customer);
    // In a real app, this would navigate to a detailed view
    alert(\`Viewing history for \${customer.name}\`);
  };

  return (
    <div className="customer-manager">
      <div className="header">
        <h1>Customer Management</h1>
        <button onClick={() => setShowAddForm(true)}>Add New Customer</button>
      </div>

      <div className="search-section">
        <input
          type="text"
          placeholder="Search customers..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {showAddForm && (
        <div className="add-form-modal">
          <div className="modal-content">
            <h2>Add New Customer</h2>
            <form onSubmit={handleAddCustomer}>
              <input
                type="text"
                placeholder="Name"
                value={newCustomer.name}
                onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                required
              />
              <input
                type="email"
                placeholder="Email"
                value={newCustomer.email}
                onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                required
              />
              <input
                type="tel"
                placeholder="Phone"
                value={newCustomer.phone}
                onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
              />
              <textarea
                placeholder="Address"
                value={newCustomer.address}
                onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
              />
              <div className="form-buttons">
                <button type="submit">Add Customer</button>
                <button type="button" onClick={() => setShowAddForm(false)}>Cancel</button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="customers-table">
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Address</th>
              <th>Member Since</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {customers.map(customer => (
              <tr key={customer.id}>
                <td>{customer.name}</td>
                <td>{customer.email}</td>
                <td>{customer.phone || 'N/A'}</td>
                <td>{customer.address || 'N/A'}</td>
                <td>{new Date(customer.created_at).toLocaleDateString()}</td>
                <td>
                  <button onClick={() => viewCustomerHistory(customer)}>
                    View History
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button
          disabled={currentPage === 1}
          onClick={() => setCurrentPage(currentPage - 1)}
        >
          Previous
        </button>
        <span>Page {currentPage} of {totalPages}</span>
        <button
          disabled={currentPage === totalPages}
          onClick={() => setCurrentPage(currentPage + 1)}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default CustomerManager;`;
    }
    generateWarrantyTrackerComponent() {
        return `import React, { useState, useEffect } from 'react';
import api from '../../services/api';
import './WarrantyTracker.css';

interface WarrantyClaim {
  id: string;
  product_id: string;
  customer_id: string;
  issue_description: string;
  status: 'pending' | 'approved' | 'rejected' | 'resolved';
  created_at: string;
  resolved_at?: string;
}

const WarrantyTracker: React.FC = () => {
  const [claims, setClaims] = useState<WarrantyClaim[]>([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState('');
  const [warrantyInfo, setWarrantyInfo] = useState<any>(null);
  const [newClaim, setNewClaim] = useState({
    productId: '',
    customerId: '',
    issueDescription: ''
  });

  useEffect(() => {
    fetchClaims();
  }, []);

  const fetchClaims = async () => {
    try {
      const response = await api.get('/warranty/claims');
      setClaims(response.data);
    } catch (error) {
      console.error('Error fetching warranty claims:', error);
    }
  };

  const checkWarranty = async (productId: string) => {
    try {
      const response = await api.get(\`/warranties/\${productId}\`);
      setWarrantyInfo(response.data);
      setSelectedProductId(productId);
    } catch (error) {
      console.error('Error checking warranty:', error);
      alert('Error checking warranty information');
    }
  };

  const handleCreateClaim = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await api.post('/warranty/claims', newClaim);
      setClaims([response.data, ...claims]);
      setNewClaim({ productId: '', customerId: '', issueDescription: '' });
      setShowCreateForm(false);
    } catch (error) {
      console.error('Error creating warranty claim:', error);
      alert('Error creating warranty claim. Please try again.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#ffa500';
      case 'approved': return '#008000';
      case 'rejected': return '#ff0000';
      case 'resolved': return '#0000ff';
      default: return '#666666';
    }
  };

  return (
    <div className="warranty-tracker">
      <div className="header">
        <h1>Warranty Management</h1>
        <button onClick={() => setShowCreateForm(true)}>Create New Claim</button>
      </div>

      <div className="warranty-check-section">
        <h2>Check Warranty Status</h2>
        <div className="warranty-check">
          <input
            type="text"
            placeholder="Enter Product ID"
            value={selectedProductId}
            onChange={(e) => setSelectedProductId(e.target.value)}
          />
          <button onClick={() => checkWarranty(selectedProductId)}>
            Check Warranty
          </button>
        </div>

        {warrantyInfo && (
          <div className="warranty-info">
            <h3>Warranty Information</h3>
            <p><strong>Product:</strong> {warrantyInfo.product_name}</p>
            <p><strong>Warranty Period:</strong> {warrantyInfo.warranty_period} months</p>
            <p><strong>Status:</strong> Active</p>
          </div>
        )}
      </div>

      {showCreateForm && (
        <div className="create-form-modal">
          <div className="modal-content">
            <h2>Create Warranty Claim</h2>
            <form onSubmit={handleCreateClaim}>
              <input
                type="text"
                placeholder="Product ID"
                value={newClaim.productId}
                onChange={(e) => setNewClaim({...newClaim, productId: e.target.value})}
                required
              />
              <input
                type="text"
                placeholder="Customer ID"
                value={newClaim.customerId}
                onChange={(e) => setNewClaim({...newClaim, customerId: e.target.value})}
                required
              />
              <textarea
                placeholder="Describe the issue..."
                value={newClaim.issueDescription}
                onChange={(e) => setNewClaim({...newClaim, issueDescription: e.target.value})}
                required
              />
              <div className="form-buttons">
                <button type="submit">Create Claim</button>
                <button type="button" onClick={() => setShowCreateForm(false)}>Cancel</button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="claims-section">
        <h2>Warranty Claims</h2>
        <div className="claims-table">
          <table>
            <thead>
              <tr>
                <th>Claim ID</th>
                <th>Product ID</th>
                <th>Customer ID</th>
                <th>Issue</th>
                <th>Status</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {claims.map(claim => (
                <tr key={claim.id}>
                  <td>{claim.id.substring(0, 8)}...</td>
                  <td>{claim.product_id.substring(0, 8)}...</td>
                  <td>{claim.customer_id.substring(0, 8)}...</td>
                  <td>{claim.issue_description.substring(0, 50)}...</td>
                  <td>
                    <span
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(claim.status) }}
                    >
                      {claim.status.toUpperCase()}
                    </span>
                  </td>
                  <td>{new Date(claim.created_at).toLocaleDateString()}</td>
                  <td>
                    <button>View Details</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default WarrantyTracker;`;
    }
    generateSalesReportsComponent() {
        return `import React, { useState, useEffect } from 'react';
import api from '../../services/api';
import './SalesReports.css';

interface SalesData {
  period: string;
  transaction_count: number;
  total_sales: number;
  average_sale: number;
}

const SalesReports: React.FC = () => {
  const [salesData, setSalesData] = useState<SalesData[]>([]);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [groupBy, setGroupBy] = useState('day');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSalesReport();
  }, [dateRange, groupBy]);

  const fetchSalesReport = async () => {
    setLoading(true);
    try {
      const response = await api.get('/reports/sales', {
        params: {
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
          groupBy
        }
      });
      setSalesData(response.data);
    } catch (error) {
      console.error('Error fetching sales report:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateTotals = () => {
    return salesData.reduce((totals, item) => ({
      totalTransactions: totals.totalTransactions + item.transaction_count,
      totalSales: totals.totalSales + item.total_sales,
      averageSale: salesData.length > 0 ? totals.totalSales / totals.totalTransactions : 0
    }), { totalTransactions: 0, totalSales: 0, averageSale: 0 });
  };

  const totals = calculateTotals();

  return (
    <div className="sales-reports">
      <h1>Sales Reports & Analytics</h1>

      <div className="report-controls">
        <div className="date-controls">
          <label>
            Start Date:
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
            />
          </label>
          <label>
            End Date:
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
            />
          </label>
        </div>

        <div className="group-controls">
          <label>
            Group By:
            <select value={groupBy} onChange={(e) => setGroupBy(e.target.value)}>
              <option value="day">Day</option>
              <option value="week">Week</option>
              <option value="month">Month</option>
            </select>
          </label>
        </div>
      </div>

      <div className="summary-cards">
        <div className="summary-card">
          <h3>Total Transactions</h3>
          <p className="metric-value">{totals.totalTransactions}</p>
        </div>
        <div className="summary-card">
          <h3>Total Sales</h3>
          <p className="metric-value">\${totals.totalSales.toFixed(2)}</p>
        </div>
        <div className="summary-card">
          <h3>Average Sale</h3>
          <p className="metric-value">\${totals.averageSale.toFixed(2)}</p>
        </div>
      </div>

      {loading ? (
        <div className="loading">Loading report data...</div>
      ) : (
        <div className="report-table">
          <table>
            <thead>
              <tr>
                <th>Period</th>
                <th>Transactions</th>
                <th>Total Sales</th>
                <th>Average Sale</th>
              </tr>
            </thead>
            <tbody>
              {salesData.map((item, index) => (
                <tr key={index}>
                  <td>{new Date(item.period).toLocaleDateString()}</td>
                  <td>{item.transaction_count}</td>
                  <td>\${item.total_sales.toFixed(2)}</td>
                  <td>\${item.average_sale.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default SalesReports;`;
    }
    extractBusinessServices(domainAPIs) {
        const services = {};
        Object.entries(domainAPIs).forEach(([filePath, content]) => {
            if (filePath.includes('services/')) {
                services[filePath] = content;
            }
        });
        return services;
    }
    extractDataModels(domainAPIs) {
        const models = {};
        Object.entries(domainAPIs).forEach(([filePath, content]) => {
            if (filePath.includes('models/')) {
                models[filePath] = content;
            }
        });
        return models;
    }
    async analyzeDomainRequirements(requirements, technicalSpec) {
        this.logInfo('Performing AI-driven domain analysis', {
            requirementsCount: requirements.length,
            projectName: technicalSpec.projectName
        });
        const analysisPrompt = `Analyze the following requirements and technical specification to extract comprehensive domain information:

PROJECT: ${technicalSpec.projectName}
DESCRIPTION: ${technicalSpec.description}
BUSINESS DOMAIN: ${technicalSpec.businessDomain?.name || 'Not specified'}

REQUIREMENTS:
${requirements.map((req, index) => `${index + 1}. ${req}`).join('\n')}

TECHNICAL SPECIFICATION:
${JSON.stringify(technicalSpec, null, 2)}

Perform a comprehensive domain analysis and return ONLY valid JSON in this exact format:

{
  "entities": [
    {
      "name": "EntityName",
      "description": "Entity description",
      "attributes": ["attribute1", "attribute2"],
      "relationships": ["relatedEntity1", "relatedEntity2"],
      "operations": ["create", "read", "update", "delete", "customOperation"]
    }
  ],
  "workflows": [
    {
      "name": "WorkflowName",
      "description": "Workflow description",
      "steps": ["step1", "step2", "step3"],
      "entities": ["Entity1", "Entity2"],
      "triggers": ["userAction", "systemEvent"]
    }
  ],
  "userRoles": [
    {
      "name": "RoleName",
      "description": "Role description",
      "permissions": ["permission1", "permission2"],
      "workflows": ["workflow1", "workflow2"]
    }
  ],
  "businessRules": [
    {
      "name": "RuleName",
      "description": "Business rule description",
      "conditions": ["condition1", "condition2"],
      "actions": ["action1", "action2"],
      "entities": ["Entity1", "Entity2"]
    }
  ],
  "apiEndpoints": [
    {
      "path": "/api/endpoint",
      "method": "GET|POST|PUT|DELETE",
      "description": "Endpoint description",
      "entity": "EntityName",
      "operation": "operationName"
    }
  ]
}

CRITICAL REQUIREMENTS:
- Return ONLY valid JSON, no markdown or explanations
- Extract real business entities from the requirements
- Identify actual workflows and user interactions
- Define meaningful user roles and permissions
- Create comprehensive API endpoints for all operations
- Ensure all entities have proper CRUD and business operations`;
        try {
            const response = await this.generateSingleLLMResponse(analysisPrompt, {
                temperature: 0.3,
                maxTokens: 4000
            });
            // Parse the domain analysis response
            const domainAnalysis = JSON.parse(response);
            this.logInfo('Domain analysis completed successfully', {
                entities: domainAnalysis.entities?.length || 0,
                workflows: domainAnalysis.workflows?.length || 0,
                userRoles: domainAnalysis.userRoles?.length || 0,
                businessRules: domainAnalysis.businessRules?.length || 0,
                apiEndpoints: domainAnalysis.apiEndpoints?.length || 0
            });
            return domainAnalysis;
        }
        catch (error) {
            this.logError('Domain analysis failed, using fallback analysis', { error: error instanceof Error ? error.message : 'Unknown error' });
            // Fallback domain analysis based on requirements
            return this.generateFallbackDomainAnalysis(requirements, technicalSpec);
        }
    }
    generateFallbackDomainAnalysis(requirements, technicalSpec) {
        this.logInfo('Generating fallback domain analysis');
        // Extract entities from requirements using pattern matching
        const entities = this.extractEntitiesFromRequirements(requirements, technicalSpec);
        const workflows = this.extractWorkflowsFromRequirements(requirements);
        const userRoles = this.extractUserRolesFromRequirements(requirements);
        return {
            entities,
            workflows,
            userRoles,
            businessRules: [
                {
                    name: "Authentication Required",
                    description: "Users must be authenticated to access protected resources",
                    conditions: ["user not authenticated"],
                    actions: ["redirect to login"],
                    entities: ["User"]
                }
            ],
            apiEndpoints: this.generateAPIEndpointsFromEntities(entities)
        };
    }
    extractEntitiesFromRequirements(requirements, technicalSpec) {
        const entityPatterns = [
            /user|customer|client|account/i,
            /product|item|catalog|inventory/i,
            /order|purchase|transaction|payment/i,
            /category|tag|classification/i,
            /admin|administrator|manager/i,
            /cart|basket|shopping/i,
            /review|rating|feedback/i,
            /report|analytics|dashboard/i
        ];
        const entities = [];
        const foundEntities = new Set();
        requirements.forEach(req => {
            entityPatterns.forEach(pattern => {
                const match = req.match(pattern);
                if (match && !foundEntities.has(match[0].toLowerCase())) {
                    const entityName = this.capitalizeFirst(match[0]);
                    foundEntities.add(match[0].toLowerCase());
                    entities.push({
                        name: entityName,
                        description: `${entityName} entity for ${technicalSpec.projectName}`,
                        attributes: this.getDefaultAttributesForEntity(entityName),
                        relationships: [],
                        operations: ["create", "read", "update", "delete", "list"]
                    });
                }
            });
        });
        // Ensure at least basic entities exist
        if (entities.length === 0) {
            entities.push({
                name: "User",
                description: "User entity for authentication and management",
                attributes: ["id", "email", "password", "name", "role"],
                relationships: [],
                operations: ["create", "read", "update", "delete", "authenticate"]
            });
        }
        return entities;
    }
    extractWorkflowsFromRequirements(requirements) {
        const workflows = [];
        requirements.forEach((req, index) => {
            workflows.push({
                name: `Workflow${index + 1}`,
                description: req,
                steps: this.extractStepsFromRequirement(req),
                entities: ["User"],
                triggers: ["user action"]
            });
        });
        return workflows;
    }
    extractUserRolesFromRequirements(requirements) {
        const roles = [
            {
                name: "User",
                description: "Regular application user",
                permissions: ["read", "create_own", "update_own"],
                workflows: ["authentication", "basic_operations"]
            }
        ];
        // Check for admin requirements
        const hasAdmin = requirements.some(req => req.toLowerCase().includes('admin') ||
            req.toLowerCase().includes('manage') ||
            req.toLowerCase().includes('dashboard'));
        if (hasAdmin) {
            roles.push({
                name: "Admin",
                description: "Administrator with full access",
                permissions: ["read", "create", "update", "delete", "manage"],
                workflows: ["administration", "user_management", "system_configuration"]
            });
        }
        return roles;
    }
    generateAPIEndpointsFromEntities(entities) {
        const endpoints = [];
        entities.forEach(entity => {
            const entityPath = entity.name.toLowerCase();
            endpoints.push({
                path: `/api/${entityPath}`,
                method: "GET",
                description: `Get all ${entity.name} records`,
                entity: entity.name,
                operation: "list"
            }, {
                path: `/api/${entityPath}/:id`,
                method: "GET",
                description: `Get ${entity.name} by ID`,
                entity: entity.name,
                operation: "read"
            }, {
                path: `/api/${entityPath}`,
                method: "POST",
                description: `Create new ${entity.name}`,
                entity: entity.name,
                operation: "create"
            }, {
                path: `/api/${entityPath}/:id`,
                method: "PUT",
                description: `Update ${entity.name} by ID`,
                entity: entity.name,
                operation: "update"
            }, {
                path: `/api/${entityPath}/:id`,
                method: "DELETE",
                description: `Delete ${entity.name} by ID`,
                entity: entity.name,
                operation: "delete"
            });
        });
        return endpoints;
    }
    extractStepsFromRequirement(requirement) {
        // Simple step extraction based on common patterns
        if (requirement.toLowerCase().includes('login') || requirement.toLowerCase().includes('auth')) {
            return ["validate credentials", "generate token", "return user data"];
        }
        if (requirement.toLowerCase().includes('create') || requirement.toLowerCase().includes('add')) {
            return ["validate input", "save to database", "return created record"];
        }
        if (requirement.toLowerCase().includes('update') || requirement.toLowerCase().includes('edit')) {
            return ["validate input", "check permissions", "update database", "return updated record"];
        }
        if (requirement.toLowerCase().includes('delete') || requirement.toLowerCase().includes('remove')) {
            return ["check permissions", "validate existence", "delete from database", "return confirmation"];
        }
        return ["process request", "validate data", "execute operation", "return result"];
    }
    getDefaultAttributesForEntity(entityName) {
        const commonAttributes = ["id", "createdAt", "updatedAt"];
        switch (entityName.toLowerCase()) {
            case 'user':
            case 'customer':
                return [...commonAttributes, "email", "password", "name", "role"];
            case 'product':
            case 'item':
                return [...commonAttributes, "name", "description", "price", "category"];
            case 'order':
            case 'purchase':
                return [...commonAttributes, "userId", "total", "status", "items"];
            case 'category':
                return [...commonAttributes, "name", "description", "parentId"];
            default:
                return [...commonAttributes, "name", "description"];
        }
    }
    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }
    /**
     * Write generated code to disk immediately after generation
     */
    async writeGeneratedCodeToDisk(domainAPIs, databaseSchema, technicalSpec) {
        try {
            // Create project directory
            const projectPath = path.join(process.cwd(), 'generated-projects', technicalSpec.projectName);
            // Ensure project directory exists
            if (!fs.existsSync(projectPath)) {
                fs.mkdirSync(projectPath, { recursive: true });
            }
            // Create backend directory structure
            const backendPath = path.join(projectPath, 'backend', 'src');
            const routesPath = path.join(backendPath, 'routes');
            const servicesPath = path.join(backendPath, 'services');
            const modelsPath = path.join(backendPath, 'models');
            const databasePath = path.join(projectPath, 'database');
            // Create directories
            [backendPath, routesPath, servicesPath, modelsPath, databasePath].forEach(dir => {
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
            });
            // Write domain API files
            let filesWritten = 0;
            for (const [filePath, content] of Object.entries(domainAPIs)) {
                let fullPath;
                // Determine the correct directory based on file type
                if (filePath.includes('routes') || filePath.endsWith('Routes.ts')) {
                    fullPath = path.join(routesPath, path.basename(filePath));
                }
                else if (filePath.includes('services') || filePath.endsWith('Service.ts')) {
                    fullPath = path.join(servicesPath, path.basename(filePath));
                }
                else if (filePath.includes('models') || filePath.endsWith('Model.ts')) {
                    fullPath = path.join(modelsPath, path.basename(filePath));
                }
                else {
                    // Default to backend src directory
                    fullPath = path.join(backendPath, path.basename(filePath));
                }
                // Write file to disk
                fs.writeFileSync(fullPath, content, 'utf8');
                filesWritten++;
                this.logInfo(`Written file: ${path.basename(filePath)}`, {
                    path: fullPath,
                    size: content.length
                });
            }
            // Write database schema if available
            if (databaseSchema && databaseSchema.initScript) {
                const schemaPath = path.join(databasePath, 'schema.sql');
                fs.writeFileSync(schemaPath, databaseSchema.initScript, 'utf8');
                filesWritten++;
                this.logInfo('Written database schema', {
                    path: schemaPath,
                    tables: databaseSchema.tables?.length || 0
                });
            }
            this.logInfo('Successfully wrote generated code to disk', {
                projectPath,
                filesWritten,
                totalAPIs: Object.keys(domainAPIs).length
            });
        }
        catch (error) {
            this.logError('Failed to write generated code to disk', {
                error: error instanceof Error ? error.message : String(error),
                projectName: technicalSpec.projectName
            });
            // Don't throw error - continue execution even if file writing fails
        }
    }
}
exports.BusinessLogicAgent = BusinessLogicAgent;
//# sourceMappingURL=BusinessLogicAgent.js.map
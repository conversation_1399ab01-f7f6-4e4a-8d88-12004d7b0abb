import { BaseAgent, AgentTask, AgentResult } from '../core/BaseAgent';
export interface GeneratedCode {
    projectPath: string;
    files: {
        [filePath: string]: string;
    };
    packageJsons: {
        [packagePath: string]: any;
    };
    scripts: {
        [scriptName: string]: string;
    };
    documentation: string;
}
export declare class CodingAgent extends BaseAgent {
    private aiCodeGenerator;
    private importExportManager;
    private advancedImportManager;
    private databaseIntegration;
    private serviceStandardization;
    private performanceOptimization;
    constructor();
    canHandle(task: AgentTask): boolean;
    /**
     * Apply Phase 3 optimizations to generated code
     */
    private applyPhase3Optimizations;
    /**
     * Generate CREATE TABLE SQL for database table
     */
    private generateCreateTableSQL;
    execute(task: AgentTask): Promise<AgentResult>;
    private generateAllCodeWithAI;
    private generateDatabaseSchemaWithAI;
    private generateBackendCodeWithAI;
    private generateFrontendCodeWithAI;
    private generateTestsWithAI;
    private generateConfigurationFiles;
    private generateDocumentationWithAI;
    private generateServerFileWithAI;
    private generateAppComponentWithAI;
    private generateDashboardComponentWithAI;
    private generatePackageJsonWithAI;
    private generateDockerComposeWithAI;
    private generateEnvExampleWithAI;
    private generateReadmeWithAI;
    private cleanJsonResponse;
    private generateApplicationFlowWithAI;
    private generateSidebarComponentWithAI;
    private createProjectStructure;
    private generateAllCodeWithBusinessLogic;
    private generateBusinessLogic;
    private generateEnhancedFrontendCode;
    private generateEnhancedBackendCode;
    private generateEnhancedConfigFiles;
    private extractRouteFilesFromSpec;
    private canGenerateRouteForAPIName;
    private shouldGenerateProductRoutes;
    private shouldGenerateOrderRoutes;
    /**
     * SecurityAgent-style defensive server file generation
     * Uses incremental enhancement instead of bulk generation
     */
    private generateOrUpdateServerFile;
    private enhanceExistingServerFile;
    private generateNewServerFile;
    private validateAndFilterRoutes;
    /**
     * SecurityAgent-style defensive route file generation
     */
    private generateRouteFilesSafely;
    private generateRouteFileContent;
    /**
     * SecurityAgent-style defensive middleware generation
     */
    private generateEssentialMiddlewareSafely;
    private generateAuthMiddleware;
    private generateErrorHandler;
    private generateLogger;
    private generateDatabaseConfigWithSchema;
    private generateTableDefinitionsFromRoutes;
    private extractComponentNames;
    private extractBusinessServices;
    private extractDataModels;
    private generateFrontendCode;
    private generateBackendCode;
    private generateConfigFiles;
    private generateFrontendPackageJson;
    private generateBackendPackageJson;
    private generateSetupScript;
    private generateStartScript;
    private generateDocumentation;
    private writeFilesToDisk;
    /**
     * Validate TypeScript syntax and fix common issues
     */
    private validateAndFixTypeScriptSyntax;
    /**
     * Check for basic syntax errors without creating a full program
     */
    private checkBasicSyntaxErrors;
    /**
     * Fix missing closing braces in code
     */
    private fixMissingClosingBraces;
    /**
     * Fix incomplete function declarations
     */
    private fixIncompleteFunctions;
    /**
     * Fix missing semicolons
     */
    private fixMissingSemicolons;
    /**
     * Fix unclosed string literals
     */
    private fixUnclosedStrings;
    /**
     * Fix missing imports based on file content and path
     */
    private fixMissingImports;
    /**
     * Fix import case consistency issues
     */
    private fixImportCaseConsistency;
    /**
     * Scan file content and register imports/exports with the ImportExportManager
     */
    private scanAndRegisterImportsExports;
    private generateDomainComponentsWithValidation;
    /**
     * Repair malformed import statements caused by destructive auto-fixes
     */
    private repairMalformedImports;
    /**
     * Remove destructive artifacts from previous auto-fixes
     */
    private removeDestructiveArtifacts;
    private generateDomainComponents;
    private mapFeatureToComponents;
    private generateBarcodeScannerComponent;
    private generateProductScannerComponent;
    private generatePOSTerminalComponent;
    private generateShoppingCartComponent;
    private generatePaymentProcessorComponent;
    private generatePaymentMethodsComponent;
    private generateInventoryManagerComponent;
    private generateStockAlertsComponent;
    private generateCustomerManagerComponent;
    private generateCustomerHistoryComponent;
    private generateSalesReportsComponent;
    private generateAnalyticsDashboardComponent;
    private generateWarrantyTrackerComponent;
    private generateWarrantyClaimsComponent;
    private generateSupplierManagerComponent;
    private generateOrderManagementComponent;
    private generateTodoRoutes;
    /**
     * Generate docker-compose.yml for the application
     */
    private generateDockerCompose;
    /**
     * Generate essential files that are critical for application functionality
     */
    private generateEssentialFiles;
    /**
     * Generate TypeScript configuration
     */
    private generateTsConfig;
    /**
     * Generate backend index.ts entry point
     */
    private generateBackendIndex;
    /**
     * Generate Jest configuration for ES modules
     */
    private generateJestConfig;
    /**
     * Generate frontend index.tsx
     */
    private generateFrontendIndex;
    /**
     * Generate frontend TypeScript configuration
     */
    private generateFrontendTsConfig;
    /**
     * Generate essential React pages that should be in the pages folder
     */
    private generateEssentialReactPages;
    /**
     * Generate Home page component
     */
    private generateHomePage;
    /**
     * Generate Dashboard page component
     */
    private generateDashboardPage;
    /**
     * Generate Login page component
     */
    private generateLoginPage;
    /**
     * Generate NotFound page component
     */
    private generateNotFoundPage;
    /**
     * Validate and resolve file structure conflicts between BusinessLogicAgent and CodingAgent
     */
    private validateAndResolveFileStructureConflicts;
}
//# sourceMappingURL=CodingAgent.d.ts.map
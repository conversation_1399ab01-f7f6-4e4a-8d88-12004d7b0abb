import { BaseAgent } from '../core/BaseAgent';
import { AgentTask, AgentResult } from '../core/BaseAgent';
import { Logger } from '../core/Logger';
import { DatabaseEnvironment } from '../infrastructure/DatabaseManager';
export declare class DatabaseAgent extends BaseAgent {
    private databaseManager;
    private migrationManager;
    private isolatedEnvironments;
    constructor(logger?: Logger);
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    /**
     * SecurityAgent-style defensive database consistency with incremental enhancements
     */
    private ensureDatabaseConsistency;
    /**
     * SecurityAgent-style safe schema analysis with defensive error handling
     */
    private analyzeSchemaRequirementsSafely;
    /**
     * SecurityAgent-style defensive database configuration enhancement
     */
    private enhanceDatabaseConfigurationSafely;
    private createDatabaseConfigSafely;
    private addSSLConfigSafely;
    private addConnectionPoolingSafely;
    private addErrorHandlingSafely;
    /**
     * SecurityAgent-style safe schema generation
     */
    private generateSchemaSafely;
    private enhanceExistingSchemaSafely;
    private createNewSchemaSafely;
    /**
     * SecurityAgent-style safe foreign key creation
     */
    private createForeignKeysSafely;
    /**
     * SecurityAgent-style optional enhancements with graceful degradation
     */
    private applyOptionalEnhancementsSafely;
    private addIndexesSafely;
    private addConstraintsSafely;
    private addMonitoringSafely;
    private generateCompleteSchemaSQL;
    private analyzeSchemaRequirements;
    private extractTablesFromSQL;
    private parseTableColumns;
    private extractPrimaryKey;
    private extractForeignKeys;
    private extractAPIRequirements;
    private extractTableNameFromOperation;
    private determineRequiredTables;
    private generateBasicTableStructure;
    private determineMissingForeignKeys;
    private implementAdvancedConstraints;
    private createOptimizedIndexes;
    private implementDatabaseSecurity;
    private generateComprehensiveMigrationSystem;
    private calculateConsistencyScore;
    private implementBackupAndRecoverySystem;
    private implementDatabaseMonitoring;
    private optimizeDatabasePerformance;
    private enhanceDatabaseConfiguration;
    private enhanceExistingDatabaseConfig;
    private implementConnectionPooling;
    private implementDataValidationLayer;
    private performDatabaseIntegrityCheck;
    private validateSQLSyntax;
    private checkMissingForeignKeyIndexes;
    private autoFixSQLIssues;
    private addMissingIndexes;
    private calculateAdvancedConsistencyScore;
    private generateCompleteSchema;
    private generateTableSQL;
    private generateForeignKeyConstraints;
    private generatePerformanceIndexes;
    private fixIdTypeConsistencies;
    private createForeignKeyRelationships;
    private generateMigrationFiles;
    private addValidationConstraintsAndIndexes;
    private updateDatabaseConfiguration;
    /**
     * Perform enhanced database operations with isolated environment
     */
    private performEnhancedDatabaseOperations;
    /**
     * Generate enhanced database configuration
     */
    private generateEnhancedDatabaseConfig;
    /**
     * Cleanup isolated environment for session
     */
    cleanupSession(sessionId: string): Promise<void>;
    /**
     * Get database environment for session
     */
    getDatabaseEnvironment(sessionId: string): DatabaseEnvironment | null;
}
//# sourceMappingURL=DatabaseAgent.d.ts.map
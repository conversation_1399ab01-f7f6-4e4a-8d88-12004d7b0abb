import { BaseAgent, AgentTask, AgentR<PERSON>ult } from '../core/BaseAgent';
export declare class DebuggerAgent extends BaseAgent {
    private importExportManager;
    constructor();
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    /**
     * SecurityAgent-style defensive static code analysis
     */
    private performStaticCodeAnalysis;
    /**
     * SecurityAgent-style safe TypeScript file discovery
     */
    private findTypeScriptFilesSafely;
    private fallbackTypeScriptFileDiscovery;
    /**
     * SecurityAgent-style safe file analysis
     */
    private analyzeFileForIssuesSafely;
    private basicFileAnalysis;
    private findTypeScriptFiles;
    private analyzeFileForIssues;
    /**
     * Analyze TypeScript syntax issues
     */
    private analyzeTypeScriptSyntax;
    /**
     * Analyze module resolution issues
     */
    private analyzeModuleResolution;
    /**
     * Analyze interface definition issues
     */
    private analyzeInterfaceDefinitions;
    /**
     * Analyze generic type issues
     */
    private analyzeGenericTypes;
    /**
     * Analyze error handling patterns
     */
    private analyzeErrorHandling;
    /**
     * Analyze null safety patterns
     */
    private analyzeNullSafety;
    /**
     * Analyze index signature patterns
     */
    private analyzeIndexSignatures;
    /**
     * Analyze import/export patterns
     */
    private analyzeImportExport;
    private validateAndResolveDependencies;
    private validatePackageJson;
    private extractImportsFromProject;
    private isExternalPackage;
    private isValidVersion;
    private optimizeTypeScriptConfiguration;
    private optimizeTsConfig;
    private applyPreCompilationFixes;
    private applyEnhancedAutoFixPatterns;
    /**
     * Clean ALL generated files of markdown artifacts and formatting issues
     * This ensures comprehensive code quality across all agents
     */
    private cleanAllGeneratedFiles;
    /**
     * Find all code files that need cleaning
     */
    private findAllCodeFiles;
    /**
     * Clean code artifacts using enhanced patterns
     */
    private cleanCodeArtifacts;
    /**
     * Perform TypeScript compilation validation with auto-fixes
     */
    private performTypeScriptCompilationCheck;
    /**
     * Check TypeScript compilation for a specific directory
     */
    private checkTypeScriptCompilation;
    /**
     * Parse TypeScript error messages
     */
    private parseTypeScriptErrors;
    /**
     * Apply auto-fixes for common TypeScript compilation errors
     */
    private applyTypeScriptAutoFixes;
    /**
     * Apply comprehensive TypeScript fixes for maximum compilation success
     */
    private applyComprehensiveTypeScriptFixes;
    /**
     * Apply basic TypeScript fixes as fallback
     */
    private applyBasicTypeScriptFixes;
    /**
     * Fix module resolution errors
     */
    private fixModuleResolutionErrors;
    /**
     * Fix export member errors
     */
    private fixExportErrors;
    /**
     * Fix type assignment errors
     */
    private fixTypeAssignmentErrors;
    /**
     * Apply general TypeScript fixes
     */
    private applyGeneralTypeScriptFixes;
    /**
     * Intelligent TypeScript fix: Apply comprehensive fixes for AI-generated malformed syntax
     */
    private applyIntelligentTypeScriptFix;
}
//# sourceMappingURL=DebuggerAgent.d.ts.map
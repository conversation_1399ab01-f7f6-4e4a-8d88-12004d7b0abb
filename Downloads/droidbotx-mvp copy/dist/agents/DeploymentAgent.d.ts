import { BaseAgent, AgentTask, AgentResult } from '../core/BaseAgent';
export interface DeploymentConfiguration {
    projectPath: string;
    dockerFiles: {
        [filePath: string]: string;
    };
    deploymentScripts: {
        [scriptName: string]: string;
    };
    documentation: {
        deployment: string;
        docker: string;
        production: string;
    };
    environmentConfigs: {
        [envName: string]: string;
    };
}
export interface EnhancedDeploymentConfiguration {
    projectPath: string;
    dockerConfig: DockerConfiguration;
    cicdConfig: CICDConfiguration;
    environmentConfig: EnvironmentConfiguration;
    monitoringConfig: MonitoringConfiguration;
    performanceConfig: PerformanceConfiguration;
    securityConfig: SecurityConfiguration;
    automationConfig: AutomationConfiguration;
    deploymentReadinessScore: number;
}
export interface DockerConfiguration {
    dockerfiles: {
        [path: string]: string;
    };
    dockerCompose: {
        [env: string]: string;
    };
    nginxConfig: string;
    healthChecks: {
        [service: string]: string;
    };
}
export interface CICDConfiguration {
    githubActions: {
        [workflow: string]: string;
    };
    gitlabCI: string;
    jenkinsfile: string;
    buildScripts: {
        [script: string]: string;
    };
}
export interface EnvironmentConfiguration {
    environments: {
        [env: string]: string;
    };
    secrets: {
        [env: string]: string;
    };
    configMaps: {
        [env: string]: string;
    };
    validation: string;
}
export interface MonitoringConfiguration {
    healthChecks: {
        [service: string]: string;
    };
    metrics: {
        [service: string]: string;
    };
    logging: {
        [service: string]: string;
    };
    alerts: {
        [service: string]: string;
    };
}
export interface PerformanceConfiguration {
    optimization: {
        [service: string]: string;
    };
    caching: {
        [service: string]: string;
    };
    loadBalancing: string;
    scaling: {
        [service: string]: string;
    };
}
export interface SecurityConfiguration {
    ssl: {
        [service: string]: string;
    };
    firewall: string;
    secrets: {
        [service: string]: string;
    };
    scanning: {
        [service: string]: string;
    };
}
export interface AutomationConfiguration {
    deploymentScripts: {
        [script: string]: string;
    };
    rollback: {
        [service: string]: string;
    };
    backup: {
        [service: string]: string;
    };
    maintenance: {
        [service: string]: string;
    };
}
export declare class DeploymentAgent extends BaseAgent {
    constructor();
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    private createDeploymentConfiguration;
    private generateDockerFiles;
    private generateDeploymentScripts;
    private generateDeploymentDocumentation;
    private generateEnvironmentConfigs;
    private writeDeploymentFiles;
    private createProductionDockerConfiguration;
    private createCICDPipelines;
    private createEnvironmentManagement;
    private createMonitoringConfiguration;
    private calculateDeploymentReadiness;
    private writeEnhancedDeploymentFiles;
    private createPerformanceOptimizations;
    private createSecurityConfigurations;
    private createDeploymentAutomation;
}
//# sourceMappingURL=DeploymentAgent.d.ts.map
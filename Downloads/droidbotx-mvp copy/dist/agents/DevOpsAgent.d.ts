import { BaseAgent, AgentTask, AgentResult } from '../core/BaseAgent';
import { GeneratedCode } from './CodingAgent';
export interface DevOpsConfiguration {
    containerization: {
        multiStage: boolean;
        securityScanning: boolean;
        baseImage: string;
        optimization: boolean;
    };
    ssl: {
        enabled: boolean;
        provider: 'letsencrypt' | 'custom' | 'cloudflare';
        autoRenewal: boolean;
    };
    secrets: {
        provider: 'env' | 'vault' | 'aws-secrets' | 'azure-keyvault';
        rotation: boolean;
        encryption: boolean;
    };
    monitoring: {
        healthChecks: boolean;
        metrics: boolean;
        logging: boolean;
        alerting: boolean;
    };
    scaling: {
        horizontal: boolean;
        loadBalancing: boolean;
        caching: boolean;
        cdn: boolean;
    };
    backup: {
        automated: boolean;
        retention: string;
        encryption: boolean;
        testing: boolean;
    };
}
export interface DevOpsEnhancedCode extends GeneratedCode {
    devopsConfiguration: DevOpsConfiguration;
    dockerFiles: {
        [fileName: string]: string;
    };
    kubernetesManifests: {
        [fileName: string]: string;
    };
    cicdPipelines: {
        [fileName: string]: string;
    };
    monitoringConfigs: {
        [fileName: string]: string;
    };
    securityPolicies: {
        [fileName: string]: string;
    };
    backupScripts: {
        [fileName: string]: string;
    };
    productionReadinessScore: number;
}
export declare class DevOpsAgent extends BaseAgent {
    private defaultConfiguration;
    constructor();
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    private determineDevOpsConfiguration;
    private generateProductionInfrastructure;
    private generateDockerConfigurations;
    private generateBackendDockerfile;
    private generateFrontendDockerfile;
    private generateDockerCompose;
    private generateProductionDockerCompose;
    private generateBackendDockerIgnore;
    private generateFrontendDockerIgnore;
    private generateKubernetesManifests;
    private generateNamespace;
    private generateBackendDeployment;
    private generateFrontendDeployment;
    private generateServices;
    private generateIngress;
    private generateConfigMap;
    private generateSecretsManifest;
    private generateHorizontalPodAutoscaler;
    private generatePostgresDeployment;
    private generateRedisDeployment;
    private generateMonitoringConfigurations;
    private generatePrometheusConfig;
    private generateGrafanaDashboard;
    private generateAlertManagerConfig;
    private generateAlertRules;
    private generateSecurityPolicies;
    private generateNetworkPolicy;
    private generatePodSecurityPolicy;
    private generateRBAC;
    private generateSecurityContext;
    private generateBackupScripts;
    private generateDatabaseBackupScript;
    private generateDatabaseRestoreScript;
    private generateBackupCronJob;
    private generateSSLConfigurations;
    private generateNginxSSLConfig;
    private generateCertManagerConfig;
    private generateSecretsManagement;
    private generateVaultConfig;
    private generateAWSSecretsConfig;
    private generateEnvExample;
    private generateCICDPipelines;
    private generateGitHubActions;
    private generateJenkinsfile;
    private calculateProductionReadinessScore;
    private writeInfrastructureFilesToDisk;
    private generateFluentdConfig;
    private generateAINamespace;
    private generateAIBackendDeployment;
    private generateAIFrontendDeployment;
    private generateAIPostgresDeployment;
    private generateAIRedisDeployment;
    private generateAIServices;
    private generateAIIngress;
    private generateAIConfigMap;
    private generateAISecrets;
    private generateAIHorizontalPodAutoscaler;
}
//# sourceMappingURL=DevOpsAgent.d.ts.map
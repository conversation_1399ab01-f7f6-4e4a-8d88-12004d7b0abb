"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevOpsAgent = void 0;
const BaseAgent_1 = require("../core/BaseAgent");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class DevOpsAgent extends BaseAgent_1.BaseAgent {
    constructor() {
        super('DevOpsAgent', 'Generates production-ready deployment configurations, security policies, and infrastructure automation', `You are a senior DevOps engineer and infrastructure architect responsible for creating enterprise-grade 
       deployment configurations and operational excellence frameworks.

       Your responsibilities include:
       - Creating multi-stage Docker builds with security scanning
       - Implementing SSL/TLS automation with certificate management
       - Setting up comprehensive secrets management with rotation
       - Designing horizontal scaling and load balancing solutions
       - Creating monitoring, logging, and alerting systems
       - Implementing backup and disaster recovery automation
       - Ensuring security compliance and operational excellence
       - Optimizing for performance, reliability, and cost-effectiveness

       Focus on creating infrastructure that is secure, scalable, maintainable, and follows industry best practices
       for production deployments.`);
        this.defaultConfiguration = {
            containerization: {
                multiStage: true,
                securityScanning: true,
                baseImage: 'node:18-alpine',
                optimization: true
            },
            ssl: {
                enabled: true,
                provider: 'letsencrypt',
                autoRenewal: true
            },
            secrets: {
                provider: 'env',
                rotation: true,
                encryption: true
            },
            monitoring: {
                healthChecks: true,
                metrics: true,
                logging: true,
                alerting: true
            },
            scaling: {
                horizontal: true,
                loadBalancing: true,
                caching: true,
                cdn: false
            },
            backup: {
                automated: true,
                retention: '30d',
                encryption: true,
                testing: true
            }
        };
    }
    canHandle(task) {
        return task.type === 'devops' || task.type === 'DEVOPS' || task.type === 'deploy' || task.type === 'infrastructure';
    }
    async execute(task) {
        this.logInfo('Starting DevOps infrastructure generation', {
            taskId: task.id,
            description: task.description
        });
        try {
            const generatedCode = task.parameters.previousResult;
            const technicalSpec = task.parameters.technicalSpec;
            if (!generatedCode) {
                throw new Error('No generated code provided from previous agent');
            }
            // Determine optimal DevOps configuration
            const devopsConfig = await this.determineDevOpsConfiguration(technicalSpec);
            // Generate production-ready infrastructure
            const enhancedCode = await this.generateProductionInfrastructure(generatedCode, devopsConfig, technicalSpec);
            // Calculate production readiness score
            const productionReadinessScore = await this.calculateProductionReadinessScore(enhancedCode);
            // Write infrastructure files to disk
            await this.writeInfrastructureFilesToDisk(enhancedCode);
            this.logInfo('DevOps infrastructure generation completed', {
                taskId: task.id,
                productionReadinessScore,
                dockerFiles: Object.keys(enhancedCode.dockerFiles).length,
                kubernetesManifests: Object.keys(enhancedCode.kubernetesManifests).length
            });
            return {
                success: true,
                data: {
                    ...enhancedCode,
                    productionReadinessScore
                },
                metadata: {
                    agent: 'DevOpsAgent',
                    timestamp: new Date().toISOString(),
                    productionReadinessScore,
                    infrastructureComponents: {
                        docker: Object.keys(enhancedCode.dockerFiles).length,
                        kubernetes: Object.keys(enhancedCode.kubernetesManifests).length,
                        monitoring: Object.keys(enhancedCode.monitoringConfigs).length,
                        security: Object.keys(enhancedCode.securityPolicies).length
                    }
                }
            };
        }
        catch (error) {
            this.logError('DevOps infrastructure generation failed', {
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            return {
                success: false,
                data: null,
                error: error instanceof Error ? error.message : 'Unknown DevOps infrastructure error'
            };
        }
    }
    async determineDevOpsConfiguration(spec) {
        const config = { ...this.defaultConfiguration };
        const requirements = (spec.description + ' ' + spec.features?.join(' ')).toLowerCase() || '';
        // Analyze requirements for infrastructure needs
        if (requirements.includes('enterprise') || requirements.includes('production')) {
            config.secrets.provider = 'vault';
            config.scaling.cdn = true;
            config.backup.retention = '90d';
        }
        if (requirements.includes('aws')) {
            config.secrets.provider = 'aws-secrets';
        }
        else if (requirements.includes('azure')) {
            config.secrets.provider = 'azure-keyvault';
        }
        if (requirements.includes('high availability') || requirements.includes('ha')) {
            config.scaling.horizontal = true;
            config.scaling.loadBalancing = true;
            config.backup.testing = true;
        }
        if (requirements.includes('compliance') || requirements.includes('security')) {
            config.containerization.securityScanning = true;
            config.secrets.rotation = true;
            config.backup.encryption = true;
        }
        return config;
    }
    async generateProductionInfrastructure(generatedCode, config, spec) {
        const enhancedCode = {
            ...generatedCode,
            devopsConfiguration: config,
            dockerFiles: {},
            kubernetesManifests: {},
            cicdPipelines: {},
            monitoringConfigs: {},
            securityPolicies: {},
            backupScripts: {},
            productionReadinessScore: 0
        };
        // Generate Docker configurations
        await this.generateDockerConfigurations(enhancedCode, config);
        // Generate Kubernetes manifests
        await this.generateKubernetesManifests(enhancedCode, config, spec);
        // Generate CI/CD pipelines
        await this.generateCICDPipelines(enhancedCode, config);
        // Generate monitoring configurations
        await this.generateMonitoringConfigurations(enhancedCode, config);
        // Generate security policies
        await this.generateSecurityPolicies(enhancedCode, config);
        // Generate backup and recovery scripts
        await this.generateBackupScripts(enhancedCode, config);
        // Generate SSL/TLS configurations
        await this.generateSSLConfigurations(enhancedCode, config);
        // Generate secrets management
        await this.generateSecretsManagement(enhancedCode, config);
        return enhancedCode;
    }
    async generateDockerConfigurations(enhancedCode, config) {
        // Generate multi-stage Dockerfile for backend
        enhancedCode.dockerFiles['backend/Dockerfile'] = this.generateBackendDockerfile(config);
        // Generate optimized Dockerfile for frontend
        enhancedCode.dockerFiles['frontend/Dockerfile'] = this.generateFrontendDockerfile(config);
        // Generate Docker Compose for development
        enhancedCode.dockerFiles['docker-compose.yml'] = this.generateDockerCompose(config);
        // Generate Docker Compose for production
        enhancedCode.dockerFiles['docker-compose.prod.yml'] = this.generateProductionDockerCompose(config);
        // Generate .dockerignore files
        enhancedCode.dockerFiles['backend/.dockerignore'] = this.generateBackendDockerIgnore();
        enhancedCode.dockerFiles['frontend/.dockerignore'] = this.generateFrontendDockerIgnore();
    }
    generateBackendDockerfile(config) {
        return `# Multi-stage build for production optimization
FROM ${config.containerization.baseImage} AS base

# Set working directory
WORKDIR /app

# Install security updates and required packages
RUN apk update && apk upgrade && \\
    apk add --no-cache \\
    dumb-init \\
    curl \\
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \\
    adduser -S nodejs -u 1001

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
FROM base AS deps
RUN npm ci --only=production && npm cache clean --force

# Build stage
FROM base AS build
COPY . .
RUN npm ci && npm run build

# Production stage
FROM ${config.containerization.baseImage} AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app directory and user
WORKDIR /app
RUN addgroup -g 1001 -S nodejs && \\
    adduser -S nodejs -u 1001

# Copy built application
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nodejs:nodejs /app/dist ./dist
COPY --from=build --chown=nodejs:nodejs /app/package*.json ./

# Set security headers and environment
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=1024"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:5000/health || exit 1

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 5000

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/server.js"]`;
    }
    generateFrontendDockerfile(config) {
        return `# Multi-stage build for React frontend
FROM ${config.containerization.baseImage} AS base

WORKDIR /app

# Install security updates
RUN apk update && apk upgrade && \\
    apk add --no-cache curl && \\
    rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./

# Install dependencies
FROM base AS deps
RUN npm ci --only=production && npm cache clean --force

# Build stage
FROM base AS build
COPY . .
RUN npm ci && npm run build

# Production stage with nginx
FROM nginx:alpine AS production

# Install security updates
RUN apk update && apk upgrade && \\
    apk add --no-cache curl && \\
    rm -rf /var/cache/apk/*

# Copy built app
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:80/health || exit 1

# Expose port
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]`;
    }
    generateDockerCompose(config) {
        return `version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/app_db
      - JWT_SECRET=dev-secret-change-in-production
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - app-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=app_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge`;
    }
    generateProductionDockerCompose(config) {
        return `version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=\${DATABASE_URL}
      - JWT_SECRET=\${JWT_SECRET}
      - REDIS_URL=\${REDIS_URL}
    depends_on:
      - postgres
      - redis
    networks:
      - app-network
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    restart: unless-stopped
    depends_on:
      - backend
    networks:
      - app-network
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.25'
          memory: 256M

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - app-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_DB=\${POSTGRES_DB}
      - POSTGRES_USER=\${POSTGRES_USER}
      - POSTGRES_PASSWORD=\${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - app-network
    command: redis-server --appendonly yes

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - app-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=\${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  app-network:
    driver: bridge`;
    }
    generateBackendDockerIgnore() {
        return `node_modules
npm-debug.log
.nyc_output
coverage
.env
.env.local
.env.production
dist
build
*.log
.git
.gitignore
README.md
Dockerfile
.dockerignore
.DS_Store
*.md`;
    }
    generateFrontendDockerIgnore() {
        return `node_modules
npm-debug.log
build
.env
.env.local
.env.production
.git
.gitignore
README.md
Dockerfile
.dockerignore
.DS_Store
*.md
coverage
.nyc_output`;
    }
    async generateKubernetesManifests(enhancedCode, config, spec) {
        this.logInfo('Generating AI-driven Kubernetes manifests', {
            projectName: spec.projectName,
            entities: spec.businessDomain?.entities?.length || 0,
            features: spec.features?.length || 0
        });
        // Generate AI-driven Kubernetes deployment manifests based on technical specification
        enhancedCode.kubernetesManifests['k8s/namespace.yaml'] = await this.generateAINamespace(spec);
        enhancedCode.kubernetesManifests['k8s/backend-deployment.yaml'] = await this.generateAIBackendDeployment(config, spec);
        enhancedCode.kubernetesManifests['k8s/frontend-deployment.yaml'] = await this.generateAIFrontendDeployment(config, spec);
        enhancedCode.kubernetesManifests['k8s/postgres-deployment.yaml'] = await this.generateAIPostgresDeployment(config, spec);
        enhancedCode.kubernetesManifests['k8s/redis-deployment.yaml'] = await this.generateAIRedisDeployment(config, spec);
        enhancedCode.kubernetesManifests['k8s/services.yaml'] = await this.generateAIServices(config, spec);
        enhancedCode.kubernetesManifests['k8s/ingress.yaml'] = await this.generateAIIngress(config, spec);
        enhancedCode.kubernetesManifests['k8s/configmap.yaml'] = await this.generateAIConfigMap(config, spec);
        enhancedCode.kubernetesManifests['k8s/secrets.yaml'] = await this.generateAISecrets(config, spec);
        if (config.scaling.horizontal) {
            enhancedCode.kubernetesManifests['k8s/hpa.yaml'] = await this.generateAIHorizontalPodAutoscaler(config, spec);
        }
        this.logInfo('AI-driven Kubernetes manifests generated successfully', {
            manifestCount: Object.keys(enhancedCode.kubernetesManifests).length
        });
    }
    generateNamespace() {
        return `apiVersion: v1
kind: Namespace
metadata:
  name: production-app
  labels:
    name: production-app
    environment: production`;
    }
    generateBackendDeployment(config) {
        return `apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: production-app
  labels:
    app: backend
    version: v1
spec:
  replicas: ${config.scaling.horizontal ? 3 : 1}
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
        version: v1
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: backend
        image: your-registry/backend:latest
        ports:
        - containerPort: 5000
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: redis-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL`;
    }
    generateFrontendDeployment(config) {
        return `apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: production-app
  labels:
    app: frontend
    version: v1
spec:
  replicas: ${config.scaling.horizontal ? 2 : 1}
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
        version: v1
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 101
        fsGroup: 101
      containers:
      - name: frontend
        image: your-registry/frontend:latest
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL`;
    }
    generateServices(config) {
        return `apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: production-app
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
  - port: 5000
    targetPort: 5000
    name: http
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: production-app
  labels:
    app: frontend
spec:
  selector:
    app: frontend
  ports:
  - port: 80
    targetPort: 80
    name: http
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: production-app
  labels:
    app: postgres
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: production-app
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
    name: redis
  type: ClusterIP`;
    }
    generateIngress(config) {
        return `apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  namespace: production-app
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - your-domain.com
    - api.your-domain.com
    secretName: app-tls-secret
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
  - host: api.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 5000`;
    }
    generateConfigMap(config) {
        return `apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: production-app
data:
  redis-url: "redis://redis-service:6379"
  node-env: "production"
  log-level: "info"
  rate-limit: "100"
  session-timeout: "3600"`;
    }
    generateSecretsManifest(config) {
        return `apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: production-app
type: Opaque
data:
  # Base64 encoded values - replace with actual secrets
  database-url: ****************************************************************************
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWhlcmU=
  postgres-password: eW91ci1wb3N0Z3Jlcy1wYXNzd29yZA==
  grafana-password: eW91ci1ncmFmYW5hLXBhc3N3b3Jk`;
    }
    generateHorizontalPodAutoscaler(config) {
        return `apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: production-app
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: frontend-hpa
  namespace: production-app
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontend
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70`;
    }
    generatePostgresDeployment(config) {
        return `apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: production-app
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: "app_db"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: postgres-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        ports:
        - containerPort: 5432
          name: postgres
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-init
          mountPath: /docker-entrypoint-initdb.d
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-init
        configMap:
          name: postgres-init-sql
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi`;
    }
    generateRedisDeployment(config) {
        return `apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: production-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: redis
        image: redis:7-alpine
        command: ["redis-server", "--appendonly", "yes"]
        ports:
        - containerPort: 6379
          name: redis
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: production-app
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi`;
    }
    async generateMonitoringConfigurations(enhancedCode, config) {
        if (config.monitoring.metrics) {
            enhancedCode.monitoringConfigs['monitoring/prometheus.yml'] = this.generatePrometheusConfig();
            enhancedCode.monitoringConfigs['monitoring/grafana-dashboard.json'] = this.generateGrafanaDashboard();
        }
        if (config.monitoring.alerting) {
            enhancedCode.monitoringConfigs['monitoring/alertmanager.yml'] = this.generateAlertManagerConfig();
            enhancedCode.monitoringConfigs['monitoring/alert-rules.yml'] = this.generateAlertRules();
        }
        if (config.monitoring.logging) {
            enhancedCode.monitoringConfigs['monitoring/fluentd.conf'] = this.generateFluentdConfig();
        }
    }
    generatePrometheusConfig() {
        return `global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert-rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'backend'
    static_configs:
      - targets: ['backend-service:5000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend-service:80']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)`;
    }
    generateGrafanaDashboard() {
        return `{
  "dashboard": {
    "id": null,
    "title": "Application Monitoring Dashboard",
    "tags": ["production", "monitoring"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Application Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\\"backend\\"}",
            "legendFormat": "Backend Status"
          },
          {
            "expr": "up{job=\\"frontend\\"}",
            "legendFormat": "Frontend Status"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ]
      },
      {
        "id": 3,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "id": 4,
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\\"5..\\"}[5m])",
            "legendFormat": "5xx errors/sec"
          }
        ]
      },
      {
        "id": 5,
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "Active connections"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}`;
    }
    generateAlertManagerConfig() {
        return `global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: 'Alert: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}
  slack_configs:
  - api_url: 'YOUR_SLACK_WEBHOOK_URL'
    channel: '#alerts'
    title: 'Alert: {{ .GroupLabels.alertname }}'
    text: |
      {{ range .Alerts }}
      {{ .Annotations.summary }}
      {{ .Annotations.description }}
      {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']`;
    }
    generateAlertRules() {
        return `groups:
- name: application.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }} seconds"

  - alert: DatabaseDown
    expr: up{job="postgres"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database is down"
      description: "PostgreSQL database is not responding"

  - alert: HighCPUUsage
    expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage"
      description: "CPU usage is above 80% for {{ $labels.container }}"

  - alert: HighMemoryUsage
    expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High memory usage"
      description: "Memory usage is above 90% for {{ $labels.container }}"

  - alert: DiskSpaceLow
    expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low disk space"
      description: "Disk space is below 10% on {{ $labels.instance }}"`;
    }
    async generateSecurityPolicies(enhancedCode, config) {
        enhancedCode.securityPolicies['security/network-policy.yaml'] = this.generateNetworkPolicy();
        enhancedCode.securityPolicies['security/pod-security-policy.yaml'] = this.generatePodSecurityPolicy();
        enhancedCode.securityPolicies['security/rbac.yaml'] = this.generateRBAC();
        enhancedCode.securityPolicies['security/security-context.yaml'] = this.generateSecurityContext();
    }
    generateNetworkPolicy() {
        return `apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: app-network-policy
  namespace: production-app
spec:
  podSelector:
    matchLabels:
      app: backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: frontend
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 5000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53`;
    }
    generatePodSecurityPolicy() {
        return `apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: restricted-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true`;
    }
    generateRBAC() {
        return `apiVersion: v1
kind: ServiceAccount
metadata:
  name: app-service-account
  namespace: production-app

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production-app
  name: app-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: app-role-binding
  namespace: production-app
subjects:
- kind: ServiceAccount
  name: app-service-account
  namespace: production-app
roleRef:
  kind: Role
  name: app-role
  apiGroup: rbac.authorization.k8s.io`;
    }
    generateSecurityContext() {
        return `# Security Context Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-context-config
  namespace: production-app
data:
  security-context.yaml: |
    securityContext:
      runAsNonRoot: true
      runAsUser: 1001
      runAsGroup: 1001
      fsGroup: 1001
      seccompProfile:
        type: RuntimeDefault
    containerSecurityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
        add:
        - NET_BIND_SERVICE`;
    }
    async generateBackupScripts(enhancedCode, config) {
        if (config.backup.automated) {
            enhancedCode.backupScripts['scripts/backup-database.sh'] = this.generateDatabaseBackupScript(config);
            enhancedCode.backupScripts['scripts/restore-database.sh'] = this.generateDatabaseRestoreScript(config);
            enhancedCode.backupScripts['k8s/backup-cronjob.yaml'] = this.generateBackupCronJob(config);
        }
    }
    generateDatabaseBackupScript(config) {
        return `#!/bin/bash
set -e

# Database backup script
BACKUP_DIR="/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="backup_\${TIMESTAMP}.sql"
ENCRYPTED_FILE="backup_\${TIMESTAMP}.sql.gpg"

# Create backup directory if it doesn't exist
mkdir -p \${BACKUP_DIR}

# Database connection details
DB_HOST=\${DB_HOST:-postgres-service}
DB_PORT=\${DB_PORT:-5432}
DB_NAME=\${DB_NAME:-app_db}
DB_USER=\${DB_USER:-postgres}

echo "Starting database backup at $(date)"

# Create database dump
PGPASSWORD=\${POSTGRES_PASSWORD} pg_dump \\
  -h \${DB_HOST} \\
  -p \${DB_PORT} \\
  -U \${DB_USER} \\
  -d \${DB_NAME} \\
  --verbose \\
  --no-password \\
  --format=custom \\
  --file=\${BACKUP_DIR}/\${BACKUP_FILE}

if [ $? -eq 0 ]; then
  echo "Database dump created successfully: \${BACKUP_FILE}"
else
  echo "Database dump failed"
  exit 1
fi

# Encrypt backup if encryption is enabled
if [ "\${BACKUP_ENCRYPTION}" = "true" ]; then
  gpg --symmetric --cipher-algo AES256 --compress-algo 1 \\
    --passphrase "\${BACKUP_PASSPHRASE}" \\
    --output \${BACKUP_DIR}/\${ENCRYPTED_FILE} \\
    \${BACKUP_DIR}/\${BACKUP_FILE}

  if [ $? -eq 0 ]; then
    echo "Backup encrypted successfully: \${ENCRYPTED_FILE}"
    rm \${BACKUP_DIR}/\${BACKUP_FILE}
  else
    echo "Backup encryption failed"
    exit 1
  fi
fi

# Upload to cloud storage (if configured)
if [ -n "\${AWS_S3_BUCKET}" ]; then
  aws s3 cp \${BACKUP_DIR}/\${ENCRYPTED_FILE:-\${BACKUP_FILE}} \\
    s3://\${AWS_S3_BUCKET}/backups/
  echo "Backup uploaded to S3"
fi

# Clean up old backups (keep last 30 days)
find \${BACKUP_DIR} -name "backup_*.sql*" -mtime +30 -delete

echo "Backup completed successfully at $(date)"`;
    }
    generateDatabaseRestoreScript(config) {
        return `#!/bin/bash
set -e

# Database restore script
BACKUP_DIR="/backups"
RESTORE_FILE=\$1

if [ -z "\${RESTORE_FILE}" ]; then
  echo "Usage: \$0 <backup_file>"
  echo "Available backups:"
  ls -la \${BACKUP_DIR}/backup_*.sql*
  exit 1
fi

# Database connection details
DB_HOST=\${DB_HOST:-postgres-service}
DB_PORT=\${DB_PORT:-5432}
DB_NAME=\${DB_NAME:-app_db}
DB_USER=\${DB_USER:-postgres}

echo "Starting database restore from \${RESTORE_FILE} at $(date)"

# Check if file is encrypted
if [[ "\${RESTORE_FILE}" == *.gpg ]]; then
  echo "Decrypting backup file..."
  DECRYPTED_FILE=\${RESTORE_FILE%.gpg}
  gpg --decrypt --passphrase "\${BACKUP_PASSPHRASE}" \\
    --output \${BACKUP_DIR}/\${DECRYPTED_FILE} \\
    \${BACKUP_DIR}/\${RESTORE_FILE}
  RESTORE_FILE=\${DECRYPTED_FILE}
fi

# Restore database
PGPASSWORD=\${POSTGRES_PASSWORD} pg_restore \\
  -h \${DB_HOST} \\
  -p \${DB_PORT} \\
  -U \${DB_USER} \\
  -d \${DB_NAME} \\
  --verbose \\
  --clean \\
  --if-exists \\
  --no-password \\
  \${BACKUP_DIR}/\${RESTORE_FILE}

if [ $? -eq 0 ]; then
  echo "Database restore completed successfully at $(date)"
else
  echo "Database restore failed"
  exit 1
fi

# Clean up decrypted file
if [[ "\${RESTORE_FILE}" != *.gpg ]] && [ -f "\${BACKUP_DIR}/\${RESTORE_FILE}" ]; then
  rm \${BACKUP_DIR}/\${RESTORE_FILE}
fi`;
    }
    generateBackupCronJob(config) {
        return `apiVersion: batch/v1
kind: CronJob
metadata:
  name: database-backup
  namespace: production-app
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
          - name: backup
            image: postgres:15-alpine
            command:
            - /bin/bash
            - -c
            - |
              apk add --no-cache aws-cli gnupg
              /scripts/backup-database.sh
            env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: postgres-password
            - name: BACKUP_ENCRYPTION
              value: "true"
            - name: BACKUP_PASSPHRASE
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: backup-passphrase
            - name: AWS_S3_BUCKET
              value: "your-backup-bucket"
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
            - name: backup-storage
              mountPath: /backups
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
              defaultMode: 0755
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc`;
    }
    async generateSSLConfigurations(enhancedCode, config) {
        if (config.ssl.enabled) {
            enhancedCode.files['nginx/nginx.conf'] = this.generateNginxSSLConfig(config);
            enhancedCode.kubernetesManifests['k8s/cert-manager.yaml'] = this.generateCertManagerConfig(config);
        }
    }
    generateNginxSSLConfig(config) {
        return `events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # Upstream servers
    upstream backend {
        server backend-service:5000;
    }

    upstream frontend {
        server frontend-service:80;
    }

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name your-domain.com api.your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    # Main application server
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL configuration
        ssl_certificate /etc/nginx/ssl/tls.crt;
        ssl_certificate_key /etc/nginx/ssl/tls.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # OCSP stapling
        ssl_stapling on;
        ssl_stapling_verify on;

        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\\n";
            add_header Content-Type text/plain;
        }
    }

    # API server
    server {
        listen 443 ssl http2;
        server_name api.your-domain.com;

        # SSL configuration (same as above)
        ssl_certificate /etc/nginx/ssl/tls.crt;
        ssl_certificate_key /etc/nginx/ssl/tls.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # API endpoints
        location / {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Stricter rate limiting for auth endpoints
        location /auth/ {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}`;
    }
    generateCertManagerConfig(config) {
        return `apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx

---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: app-tls-certificate
  namespace: production-app
spec:
  secretName: app-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - your-domain.com
  - api.your-domain.com`;
    }
    async generateSecretsManagement(enhancedCode, config) {
        switch (config.secrets.provider) {
            case 'vault':
                enhancedCode.files['vault/vault-config.hcl'] = this.generateVaultConfig();
                break;
            case 'aws-secrets':
                enhancedCode.files['aws/secrets-manager.yaml'] = this.generateAWSSecretsConfig();
                break;
            default:
                enhancedCode.files['.env.example'] = this.generateEnvExample();
        }
    }
    generateVaultConfig() {
        return `storage "consul" {
  address = "127.0.0.1:8500"
  path    = "vault/"
}

listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = 0
  tls_cert_file = "/etc/vault/tls/vault.crt"
  tls_key_file = "/etc/vault/tls/vault.key"
}

api_addr = "https://vault.your-domain.com:8200"
cluster_addr = "https://vault.your-domain.com:8201"
ui = true

# Enable audit logging
audit {
  file {
    file_path = "/vault/logs/audit.log"
  }
}`;
    }
    generateAWSSecretsConfig() {
        return `apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secrets-store
  namespace: production-app
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-west-2
      auth:
        jwt:
          serviceAccountRef:
            name: external-secrets-sa

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: app-secrets
  namespace: production-app
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-store
    kind: SecretStore
  target:
    name: app-secrets
    creationPolicy: Owner
  data:
  - secretKey: database-url
    remoteRef:
      key: prod/database
      property: url
  - secretKey: jwt-secret
    remoteRef:
      key: prod/auth
      property: jwt-secret`;
    }
    generateEnvExample() {
        return `# Environment Variables Example
# Copy this file to .env and fill in the actual values

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
POSTGRES_DB=app_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password

# Authentication
JWT_SECRET=your_jwt_secret_here_make_it_long_and_random
JWT_EXPIRES_IN=24h

# Redis
REDIS_URL=redis://localhost:6379

# Application
NODE_ENV=production
PORT=5000
LOG_LEVEL=info

# Monitoring
GRAFANA_PASSWORD=your_grafana_password

# Backup
BACKUP_ENCRYPTION=true
BACKUP_PASSPHRASE=your_backup_passphrase
AWS_S3_BUCKET=your-backup-bucket

# SSL/TLS
SSL_CERT_PATH=/etc/ssl/certs/app.crt
SSL_KEY_PATH=/etc/ssl/private/app.key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100`;
    }
    async generateCICDPipelines(enhancedCode, config) {
        enhancedCode.cicdPipelines['.github/workflows/ci-cd.yml'] = this.generateGitHubActions();
        enhancedCode.cicdPipelines['Jenkinsfile'] = this.generateJenkinsfile();
    }
    generateGitHubActions() {
        return `name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: \${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        cd backend && npm ci
        cd ../frontend && npm ci

    - name: Run backend tests
      run: |
        cd backend
        npm run test
        npm run test:coverage
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379

    - name: Run frontend tests
      run: |
        cd frontend
        npm run test -- --coverage --watchAll=false

    - name: Build applications
      run: |
        cd backend && npm run build
        cd ../frontend && npm run build

    - name: Security scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif'

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: \${{ env.REGISTRY }}
        username: \${{ github.actor }}
        password: \${{ secrets.GITHUB_TOKEN }}

    - name: Build and push Docker images
      run: |
        # Backend
        docker build -t \${{ env.REGISTRY }}/\${{ env.IMAGE_NAME }}/backend:\${{ github.sha }} ./backend
        docker push \${{ env.REGISTRY }}/\${{ env.IMAGE_NAME }}/backend:\${{ github.sha }}

        # Frontend
        docker build -t \${{ env.REGISTRY }}/\${{ env.IMAGE_NAME }}/frontend:\${{ github.sha }} ./frontend
        docker push \${{ env.REGISTRY }}/\${{ env.IMAGE_NAME }}/frontend:\${{ github.sha }}

    - name: Deploy to Kubernetes
      run: |
        echo "\${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

        # Update image tags in manifests
        sed -i "s|your-registry/backend:latest|\${{ env.REGISTRY }}/\${{ env.IMAGE_NAME }}/backend:\${{ github.sha }}|g" k8s/backend-deployment.yaml
        sed -i "s|your-registry/frontend:latest|\${{ env.REGISTRY }}/\${{ env.IMAGE_NAME }}/frontend:\${{ github.sha }}|g" k8s/frontend-deployment.yaml

        # Apply manifests
        kubectl apply -f k8s/

        # Wait for rollout
        kubectl rollout status deployment/backend -n production-app
        kubectl rollout status deployment/frontend -n production-app`;
    }
    generateJenkinsfile() {
        return `pipeline {
    agent any

    environment {
        REGISTRY = 'your-registry.com'
        IMAGE_NAME = 'your-app'
        KUBECONFIG = credentials('kubeconfig')
    }

    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }

        stage('Test') {
            parallel {
                stage('Backend Tests') {
                    steps {
                        dir('backend') {
                            sh 'npm ci'
                            sh 'npm run test'
                            sh 'npm run test:coverage'
                        }
                    }
                    post {
                        always {
                            publishTestResults testResultsPattern: 'backend/test-results.xml'
                            publishCoverageResults(
                                adapters: [coberturaAdapter('backend/coverage/cobertura-coverage.xml')],
                                sourceFileResolver: sourceFiles('STORE_LAST_BUILD')
                            )
                        }
                    }
                }

                stage('Frontend Tests') {
                    steps {
                        dir('frontend') {
                            sh 'npm ci'
                            sh 'npm run test -- --coverage --watchAll=false'
                        }
                    }
                }
            }
        }

        stage('Security Scan') {
            steps {
                sh 'docker run --rm -v "\$(pwd):/app" securecodewarrior/sast-scan /app'
            }
        }

        stage('Build') {
            steps {
                script {
                    def backendImage = docker.build("\${REGISTRY}/\${IMAGE_NAME}/backend:\${BUILD_NUMBER}", "./backend")
                    def frontendImage = docker.build("\${REGISTRY}/\${IMAGE_NAME}/frontend:\${BUILD_NUMBER}", "./frontend")

                    docker.withRegistry("https://\${REGISTRY}", 'registry-credentials') {
                        backendImage.push()
                        frontendImage.push()
                        backendImage.push("latest")
                        frontendImage.push("latest")
                    }
                }
            }
        }

        stage('Deploy') {
            when {
                branch 'main'
            }
            steps {
                script {
                    sh """
                        sed -i 's|your-registry/backend:latest|\${REGISTRY}/\${IMAGE_NAME}/backend:\${BUILD_NUMBER}|g' k8s/backend-deployment.yaml
                        sed -i 's|your-registry/frontend:latest|\${REGISTRY}/\${IMAGE_NAME}/frontend:\${BUILD_NUMBER}|g' k8s/frontend-deployment.yaml
                        kubectl apply -f k8s/
                        kubectl rollout status deployment/backend -n production-app
                        kubectl rollout status deployment/frontend -n production-app
                    """
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
        failure {
            emailext (
                subject: "Build Failed: \${env.JOB_NAME} - \${env.BUILD_NUMBER}",
                body: "Build failed. Check console output at \${env.BUILD_URL}",
                to: "\${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}`;
    }
    async calculateProductionReadinessScore(enhancedCode) {
        let score = 0;
        const maxScore = 100;
        // Docker configurations (20 points)
        if (Object.keys(enhancedCode.dockerFiles).length >= 4)
            score += 20;
        // Kubernetes manifests (25 points)
        if (Object.keys(enhancedCode.kubernetesManifests).length >= 8)
            score += 25;
        // Monitoring configurations (20 points)
        if (Object.keys(enhancedCode.monitoringConfigs).length >= 3)
            score += 20;
        // Security policies (15 points)
        if (Object.keys(enhancedCode.securityPolicies).length >= 3)
            score += 15;
        // Backup scripts (10 points)
        if (Object.keys(enhancedCode.backupScripts).length >= 2)
            score += 10;
        // CI/CD pipelines (10 points)
        if (Object.keys(enhancedCode.cicdPipelines).length >= 1)
            score += 10;
        return Math.min(score, maxScore);
    }
    async writeInfrastructureFilesToDisk(enhancedCode) {
        // Write all generated files to disk
        const allFiles = {
            ...enhancedCode.files,
            ...enhancedCode.dockerFiles,
            ...enhancedCode.kubernetesManifests,
            ...enhancedCode.cicdPipelines,
            ...enhancedCode.monitoringConfigs,
            ...enhancedCode.securityPolicies,
            ...enhancedCode.backupScripts
        };
        for (const [filePath, content] of Object.entries(allFiles)) {
            const fullPath = path.join(enhancedCode.projectPath, filePath);
            const dir = path.dirname(fullPath);
            // Ensure directory exists
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            fs.writeFileSync(fullPath, content);
        }
        // Write package.json files
        for (const [packagePath, packageContent] of Object.entries(enhancedCode.packageJsons)) {
            const fullPath = path.join(enhancedCode.projectPath, packagePath);
            fs.writeFileSync(fullPath, JSON.stringify(packageContent, null, 2));
        }
    }
    generateFluentdConfig() {
        return `<source>
  @type tail
  path /var/log/containers/*.log
  pos_file /var/log/fluentd-containers.log.pos
  tag kubernetes.*
  read_from_head true
  <parse>
    @type json
    time_format %Y-%m-%dT%H:%M:%S.%NZ
  </parse>
</source>

<filter kubernetes.**>
  @type kubernetes_metadata
</filter>

<match kubernetes.**>
  @type elasticsearch
  host elasticsearch.logging.svc.cluster.local
  port 9200
  logstash_format true
  logstash_prefix kubernetes
  <buffer>
    @type file
    path /var/log/fluentd-buffers/kubernetes.system.buffer
    flush_mode interval
    retry_type exponential_backoff
    flush_thread_count 2
    flush_interval 5s
    retry_forever
    retry_max_interval 30
    chunk_limit_size 2M
    queue_limit_length 8
    overflow_action block
  </buffer>
</match>`;
    }
    // AI-Driven Kubernetes Manifest Generation Methods
    // These replace template-based generation with AI-driven, context-aware generation
    async generateAINamespace(spec) {
        const prompt = `Generate a Kubernetes namespace YAML manifest for the project "${spec.projectName}".

    Requirements:
    - Use project name in kebab-case for namespace name
    - Include appropriate labels and annotations
    - Add resource quotas if needed
    - Follow Kubernetes best practices

    Return only the YAML manifest without markdown formatting.`;
        try {
            const response = await this.llmProvider.generateResponse([
                { role: 'user', content: prompt }
            ], {
                temperature: 0.1,
                maxTokens: 500
            });
            return response.content.trim();
        }
        catch (error) {
            this.logError('Failed to generate AI namespace', { error: error instanceof Error ? error.message : 'Unknown error' });
            // Fallback to basic namespace
            return `apiVersion: v1
kind: Namespace
metadata:
  name: ${spec.projectName}
  labels:
    name: ${spec.projectName}
    environment: production`;
        }
    }
    async generateAIBackendDeployment(config, spec) {
        const prompt = `Generate a Kubernetes Deployment YAML manifest for the backend service of "${spec.projectName}".

    Project Details:
    - Project: ${spec.projectName}
    - Framework: ${spec.architecture?.backend?.framework || 'Express.js with TypeScript'}
    - Database: ${spec.architecture?.backend?.database || 'PostgreSQL'}
    - Authentication: ${spec.architecture?.backend?.authentication || 'JWT'}
    - APIs: ${spec.architecture?.backend?.apis?.length || 0} endpoints
    - Entities: ${spec.businessDomain?.entities?.length || 0}

    Configuration:
    - Horizontal scaling: ${config.scaling.horizontal}
    - Replicas: ${config.scaling.horizontal ? 3 : 1}
    - Environment: production

    Requirements:
    - Include proper resource limits and requests
    - Add health checks (liveness and readiness probes)
    - Include environment variables for database connection
    - Add security context
    - Follow Kubernetes best practices
    - Include proper labels and selectors

    Return only the YAML manifest without markdown formatting.`;
        try {
            const response = await this.llmProvider.generateResponse([
                { role: 'user', content: prompt }
            ], {
                temperature: 0.1,
                maxTokens: 1000
            });
            return response.content.trim();
        }
        catch (error) {
            this.logError('Failed to generate AI backend deployment', { error: error instanceof Error ? error.message : 'Unknown error' });
            // Fallback to basic deployment
            return this.generateBackendDeployment(config);
        }
    }
    async generateAIFrontendDeployment(config, spec) {
        const prompt = `Generate a Kubernetes Deployment YAML manifest for the frontend service of "${spec.projectName}".

    Project Details:
    - Project: ${spec.projectName}
    - Framework: ${spec.architecture?.frontend?.framework || 'React with TypeScript'}
    - State Management: ${spec.architecture?.frontend?.stateManagement || 'Redux Toolkit'}
    - Components: ${spec.architecture?.frontend?.components?.length || 0}

    Configuration:
    - Horizontal scaling: ${config.scaling.horizontal}
    - Replicas: ${config.scaling.horizontal ? 2 : 1}
    - Environment: production

    Requirements:
    - Include proper resource limits and requests
    - Add health checks for React app
    - Include environment variables for API endpoints
    - Add security context
    - Configure for production build
    - Follow Kubernetes best practices

    Return only the YAML manifest without markdown formatting.`;
        try {
            const response = await this.llmProvider.generateResponse([
                { role: 'user', content: prompt }
            ], {
                temperature: 0.1,
                maxTokens: 1000
            });
            return response.content.trim();
        }
        catch (error) {
            this.logError('Failed to generate AI frontend deployment', { error: error instanceof Error ? error.message : 'Unknown error' });
            // Fallback to basic deployment
            return this.generateFrontendDeployment(config);
        }
    }
    async generateAIPostgresDeployment(config, spec) {
        const entities = spec.businessDomain?.entities || [];
        const entityNames = entities.map(e => e.name).join(', ');
        const prompt = `Generate a Kubernetes StatefulSet YAML manifest for PostgreSQL database for "${spec.projectName}".

    Project Details:
    - Project: ${spec.projectName}
    - Database entities: ${entityNames}
    - Entity count: ${entities.length}
    - Features requiring database: ${spec.features?.length || 0}

    Requirements:
    - Use StatefulSet for data persistence
    - Include persistent volume claims
    - Add proper resource limits and requests
    - Include health checks and readiness probes
    - Add initialization scripts if needed
    - Configure for production use
    - Include backup considerations
    - Follow PostgreSQL best practices

    Return only the YAML manifest without markdown formatting.`;
        try {
            const response = await this.llmProvider.generateResponse([
                { role: 'user', content: prompt }
            ], {
                temperature: 0.1,
                maxTokens: 1200
            });
            return response.content.trim();
        }
        catch (error) {
            this.logError('Failed to generate AI postgres deployment', { error: error instanceof Error ? error.message : 'Unknown error' });
            // Fallback to basic deployment
            return this.generatePostgresDeployment(config);
        }
    }
    async generateAIRedisDeployment(config, spec) {
        // Use existing method for Redis as it's already optimized
        return this.generateRedisDeployment(config);
    }
    async generateAIServices(config, spec) {
        const prompt = `Generate Kubernetes Service YAML manifests for "${spec.projectName}".

    Required services:
    - Backend service (${spec.architecture?.backend?.framework || 'Express.js'})
    - Frontend service (${spec.architecture?.frontend?.framework || 'React'})
    - PostgreSQL database service
    - Redis cache service (if needed)

    Requirements:
    - Include all necessary services in one YAML file
    - Use appropriate service types (ClusterIP, NodePort, LoadBalancer)
    - Include proper port configurations
    - Add service discovery labels
    - Follow Kubernetes best practices

    Return only the YAML manifest without markdown formatting.`;
        try {
            const response = await this.llmProvider.generateResponse([
                { role: 'user', content: prompt }
            ], {
                temperature: 0.1,
                maxTokens: 800
            });
            return response.content.trim();
        }
        catch (error) {
            this.logError('Failed to generate AI services', { error: error instanceof Error ? error.message : 'Unknown error' });
            return this.generateServices(config);
        }
    }
    async generateAIIngress(config, spec) {
        const prompt = `Generate a Kubernetes Ingress YAML manifest for "${spec.projectName}".

    Project Details:
    - Project: ${spec.projectName}
    - APIs: ${spec.architecture?.backend?.apis?.length || 0} endpoints
    - Features: ${spec.features?.length || 0}

    Requirements:
    - Configure SSL/TLS termination
    - Include proper routing rules for frontend and backend
    - Add rate limiting annotations
    - Include CORS configuration
    - Follow security best practices
    - Support for production domain

    Return only the YAML manifest without markdown formatting.`;
        try {
            const response = await this.llmProvider.generateResponse([
                { role: 'user', content: prompt }
            ], {
                temperature: 0.1,
                maxTokens: 600
            });
            return response.content.trim();
        }
        catch (error) {
            this.logError('Failed to generate AI ingress', { error: error instanceof Error ? error.message : 'Unknown error' });
            return this.generateIngress(config);
        }
    }
    async generateAIConfigMap(config, spec) {
        const prompt = `Generate a Kubernetes ConfigMap YAML manifest for "${spec.projectName}".

    Project Details:
    - Project: ${spec.projectName}
    - Database: ${spec.architecture?.backend?.database || 'PostgreSQL'}
    - Features: ${spec.features?.join(', ') || 'Standard features'}

    Requirements:
    - Include application configuration
    - Add database connection settings (non-sensitive)
    - Include feature flags if applicable
    - Add logging configuration
    - Follow configuration best practices

    Return only the YAML manifest without markdown formatting.`;
        try {
            const response = await this.llmProvider.generateResponse([
                { role: 'user', content: prompt }
            ], {
                temperature: 0.1,
                maxTokens: 600
            });
            return response.content.trim();
        }
        catch (error) {
            this.logError('Failed to generate AI configmap', { error: error instanceof Error ? error.message : 'Unknown error' });
            return this.generateConfigMap(config);
        }
    }
    async generateAISecrets(config, spec) {
        // Use existing method for secrets as it handles sensitive data properly
        return this.generateSecretsManifest(config);
    }
    async generateAIHorizontalPodAutoscaler(config, spec) {
        const prompt = `Generate a Kubernetes HorizontalPodAutoscaler YAML manifest for "${spec.projectName}".

    Project Details:
    - Project: ${spec.projectName}
    - Expected load: ${spec.features?.length || 0} features
    - Scaling enabled: ${config.scaling.horizontal}

    Requirements:
    - Configure CPU and memory-based scaling
    - Set appropriate min/max replicas
    - Include scaling policies
    - Follow HPA best practices

    Return only the YAML manifest without markdown formatting.`;
        try {
            const response = await this.llmProvider.generateResponse([
                { role: 'user', content: prompt }
            ], {
                temperature: 0.1,
                maxTokens: 500
            });
            return response.content.trim();
        }
        catch (error) {
            this.logError('Failed to generate AI HPA', { error: error instanceof Error ? error.message : 'Unknown error' });
            return this.generateHorizontalPodAutoscaler(config);
        }
    }
}
exports.DevOpsAgent = DevOpsAgent;
//# sourceMappingURL=DevOpsAgent.js.map
import { BaseAgent, AgentTask, AgentResult } from '../core/BaseAgent';
export interface BusinessEntity {
    name: string;
    description: string;
    fields: {
        name: string;
        type: string;
        required: boolean;
        description: string;
    }[];
    relationships: {
        type: 'oneToMany' | 'manyToOne' | 'manyToMany' | 'oneToOne';
        target: string;
        description: string;
    }[];
    operations: {
        name: string;
        type: 'create' | 'read' | 'update' | 'delete' | 'custom';
        description: string;
        parameters?: string[];
    }[];
}
export interface BusinessDomain {
    name: string;
    description: string;
    entities: BusinessEntity[];
    workflows: {
        name: string;
        description: string;
        steps: string[];
        entities: string[];
    }[];
    userRoles: {
        name: string;
        permissions: string[];
        description: string;
    }[];
}
export interface TechnicalSpecification {
    projectName: string;
    description: string;
    businessDomain: BusinessDomain;
    architecture: {
        frontend: {
            framework: string;
            components: string[];
            routing: boolean;
            stateManagement: string;
        };
        backend: {
            framework: string;
            database: string;
            authentication: string;
            apis: string[];
        };
        deployment: {
            containerization: string;
            environment: string;
            ports: number[];
        };
    };
    dependencies: {
        frontend: string[];
        backend: string[];
        dev: string[];
    };
    fileStructure: {
        [key: string]: string[];
    };
    features: string[];
    testingStrategy: string[];
}
export declare class PlanningAgent extends BaseAgent {
    private semanticAnalyzer;
    private jsonRepairAttempts;
    private maxRepairAttempts;
    constructor();
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    private extractProjectName;
    private generateTechnicalSpecification;
    private buildTechnicalSpecPrompt;
    private convertToLegacyBusinessDomain;
    private buildPlanningPrompt;
    private parseTechnicalSpecification;
    private preValidateResponse;
    private postValidateParsedResult;
    private parseWithRobustFallbacks;
    private parseCleanJson;
    private parseCleanJsonWithRepair;
    private parseWithBasicFixes;
    private parseWithAdvancedFixes;
    private parseWithEnhancedRepair;
    private parseWithAIAssistance;
    private parseWithChunkedAIAssistance;
    private parseWithManualRepair;
    private forceCloseJSON;
    private cleanupJSONString;
    private parseWithRegexExtraction;
    private parseWithIntelligentAnalysis;
    private generateComprehensiveFallbackBlueprint;
    private extractProjectNameFromText;
    private extractDescriptionFromText;
    private extractFeaturesFromText;
    private buildTechnicalSpecFromParsed;
    private validateTechnicalSpecification;
    private normalizeFeatures;
    private ensureArray;
    private extractEntitiesFromText;
    private extractAPIsFromText;
    private generateDefaultFields;
    private generateWorkflowsFromFeatures;
    private generateUserRoles;
    private generateComprehensiveFields;
    private generateEntityRelationships;
    private generateComprehensiveOperations;
    private generateComprehensiveWorkflows;
    private generateComprehensiveUserRoles;
    private generateComprehensiveArchitecture;
    private generateProductionArchitecture;
    private generateComprehensiveDependencies;
    private generateProductionFileStructure;
    private generateDefaultFeatures;
    private generateComprehensiveTestingStrategy;
}
//# sourceMappingURL=PlanningAgent.d.ts.map
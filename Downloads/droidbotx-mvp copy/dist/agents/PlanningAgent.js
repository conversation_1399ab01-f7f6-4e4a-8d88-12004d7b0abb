"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanningAgent = void 0;
const BaseAgent_1 = require("../core/BaseAgent");
const SemanticAnalyzer_1 = require("../core/SemanticAnalyzer");
class PlanningAgent extends BaseAgent_1.BaseAgent {
    constructor() {
        super('PlanningAgent', 'Comprehensive Blueprint Generator - Creates detailed system blueprints for zero-template code generation', `You are a world-class software architect and system designer responsible for creating comprehensive system blueprints that eliminate the need for any template-based code generation.

Your role is to:
1. Perform deep semantic analysis to understand the true business domain and requirements
2. Generate comprehensive system blueprints with precise, unambiguous specifications
3. Create detailed technical specifications that include:
   - Complete file structures with exact paths and purposes
   - Detailed API contracts with request/response schemas
   - Comprehensive database schemas with relationships and constraints
   - Component hierarchies with data flow specifications
   - Security requirements and authentication patterns
   - Testing strategies and deployment configurations
   - Performance requirements and optimization guidelines

4. Ensure the blueprint is so detailed that subsequent agents can generate production-ready code without making assumptions
5. Eliminate all template dependencies by providing precise specifications for every component

You create blueprints that enable 100% AI-driven code generation, achieving 90-95/100 production readiness scores with zero manual intervention.

Focus on creating specifications that result in complete, functional applications that solve real business problems with enterprise-grade quality.`);
        this.jsonRepairAttempts = 0;
        this.maxRepairAttempts = 3;
        this.semanticAnalyzer = new SemanticAnalyzer_1.SemanticAnalyzer();
    }
    canHandle(task) {
        return task.type === 'plan';
    }
    async execute(task) {
        try {
            this.logInfo('Starting AI-driven planning phase with semantic analysis', { taskId: task.id });
            const requirements = task.parameters.requirements || [];
            const description = task.description;
            // Step 1: Perform semantic analysis to understand business domain
            this.logInfo('Performing semantic analysis', { taskId: task.id });
            const businessAnalysis = await this.semanticAnalyzer.analyzeBusinessDomain(this.extractProjectName(description), description, requirements);
            // Step 2: Generate technical specification based on semantic analysis
            this.logInfo('Generating technical specification', {
                taskId: task.id,
                domain: businessAnalysis.domain,
                confidence: businessAnalysis.confidence
            });
            const technicalSpec = await this.generateTechnicalSpecification(businessAnalysis, description, requirements);
            // Step 3: Validate the specification
            this.validateTechnicalSpecification(technicalSpec);
            this.logInfo('AI-driven planning phase completed successfully', {
                taskId: task.id,
                projectName: technicalSpec.projectName,
                domain: businessAnalysis.domain,
                entitiesCount: businessAnalysis.entities.length,
                features: technicalSpec.features.length,
            });
            return {
                success: true,
                data: technicalSpec,
                metadata: {
                    phase: 'planning',
                    timestamp: new Date().toISOString(),
                    semanticAnalysis: businessAnalysis,
                },
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown planning error';
            this.logError('AI-driven planning phase failed', { taskId: task.id, error: errorMessage });
            return {
                success: false,
                error: errorMessage,
            };
        }
    }
    extractProjectName(description) {
        // Extract project name from description or generate one based on domain
        const words = description.split(' ').filter(word => word.length > 2);
        if (words.length > 0) {
            return words.slice(0, 3).join('-').toLowerCase().replace(/[^a-z0-9-]/g, '');
        }
        return 'generated-application';
    }
    async generateTechnicalSpecification(businessAnalysis, description, requirements) {
        const specPrompt = this.buildTechnicalSpecPrompt(businessAnalysis, description, requirements);
        try {
            const specResponse = await this.generateSingleLLMResponse(specPrompt, {
                temperature: 0.3,
                maxTokens: 4000
            });
            const technicalSpec = this.parseTechnicalSpecification(specResponse);
            // Enhance with business domain analysis
            technicalSpec.businessDomain = this.convertToLegacyBusinessDomain(businessAnalysis);
            return technicalSpec;
        }
        catch (error) {
            this.logError('Failed to generate technical specification', {
                error: error instanceof Error ? error.message : String(error)
            });
            throw new Error(`Technical specification generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    buildTechnicalSpecPrompt(businessAnalysis, description, requirements) {
        return `You are a world-class software architect creating a COMPREHENSIVE SYSTEM BLUEPRINT for production-ready applications.

MISSION: Generate a complete technical specification that eliminates ALL template dependencies and enables 100% AI-driven code generation.

BUSINESS DOMAIN ANALYSIS:
- Domain: ${businessAnalysis.domain}
- Confidence: ${businessAnalysis.confidence}
- Target: 90-95/100 production readiness score

ENTITIES (Complete Business Model):
${(businessAnalysis.entities || []).map(entity => `
- ${entity.name}: ${entity.description}
  Fields: ${(entity.fields || []).map(f => `${f.name} (${f.type})`).join(', ')}
  Operations: ${(entity.operations || []).map(op => op.name).join(', ')}
  Relationships: ${(entity.relationships || []).map(r => `${r.type} → ${r.target}`).join(', ')}
`).join('\n')}

WORKFLOWS (Business Process Flows):
${(businessAnalysis.workflows || []).map(workflow => `
- ${workflow.name}: ${workflow.description}
  Steps: ${(workflow.steps || []).map(step => step.name).join(' → ')}
  Entities: ${(workflow.entities || []).join(', ')}
`).join('\n')}

USER ROLES (Access Control Matrix):
${(businessAnalysis.userRoles || []).map(role => `
- ${role.name} (${role.accessLevel}): ${role.description}
  Permissions: ${(role.permissions || []).join(', ')}
`).join('\n')}

PROJECT DESCRIPTION: ${description}

REQUIREMENTS:
${requirements.map((req, index) => `${index + 1}. ${req}`).join('\n')}

BLUEPRINT GENERATION REQUIREMENTS:
1. Create COMPREHENSIVE specifications that eliminate ALL template dependencies
2. Generate PRECISE file structures with exact paths and purposes
3. Define DETAILED API contracts with request/response schemas
4. Specify COMPLETE database schemas with relationships and constraints
5. Include PRODUCTION-READY security, testing, and deployment configurations
6. Ensure specifications enable 90-95/100 production readiness scores
7. Design for ZERO manual intervention in code generation

COMPREHENSIVE BLUEPRINT REQUIREMENTS:
1. Project name must reflect the actual business domain with precise naming
2. Features must be domain-specific and comprehensive (10+ features minimum)
3. Architecture must be production-ready with security, performance, and scalability
4. API endpoints must cover ALL business entities with full CRUD + advanced operations
5. Database schema must include ALL entities, relationships, constraints, and indexes
6. File structure must be detailed with exact paths and purposes
7. Dependencies must be comprehensive and production-ready
8. Testing strategy must cover unit, integration, E2E, performance, and security tests
9. Security specifications must include authentication, authorization, encryption, and compliance
10. Deployment configuration must be container-ready with monitoring and scaling

CRITICAL JSON FORMATTING REQUIREMENTS:
- Return ONLY valid JSON, no markdown code blocks or explanations
- Use double quotes for all strings
- No trailing commas anywhere
- No comments in JSON
- Ensure all brackets and braces are properly closed
- Validate JSON syntax before responding
- Use consistent indentation

Generate a COMPREHENSIVE SYSTEM BLUEPRINT in this exact JSON format:

{
  "projectName": "domain-specific-name-kebab-case",
  "description": "Comprehensive description of the business domain and application purpose",
  "businessDomain": {
    "name": "Business Domain Name",
    "description": "Detailed business domain description",
    "entities": [
      {
        "name": "EntityName",
        "description": "Entity purpose and business logic",
        "fields": [
          {"name": "id", "type": "string", "required": true, "description": "Unique identifier"},
          {"name": "fieldName", "type": "string", "required": true, "description": "Field purpose"}
        ],
        "relationships": [
          {"type": "oneToMany", "target": "RelatedEntity", "description": "Relationship description"}
        ],
        "operations": [
          {"name": "create", "type": "create", "description": "Create new entity"},
          {"name": "findById", "type": "read", "description": "Find entity by ID"}
        ]
      }
    ],
    "workflows": [
      {
        "name": "Workflow Name",
        "description": "Complete workflow description",
        "steps": ["Step 1", "Step 2", "Step 3"],
        "entities": ["Entity1", "Entity2"]
      }
    ],
    "userRoles": [
      {
        "name": "Admin",
        "permissions": ["create", "read", "update", "delete"],
        "description": "Full system access"
      }
    ]
  },
  "architecture": {
    "frontend": {
      "framework": "React with TypeScript",
      "components": ["App", "Router", "Layout", "EntityList", "EntityForm", "EntityDetail"],
      "routing": true,
      "stateManagement": "Redux Toolkit with RTK Query",
      "styling": "Tailwind CSS with Headless UI",
      "testing": "Jest + React Testing Library",
      "bundler": "Vite"
    },
    "backend": {
      "framework": "Express.js with TypeScript",
      "database": "PostgreSQL with Prisma ORM",
      "authentication": "JWT with refresh tokens",
      "authorization": "Role-based access control (RBAC)",
      "apis": ["/api/auth/login", "/api/entities", "/api/entities/:id"],
      "middleware": ["cors", "helmet", "compression", "rate-limiting"],
      "validation": "Joi schema validation",
      "documentation": "OpenAPI/Swagger"
    },
    "deployment": {
      "containerization": "Docker with multi-stage builds",
      "environment": "production",
      "ports": [3000, 5000, 5432],
      "loadBalancer": "Nginx",
      "ssl": "Let's Encrypt certificates"
    }
  },
  "dependencies": {
    "frontend": ["react", "react-dom", "@types/react", "react-router-dom", "@reduxjs/toolkit", "react-redux", "axios", "tailwindcss", "@headlessui/react", "vite"],
    "backend": ["express", "@types/express", "prisma", "@prisma/client", "pg", "@types/pg", "jsonwebtoken", "@types/jsonwebtoken", "bcryptjs", "cors", "helmet", "joi"],
    "dev": ["typescript", "@types/node", "ts-node", "nodemon", "jest", "@types/jest", "supertest", "@types/supertest", "eslint", "prettier"]
  },
  "fileStructure": {
    "frontend": ["src/components/common", "src/components/layout", "src/pages", "src/store", "src/services", "src/types", "src/utils", "public"],
    "backend": ["src/controllers", "src/services", "src/models", "src/routes", "src/middleware", "src/utils", "src/types", "src/config", "prisma"],
    "root": ["docker-compose.yml", "Dockerfile", "README.md", ".env.example", "nginx.conf", "k8s"]
  },
  "features": ["User Authentication", "User Authorization", "Entity Management", "Search and Filter", "Dashboard", "Analytics", "API Documentation", "Error Handling"],
  "testingStrategy": ["Unit tests for all services", "Integration tests for APIs", "Component tests", "E2E user journey tests", "Performance tests", "Security tests", "80% code coverage minimum"]
}

VALIDATION CHECKLIST:
✓ All strings use double quotes
✓ No trailing commas
✓ All arrays and objects properly closed
✓ Valid JSON syntax
✓ No markdown formatting

Remember: Respond with ONLY the JSON object. No additional text or explanations.`;
    }
    convertToLegacyBusinessDomain(analysis) {
        const entities = analysis.entities || [];
        const workflows = analysis.workflows || [];
        const userRoles = analysis.userRoles || [];
        return {
            name: analysis.domain,
            description: `${analysis.domain} domain with ${entities.length} core entities`,
            entities: entities.map(entity => ({
                name: entity.name,
                description: entity.description,
                fields: (entity.fields || []).map(field => ({
                    name: field.name,
                    type: field.type,
                    required: field.required,
                    description: field.description
                })),
                relationships: (entity.relationships || []).map(rel => ({
                    type: rel.type,
                    target: rel.target,
                    description: rel.description
                })),
                operations: (entity.operations || []).map(op => ({
                    name: op.name,
                    type: op.type,
                    description: op.description,
                    parameters: (op.parameters || []).map(p => p.name)
                }))
            })),
            workflows: workflows.map(workflow => ({
                name: workflow.name,
                description: workflow.description,
                steps: (workflow.steps || []).map(step => step.name),
                entities: workflow.entities || []
            })),
            userRoles: userRoles.map(role => ({
                name: role.name,
                permissions: (role.permissions || []).map(p => p.resource),
                description: role.description
            }))
        };
    }
    buildPlanningPrompt(description, requirements) {
        return `
BUSINESS DOMAIN ANALYSIS AND TECHNICAL SPECIFICATION

Project Description: ${description}

Requirements:
${requirements.map((req, index) => `${index + 1}. ${req}`).join('\n')}

STEP 1: BUSINESS DOMAIN ANALYSIS
First, analyze the requirements to identify:
- Core business entities (what are the main "things" users work with?)
- Entity relationships (how do these entities relate to each other?)
- Business operations (what actions can users perform?)
- User workflows (what processes do users follow?)
- User roles and permissions

STEP 2: TECHNICAL SPECIFICATION
Based on your business domain analysis, create a technical specification.

CRITICAL RULES:
- Entity names must match the business domain (e.g., Task, Project, User for task management)
- API endpoints must reflect actual business operations (e.g., /api/tasks, /api/projects)
- Database tables must match identified entities
- Services must implement business logic for identified entities
- NEVER default to POS/retail patterns unless explicitly requested

IMPORTANT: You must respond with ONLY valid JSON. Do not include any text before or after the JSON.
The JSON must be properly formatted with correct syntax, proper quotes, and no trailing commas.

Provide the technical specification in this exact JSON format:

{
  "projectName": "task-management-platform",
  "description": "A comprehensive task management and team collaboration platform",
  "businessDomain": {
    "name": "Task Management",
    "description": "Platform for managing tasks, projects, and team collaboration",
    "entities": [
      {
        "name": "Task",
        "description": "Individual work items that can be assigned and tracked",
        "fields": [
          {"name": "id", "type": "UUID", "required": true, "description": "Unique identifier"},
          {"name": "title", "type": "string", "required": true, "description": "Task title"},
          {"name": "description", "type": "text", "required": false, "description": "Detailed description"},
          {"name": "status", "type": "enum", "required": true, "description": "Current status (todo, in_progress, done)"},
          {"name": "priority", "type": "enum", "required": true, "description": "Priority level (low, medium, high)"},
          {"name": "assignee_id", "type": "UUID", "required": false, "description": "Assigned user ID"},
          {"name": "project_id", "type": "UUID", "required": true, "description": "Parent project ID"},
          {"name": "due_date", "type": "timestamp", "required": false, "description": "Due date"},
          {"name": "created_at", "type": "timestamp", "required": true, "description": "Creation timestamp"},
          {"name": "updated_at", "type": "timestamp", "required": true, "description": "Last update timestamp"}
        ],
        "relationships": [
          {"type": "manyToOne", "target": "Project", "description": "Task belongs to a project"},
          {"type": "manyToOne", "target": "User", "description": "Task assigned to a user"}
        ],
        "operations": [
          {"name": "createTask", "type": "create", "description": "Create a new task"},
          {"name": "updateTask", "type": "update", "description": "Update task details"},
          {"name": "assignTask", "type": "custom", "description": "Assign task to user", "parameters": ["taskId", "userId"]},
          {"name": "changeStatus", "type": "custom", "description": "Change task status", "parameters": ["taskId", "status"]}
        ]
      },
      {
        "name": "Project",
        "description": "Container for organizing related tasks",
        "fields": [
          {"name": "id", "type": "UUID", "required": true, "description": "Unique identifier"},
          {"name": "name", "type": "string", "required": true, "description": "Project name"},
          {"name": "description", "type": "text", "required": false, "description": "Project description"},
          {"name": "owner_id", "type": "UUID", "required": true, "description": "Project owner user ID"},
          {"name": "status", "type": "enum", "required": true, "description": "Project status (active, completed, archived)"},
          {"name": "created_at", "type": "timestamp", "required": true, "description": "Creation timestamp"},
          {"name": "updated_at", "type": "timestamp", "required": true, "description": "Last update timestamp"}
        ],
        "relationships": [
          {"type": "oneToMany", "target": "Task", "description": "Project contains multiple tasks"},
          {"type": "manyToOne", "target": "User", "description": "Project owned by a user"}
        ],
        "operations": [
          {"name": "createProject", "type": "create", "description": "Create a new project"},
          {"name": "updateProject", "type": "update", "description": "Update project details"},
          {"name": "getProjectTasks", "type": "custom", "description": "Get all tasks in project", "parameters": ["projectId"]}
        ]
      }
    ],
    "workflows": [
      {
        "name": "Task Assignment Workflow",
        "description": "Process for assigning and tracking tasks",
        "steps": ["Create task", "Assign to user", "Track progress", "Mark complete"],
        "entities": ["Task", "User", "Project"]
      }
    ],
    "userRoles": [
      {
        "name": "ProjectManager",
        "permissions": ["create_project", "assign_tasks", "view_all_tasks"],
        "description": "Can manage projects and assign tasks"
      },
      {
        "name": "TeamMember",
        "permissions": ["view_assigned_tasks", "update_task_status"],
        "description": "Can work on assigned tasks"
      }
    ]
  },
  "architecture": {
    "frontend": {
      "framework": "React with TypeScript",
      "components": ["TaskList", "TaskCard", "ProjectDashboard", "TaskForm", "UserAuth"],
      "routing": true,
      "stateManagement": "Context API"
    },
    "backend": {
      "framework": "Express.js with TypeScript",
      "database": "PostgreSQL",
      "authentication": "JWT",
      "apis": ["/api/tasks", "/api/projects", "/api/auth", "/api/users"]
    },
    "deployment": {
      "containerization": "Docker",
      "environment": "production",
      "ports": [3000, 5000, 5432]
    }
  },
  "dependencies": {
    "frontend": ["react", "react-dom", "@types/react", "react-router-dom", "axios", "tailwindcss"],
    "backend": ["express", "@types/express", "cors", "helmet", "jsonwebtoken", "bcryptjs", "pg", "@types/pg"],
    "dev": ["typescript", "ts-node", "nodemon", "@types/node", "jest", "@types/jest", "supertest"]
  },
  "fileStructure": {
    "frontend": ["src/components", "src/pages", "src/hooks", "src/utils", "src/types", "public"],
    "backend": ["src/routes", "src/controllers", "src/models", "src/middleware", "src/utils", "src/types"],
    "root": ["docker-compose.yml", "Dockerfile", "README.md", ".env.example"]
  },
  "features": ["User authentication", "Task management", "Project organization", "Team collaboration", "Real-time updates"],
  "testingStrategy": ["unit tests", "integration tests", "end-to-end tests"]
}

Remember: Respond with ONLY the JSON object. No additional text or explanations.
`;
    }
    parseTechnicalSpecification(response) {
        this.jsonRepairAttempts = 0;
        try {
            this.logInfo('Starting enhanced JSON parsing with comprehensive error recovery');
            // Pre-validate the response before parsing
            this.preValidateResponse(response);
            // Try multiple parsing strategies with increasing fallback levels
            const parsed = this.parseWithRobustFallbacks(response);
            // Post-validate the parsed result
            this.postValidateParsedResult(parsed);
            const technicalSpec = this.buildTechnicalSpecFromParsed(parsed);
            this.logInfo('JSON parsing successful - comprehensive blueprint generated', {
                projectName: technicalSpec.projectName,
                entitiesCount: technicalSpec.businessDomain?.entities?.length || 0,
                featuresCount: technicalSpec.features?.length || 0
            });
            return technicalSpec;
        }
        catch (error) {
            this.logError('All JSON parsing strategies failed', {
                error: error instanceof Error ? error.message : 'Unknown error',
                repairAttempts: this.jsonRepairAttempts,
                responseLength: response.length
            });
            // Final fallback: Generate a comprehensive blueprint from text analysis
            return this.generateComprehensiveFallbackBlueprint(response);
        }
    }
    preValidateResponse(response) {
        // Check for common issues that would prevent parsing
        if (!response || response.trim().length === 0) {
            throw new Error('Empty response from LLM');
        }
        // Check for obvious non-JSON content
        if (!response.includes('{') || !response.includes('}')) {
            throw new Error('Response does not contain JSON object');
        }
        // Check for common markdown formatting that needs to be stripped
        if (response.includes('```json') || response.includes('```')) {
            this.logInfo('Response contains markdown formatting - will be cleaned');
        }
        // Check for obvious syntax issues
        const openBraces = (response.match(/\{/g) || []).length;
        const closeBraces = (response.match(/\}/g) || []).length;
        if (Math.abs(openBraces - closeBraces) > 2) {
            this.logInfo(`Potential brace mismatch detected: ${openBraces} open, ${closeBraces} close`);
        }
    }
    postValidateParsedResult(parsed) {
        // Validate that essential fields are present
        const requiredFields = ['projectName', 'description', 'architecture', 'dependencies', 'features'];
        const missingFields = requiredFields.filter(field => !parsed[field]);
        if (missingFields.length > 0) {
            throw new Error(`Parsed result missing required fields: ${missingFields.join(', ')}`);
        }
        // Validate architecture structure
        if (!parsed.architecture.frontend || !parsed.architecture.backend) {
            throw new Error('Architecture must include both frontend and backend configurations');
        }
        // Validate arrays are actually arrays
        if (!Array.isArray(parsed.features)) {
            throw new Error('Features must be an array');
        }
        if (!Array.isArray(parsed.dependencies?.frontend) && parsed.dependencies?.frontend !== undefined) {
            throw new Error('Frontend dependencies must be an array');
        }
        if (!Array.isArray(parsed.dependencies?.backend) && parsed.dependencies?.backend !== undefined) {
            throw new Error('Backend dependencies must be an array');
        }
    }
    parseWithRobustFallbacks(response) {
        const strategies = [
            { name: 'Clean JSON Parse', fn: () => this.parseCleanJson(response) },
            { name: 'Basic JSON Fixes', fn: () => this.parseWithBasicFixes(response) },
            { name: 'Advanced JSON Fixes', fn: () => this.parseWithAdvancedFixes(response) },
            { name: 'Enhanced JSON Repair', fn: () => this.parseWithEnhancedRepair(response) },
            { name: 'Manual JSON Repair', fn: () => this.parseWithManualRepair(response) },
            { name: 'AI-Assisted JSON Fix', fn: () => this.parseWithAIAssistance(response) },
            { name: 'Regex Extraction', fn: () => this.parseWithRegexExtraction(response) },
            { name: 'Intelligent Text Analysis', fn: () => this.parseWithIntelligentAnalysis(response) }
        ];
        for (let i = 0; i < strategies.length; i++) {
            try {
                this.logInfo(`Attempting JSON parsing strategy: ${strategies[i].name}`);
                const result = strategies[i].fn();
                this.logInfo(`✅ JSON parsing succeeded with strategy: ${strategies[i].name}`);
                return result;
            }
            catch (error) {
                this.jsonRepairAttempts++;
                this.logInfo(`❌ Strategy ${strategies[i].name} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                if (i === strategies.length - 1) {
                    this.logError('All JSON parsing strategies exhausted', {
                        totalStrategies: strategies.length,
                        repairAttempts: this.jsonRepairAttempts
                    });
                    throw error;
                }
            }
        }
    }
    parseCleanJson(response) {
        // Strategy 1: Try parsing the response as-is after basic cleanup
        let cleanResponse = response.trim();
        // Remove markdown code blocks
        cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
        // Find JSON object boundaries
        const startIndex = cleanResponse.indexOf('{');
        const lastIndex = cleanResponse.lastIndexOf('}');
        if (startIndex === -1 || lastIndex === -1 || startIndex >= lastIndex) {
            throw new Error('No valid JSON object found');
        }
        const jsonString = cleanResponse.substring(startIndex, lastIndex + 1);
        return JSON.parse(jsonString);
    }
    parseCleanJsonWithRepair(response) {
        // Enhanced version of parseCleanJson with automatic repair
        try {
            return this.parseCleanJson(response);
        }
        catch (error) {
            // If direct parsing fails, try repair
            this.logInfo('Direct JSON parsing failed, attempting automatic repair', {
                error: error instanceof Error ? error.message : String(error),
                responseLength: response.length
            });
            return this.parseWithManualRepair(response);
        }
    }
    parseWithBasicFixes(response) {
        // Strategy 2: Apply basic JSON fixes
        let cleanResponse = response.trim();
        cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
        const startIndex = cleanResponse.indexOf('{');
        const lastIndex = cleanResponse.lastIndexOf('}');
        if (startIndex === -1 || lastIndex === -1) {
            throw new Error('No JSON object boundaries found');
        }
        let jsonString = cleanResponse.substring(startIndex, lastIndex + 1);
        // Apply basic fixes
        jsonString = jsonString
            .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
            .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Add quotes to unquoted keys
            .replace(/:\s*'([^']*)'/g, ': "$1"'); // Replace single quotes with double quotes
        return JSON.parse(jsonString);
    }
    parseWithAdvancedFixes(response) {
        // Strategy 3: Apply advanced JSON fixes
        let cleanResponse = response.trim();
        cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
        const startIndex = cleanResponse.indexOf('{');
        const lastIndex = cleanResponse.lastIndexOf('}');
        if (startIndex === -1 || lastIndex === -1) {
            throw new Error('No JSON object boundaries found');
        }
        let jsonString = cleanResponse.substring(startIndex, lastIndex + 1);
        // Advanced fixes
        jsonString = jsonString
            .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
            .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Add quotes to unquoted keys
            .replace(/:\s*'([^']*)'/g, ': "$1"') // Replace single quotes with double quotes
            .replace(/\n/g, ' ') // Remove newlines
            .replace(/\t/g, ' ') // Remove tabs
            .replace(/\s+/g, ' ') // Normalize whitespace
            .replace(/,\s*}/g, '}') // Remove trailing commas before closing braces
            .replace(/,\s*]/g, ']') // Remove trailing commas before closing brackets
            .replace(/([^\\])\\([^"\\\/bfnrt])/g, '$1\\\\$2') // Fix unescaped backslashes
            .replace(/([^\\])"/g, '$1\\"') // Fix unescaped quotes in strings
            .replace(/^"|"$/g, '') // Remove surrounding quotes if present
            .replace(/\\"([^"]*)\\":/g, '"$1":'); // Fix over-escaped property names
        return JSON.parse(jsonString);
    }
    parseWithEnhancedRepair(response) {
        // Strategy 4: Enhanced JSON repair with comprehensive fixes
        let cleanResponse = response.trim();
        // Remove all markdown formatting
        cleanResponse = cleanResponse
            .replace(/```json\s*/gi, '')
            .replace(/```\s*/g, '')
            .replace(/^```/gm, '')
            .replace(/```$/gm, '');
        // Find the main JSON object
        const startIndex = cleanResponse.indexOf('{');
        const lastIndex = cleanResponse.lastIndexOf('}');
        if (startIndex === -1 || lastIndex === -1) {
            throw new Error('No JSON object boundaries found in enhanced repair');
        }
        let jsonString = cleanResponse.substring(startIndex, lastIndex + 1);
        // Comprehensive JSON repairs
        jsonString = jsonString
            // Fix common syntax issues
            .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
            .replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)(\s*):/g, '$1"$2"$3:') // Quote unquoted keys
            .replace(/:\s*'([^']*)'/g, ': "$1"') // Replace single quotes with double quotes
            .replace(/:\s*`([^`]*)`/g, ': "$1"') // Replace backticks with double quotes
            // Fix array and object formatting
            .replace(/\[\s*,/g, '[') // Remove leading commas in arrays
            .replace(/,\s*,/g, ',') // Remove duplicate commas
            .replace(/{\s*,/g, '{') // Remove leading commas in objects
            // Fix string escaping
            .replace(/\\(?!["\\/bfnrt])/g, '\\\\') // Escape unescaped backslashes
            .replace(/(?<!\\)"/g, '\\"') // Escape unescaped quotes in values
            .replace(/\\"([^"]*)\\":/g, '"$1":') // Fix over-escaped property names
            // Normalize whitespace
            .replace(/\s+/g, ' ')
            .trim();
        return JSON.parse(jsonString);
    }
    async parseWithAIAssistance(response) {
        // Strategy 4: Use AI to fix malformed JSON with intelligent chunking
        // If response is too large, try chunked approach
        if (response.length > 15000) {
            return this.parseWithChunkedAIAssistance(response);
        }
        const fixPrompt = `The following text contains a malformed JSON object. Please fix it and return only the valid JSON:

${response}

Requirements:
- Return only valid JSON, no explanations
- Preserve all original data
- Fix syntax errors (missing quotes, trailing commas, etc.)
- Ensure proper JSON structure
- If the JSON is truncated, complete it with proper closing brackets/braces`;
        try {
            const fixedResponse = await this.generateSingleLLMResponse(fixPrompt, {
                temperature: 0.1,
                maxTokens: 8000 // Increased token limit
            });
            return this.parseCleanJsonWithRepair(fixedResponse);
        }
        catch (error) {
            throw new Error(`AI-assisted JSON fixing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async parseWithChunkedAIAssistance(response) {
        // For very large responses, try to identify the core structure first
        const corePrompt = `Extract and fix only the main structure of this JSON, focusing on the root object and main arrays. Return a simplified but valid JSON:

${response.substring(0, 10000)}...

Requirements:
- Return only the main JSON structure
- Include key arrays but with minimal items
- Ensure valid JSON syntax
- Focus on preserving the overall schema`;
        try {
            const coreResponse = await this.generateSingleLLMResponse(corePrompt, {
                temperature: 0.1,
                maxTokens: 6000
            });
            return this.parseCleanJsonWithRepair(coreResponse);
        }
        catch (error) {
            // If chunked approach fails, try manual repair
            return this.parseWithManualRepair(response);
        }
    }
    parseWithManualRepair(response) {
        // Strategy 7: Manual JSON repair for large responses
        let cleanResponse = response.trim();
        // Remove markdown code blocks
        cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
        // Find JSON boundaries
        const startIndex = cleanResponse.indexOf('{');
        if (startIndex === -1) {
            throw new Error('No JSON object start found');
        }
        let jsonString = cleanResponse.substring(startIndex);
        // Count brackets and braces to find proper ending
        let braceCount = 0;
        let bracketCount = 0;
        let inString = false;
        let escaped = false;
        let lastValidIndex = -1;
        for (let i = 0; i < jsonString.length; i++) {
            const char = jsonString[i];
            if (escaped) {
                escaped = false;
                continue;
            }
            if (char === '\\') {
                escaped = true;
                continue;
            }
            if (char === '"') {
                inString = !inString;
                continue;
            }
            if (!inString) {
                if (char === '{') {
                    braceCount++;
                }
                else if (char === '}') {
                    braceCount--;
                    if (braceCount === 0) {
                        lastValidIndex = i;
                        break;
                    }
                }
                else if (char === '[') {
                    bracketCount++;
                }
                else if (char === ']') {
                    bracketCount--;
                }
            }
        }
        if (lastValidIndex > 0) {
            jsonString = jsonString.substring(0, lastValidIndex + 1);
        }
        else {
            // If we can't find proper ending, try to close it
            jsonString = this.forceCloseJSON(jsonString);
        }
        // Remove trailing commas and fix common issues
        jsonString = this.cleanupJSONString(jsonString);
        try {
            return JSON.parse(jsonString);
        }
        catch (error) {
            this.logError('Manual JSON repair failed', {
                error: error instanceof Error ? error.message : String(error),
                jsonLength: jsonString.length,
                sample: jsonString.substring(0, 200)
            });
            throw new Error(`Manual JSON repair failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    forceCloseJSON(jsonString) {
        // Count unclosed brackets and braces
        let braceCount = 0;
        let bracketCount = 0;
        let inString = false;
        let escaped = false;
        for (let i = 0; i < jsonString.length; i++) {
            const char = jsonString[i];
            if (escaped) {
                escaped = false;
                continue;
            }
            if (char === '\\') {
                escaped = true;
                continue;
            }
            if (char === '"') {
                inString = !inString;
                continue;
            }
            if (!inString) {
                if (char === '{')
                    braceCount++;
                else if (char === '}')
                    braceCount--;
                else if (char === '[')
                    bracketCount++;
                else if (char === ']')
                    bracketCount--;
            }
        }
        // Close unclosed strings if needed
        if (inString) {
            jsonString += '"';
        }
        // Close unclosed arrays
        for (let i = 0; i < bracketCount; i++) {
            jsonString += ']';
        }
        // Close unclosed objects
        for (let i = 0; i < braceCount; i++) {
            jsonString += '}';
        }
        return jsonString;
    }
    cleanupJSONString(jsonString) {
        // Remove trailing commas before closing brackets/braces
        jsonString = jsonString.replace(/,(\s*[\]\}])/g, '$1');
        // Fix common JSON issues
        jsonString = jsonString.replace(/,(\s*,)/g, ','); // Remove duplicate commas
        jsonString = jsonString.replace(/\n/g, ' '); // Replace newlines with spaces
        jsonString = jsonString.replace(/\s+/g, ' '); // Normalize whitespace
        return jsonString;
    }
    parseWithRegexExtraction(response) {
        // Strategy 8: Extract JSON using enhanced regex patterns
        const patterns = [
            /\{[\s\S]*\}/g, // Match any content between outermost braces
            /"projectName"[\s\S]*?"features"[\s\S]*?\]/g, // Match from projectName to features array
            /"architecture"[\s\S]*?"deployment"[\s\S]*?\}/g, // Match architecture section
            /"businessDomain"[\s\S]*?"testingStrategy"[\s\S]*?\]/g, // Match business domain section
            /\{[^{}]*"projectName"[^{}]*\}/g // Match simple object with projectName
        ];
        for (const pattern of patterns) {
            const matches = response.match(pattern);
            if (matches && matches.length > 0) {
                try {
                    const candidate = matches[0];
                    return this.parseWithEnhancedRepair(candidate);
                }
                catch (error) {
                    continue;
                }
            }
        }
        throw new Error('No valid JSON found with regex extraction');
    }
    parseWithIntelligentAnalysis(response) {
        // Strategy 7: Intelligent text analysis to extract structured data
        this.logInfo('Attempting intelligent text analysis for JSON extraction');
        try {
            // Extract key information using natural language processing
            const projectName = this.extractProjectNameFromText(response) || 'intelligent-app';
            const description = this.extractDescriptionFromText(response) || 'AI-generated application';
            const features = this.extractFeaturesFromText(response);
            const entities = this.extractEntitiesFromText(response);
            const apis = this.extractAPIsFromText(response);
            // Build a comprehensive specification from extracted data
            return {
                projectName,
                description,
                businessDomain: {
                    name: projectName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    description,
                    entities: entities.map(entity => ({
                        name: entity,
                        description: `${entity} entity for ${projectName}`,
                        fields: this.generateDefaultFields(entity),
                        relationships: [],
                        operations: ['create', 'read', 'update', 'delete'].map(op => ({
                            name: op,
                            type: op,
                            description: `${op.charAt(0).toUpperCase() + op.slice(1)} ${entity}`
                        }))
                    })),
                    workflows: this.generateWorkflowsFromFeatures(features),
                    userRoles: this.generateUserRoles(features)
                },
                architecture: this.generateComprehensiveArchitecture(features, entities),
                dependencies: this.generateComprehensiveDependencies(features),
                fileStructure: this.generateProductionFileStructure(entities, features),
                features,
                testingStrategy: this.generateComprehensiveTestingStrategy(features, entities)
            };
        }
        catch (error) {
            throw new Error(`Intelligent analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    generateComprehensiveFallbackBlueprint(response) {
        // Final fallback: Generate comprehensive blueprint from text analysis
        this.logInfo('Generating comprehensive fallback blueprint from response analysis');
        const projectName = this.extractProjectNameFromText(response) || 'comprehensive-app';
        const description = this.extractDescriptionFromText(response) || 'AI-generated comprehensive application';
        const features = this.extractFeaturesFromText(response);
        const entities = this.extractEntitiesFromText(response);
        const apis = this.extractAPIsFromText(response);
        const businessDomain = {
            name: projectName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            description,
            entities: entities.map(entity => ({
                name: entity,
                description: `${entity} entity with comprehensive business logic`,
                fields: this.generateComprehensiveFields(entity),
                relationships: this.generateEntityRelationships(entity, entities),
                operations: this.generateComprehensiveOperations(entity)
            })),
            workflows: this.generateComprehensiveWorkflows(features, entities),
            userRoles: this.generateComprehensiveUserRoles(features)
        };
        return {
            projectName,
            description,
            businessDomain,
            architecture: this.generateProductionArchitecture(features, entities),
            dependencies: this.generateComprehensiveDependencies(features),
            fileStructure: this.generateProductionFileStructure(entities, features),
            features: features.length > 0 ? features : this.generateDefaultFeatures(entities),
            testingStrategy: this.generateComprehensiveTestingStrategy(features, entities)
        };
    }
    extractProjectNameFromText(text) {
        // Try to extract project name from various patterns
        const patterns = [
            /"projectName":\s*"([^"]+)"/,
            /project.*?name.*?[:"]\s*([^",\n]+)/i,
            /name.*?[:"]\s*([^",\n]+)/i
        ];
        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return 'generated-application';
    }
    extractDescriptionFromText(text) {
        // Try to extract description from various patterns
        const patterns = [
            /"description":\s*"([^"]+)"/,
            /description.*?[:"]\s*([^",\n]+)/i
        ];
        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return 'Generated application';
    }
    extractFeaturesFromText(text) {
        // Try to extract features from various patterns
        const patterns = [
            /"features":\s*\[([\s\S]*?)\]/,
            /features.*?:\s*\[([\s\S]*?)\]/i
        ];
        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                try {
                    const featuresText = match[1];
                    const features = featuresText
                        .split(',')
                        .map(f => f.trim().replace(/['"]/g, ''))
                        .filter(f => f.length > 0);
                    return features;
                }
                catch (error) {
                    continue;
                }
            }
        }
        return ['User authentication', 'Data management', 'API endpoints'];
    }
    buildTechnicalSpecFromParsed(parsed) {
        // Ensure all required fields are present with defaults
        const spec = {
            projectName: parsed.projectName || 'generated-app',
            description: parsed.description || 'Generated application',
            businessDomain: parsed.businessDomain || {
                name: 'Generic Application',
                description: 'A generic application domain',
                entities: [],
                workflows: [],
                userRoles: []
            },
            architecture: {
                frontend: {
                    framework: parsed.architecture?.frontend?.framework || 'React with TypeScript',
                    components: parsed.architecture?.frontend?.components || ['App', 'Header', 'Footer'],
                    routing: parsed.architecture?.frontend?.routing !== false,
                    stateManagement: parsed.architecture?.frontend?.stateManagement || 'Context API',
                },
                backend: {
                    framework: parsed.architecture?.backend?.framework || 'Express.js with TypeScript',
                    database: parsed.architecture?.backend?.database || 'PostgreSQL',
                    authentication: parsed.architecture?.backend?.authentication || 'JWT',
                    apis: parsed.architecture?.backend?.apis || ['/api/auth', '/api/users'],
                },
                deployment: {
                    containerization: parsed.architecture?.deployment?.containerization || 'Docker',
                    environment: parsed.architecture?.deployment?.environment || 'production',
                    ports: parsed.architecture?.deployment?.ports || [3000, 5000, 5432],
                },
            },
            dependencies: {
                frontend: this.ensureArray(parsed.dependencies?.frontend) ?? (
                // Only provide default frontend deps if frontend framework is not API-only
                (parsed.architecture?.frontend?.framework?.toLowerCase()?.includes('none') ||
                    parsed.architecture?.frontend?.framework?.toLowerCase()?.includes('api only'))
                    ? []
                    : ['react', 'react-dom', '@types/react', 'react-router-dom', 'axios', 'tailwindcss']),
                backend: this.ensureArray(parsed.dependencies?.backend) ?? [
                    'express', '@types/express', 'cors', 'helmet', 'jsonwebtoken', 'bcryptjs', 'pg', '@types/pg'
                ],
                dev: this.ensureArray(parsed.dependencies?.dev) ?? [
                    'typescript', 'ts-node', 'nodemon', '@types/node', 'jest', '@types/jest', 'supertest'
                ],
            },
            fileStructure: parsed.fileStructure || {
                frontend: ['src/components', 'src/pages', 'src/hooks', 'src/utils', 'src/types', 'public'],
                backend: ['src/routes', 'src/controllers', 'src/models', 'src/middleware', 'src/utils', 'src/types'],
                root: ['docker-compose.yml', 'Dockerfile', 'README.md', '.env.example'],
            },
            features: this.normalizeFeatures(parsed.features || parsed),
            testingStrategy: parsed.testingStrategy || ['unit tests', 'integration tests'],
        };
        return spec;
    }
    validateTechnicalSpecification(spec) {
        const requiredFields = ['projectName', 'description', 'architecture', 'dependencies', 'features'];
        for (const field of requiredFields) {
            if (!spec[field]) {
                throw new Error(`Missing required field in technical specification: ${field}`);
            }
        }
        if (!spec.architecture.frontend.framework || !spec.architecture.backend.framework) {
            throw new Error('Frontend and backend frameworks must be specified');
        }
        // For API-only projects, frontend dependencies can be empty
        if (!spec.dependencies.backend || spec.dependencies.backend.length === 0) {
            throw new Error('Backend dependencies must be specified');
        }
        // If frontend framework is specified (not "None" or "API Only"), require frontend dependencies
        const frontendFramework = spec.architecture.frontend.framework?.toLowerCase() || '';
        const isApiOnly = frontendFramework.includes('none') || frontendFramework.includes('api only');
        if (!isApiOnly && (!spec.dependencies.frontend || spec.dependencies.frontend.length === 0)) {
            throw new Error('Frontend dependencies must be specified when frontend framework is used');
        }
        if (spec.features.length === 0) {
            throw new Error('At least one feature must be specified');
        }
    }
    normalizeFeatures(features) {
        if (!features) {
            return ['User authentication', 'CRUD operations', 'Responsive design'];
        }
        // Handle object with feature properties
        if (typeof features === 'object' && !Array.isArray(features)) {
            const featureList = [];
            // Extract features from object properties
            Object.values(features).forEach(feature => {
                if (typeof feature === 'string') {
                    featureList.push(feature);
                }
                else if (feature && typeof feature === 'object') {
                    const featureObj = feature;
                    const featureName = featureObj.name || featureObj.title || featureObj.description || String(feature);
                    featureList.push(featureName);
                }
            });
            return featureList.length > 0 ? featureList : ['User authentication', 'CRUD operations', 'Responsive design'];
        }
        // Handle array of features
        if (Array.isArray(features)) {
            const normalized = features.map(feature => {
                if (typeof feature === 'string') {
                    return feature;
                }
                else if (feature && typeof feature === 'object') {
                    return feature.name || feature.title || feature.description || String(feature);
                }
                else {
                    return String(feature);
                }
            });
            return normalized.length > 0 ? normalized : ['User authentication', 'CRUD operations', 'Responsive design'];
        }
        // Fallback
        return ['User authentication', 'CRUD operations', 'Responsive design'];
    }
    ensureArray(value) {
        if (Array.isArray(value)) {
            const filtered = value.filter(item => typeof item === 'string');
            return filtered.length > 0 ? filtered : null;
        }
        if (typeof value === 'string') {
            return [value];
        }
        return null;
    }
    // Comprehensive Blueprint Generation Methods
    extractEntitiesFromText(text) {
        const entityPatterns = [
            /\b(user|customer|product|order|item|category|payment|invoice|account|profile|admin|manager|employee|client|vendor|supplier|inventory|stock|transaction|report|dashboard|notification|message|comment|review|rating|tag|label|setting|configuration|log|audit|session|token|role|permission|group|team|department|organization|company|address|contact|phone|email|document|file|image|video|media|content|page|post|article|blog|news|event|calendar|schedule|appointment|booking|reservation|ticket|task|project|milestone|goal|objective|target|metric|analytics|statistic|chart|graph|table|list|form|field|input|output|search|filter|sort|pagination|navigation|menu|sidebar|header|footer|layout|theme|style|template|component|widget|module|plugin|extension|integration|api|endpoint|service|controller|model|view|route|middleware|guard|interceptor|validator|transformer|serializer|deserializer|parser|formatter|converter|mapper|adapter|factory|builder|manager|handler|processor|executor|scheduler|queue|job|worker|task|thread|process|daemon|service|server|client|database|table|column|row|record|entity|relation|association|connection|link|reference|foreign|key|index|constraint|trigger|procedure|function|query|statement|command|operation|action|method|function|class|interface|type|enum|constant|variable|parameter|argument|return|result|response|request|data|payload|body|header|cookie|session|cache|storage|memory|disk|file|directory|folder|path|url|uri|link|href|src|alt|title|name|id|uuid|guid|hash|checksum|signature|certificate|key|secret|password|salt|token|refresh|access|bearer|basic|digest|oauth|saml|ldap|sso|mfa|2fa|captcha|recaptcha|csrf|xss|sql|injection|vulnerability|security|encryption|decryption|hashing|signing|verification|authentication|authorization|permission|role|scope|claim|policy|rule|condition|expression|formula|calculation|computation|algorithm|logic|flow|workflow|process|pipeline|stage|step|phase|state|status|flag|boolean|string|number|integer|float|double|decimal|date|time|datetime|timestamp|duration|interval|period|range|limit|offset|page|size|count|total|sum|average|min|max|median|mode|variance|deviation|correlation|regression|classification|clustering|prediction|recommendation|suggestion|hint|tip|help|guide|tutorial|documentation|manual|readme|changelog|license|copyright|author|contributor|maintainer|owner|admin|moderator|editor|viewer|guest|anonymous|public|private|protected|internal|external|local|remote|global|regional|national|international|worldwide|universal|general|specific|custom|default|standard|premium|pro|enterprise|business|personal|individual|group|team|organization|company|corporation|startup|nonprofit|government|education|healthcare|finance|banking|insurance|retail|ecommerce|marketplace|platform|network|social|media|entertainment|gaming|sports|travel|hospitality|real|estate|automotive|manufacturing|logistics|shipping|delivery|transportation|energy|utilities|telecommunications|technology|software|hardware|mobile|web|desktop|cloud|saas|paas|iaas|api|sdk|framework|library|package|module|plugin|extension|tool|utility|helper|service|microservice|monolith|serverless|container|docker|kubernetes|aws|azure|gcp|firebase|mongodb|postgresql|mysql|redis|elasticsearch|kafka|rabbitmq|nginx|apache|nodejs|python|java|csharp|php|ruby|go|rust|typescript|javascript|html|css|sass|less|react|vue|angular|svelte|nextjs|nuxtjs|gatsby|express|fastify|nestjs|django|flask|spring|laravel|rails|symfony|codeigniter|wordpress|drupal|joomla|magento|shopify|woocommerce|prestashop|opencart|bigcommerce|squarespace|wix|webflow|bubble|nocode|lowcode|automation|integration|webhook|cron|scheduler|queue|background|async|sync|realtime|streaming|websocket|sse|polling|push|pull|fetch|ajax|xhr|graphql|rest|soap|grpc|json|xml|yaml|csv|pdf|excel|word|powerpoint|image|video|audio|file|upload|download|import|export|backup|restore|migration|deployment|cicd|devops|testing|unit|integration|e2e|performance|load|stress|security|penetration|vulnerability|monitoring|logging|alerting|notification|dashboard|analytics|reporting|metrics|kpi|roi|conversion|funnel|cohort|retention|churn|acquisition|engagement|satisfaction|feedback|survey|poll|quiz|form|questionnaire|interview|focus|group|usability|ux|ui|design|prototype|wireframe|mockup|sketch|figma|adobe|photoshop|illustrator|indesign|canva|brand|logo|color|font|typography|layout|grid|responsive|mobile|tablet|desktop|accessibility|seo|sem|ppc|cpc|cpm|ctr|cpa|roas|ltv|arpu|mrr|arr|churn|growth|scale|performance|optimization|speed|latency|throughput|bandwidth|cpu|memory|storage|network|security|compliance|gdpr|ccpa|hipaa|sox|pci|iso|audit|risk|governance|policy|procedure|standard|best|practice|guideline|recommendation|suggestion|tip|trick|hack|workaround|solution|fix|patch|update|upgrade|version|release|deployment|rollback|hotfix|bugfix|feature|enhancement|improvement|optimization|refactoring|cleanup|maintenance|support|help|documentation|training|onboarding|tutorial|guide|manual|faq|troubleshooting|debugging|testing|validation|verification|quality|assurance|control|review|approval|feedback|iteration|agile|scrum|kanban|waterfall|lean|devops|continuous|integration|delivery|deployment|monitoring|observability|telemetry|tracing|profiling|benchmarking|load|testing|stress|testing|chaos|engineering|disaster|recovery|backup|restore|high|availability|fault|tolerance|resilience|scalability|elasticity|auto|scaling|load|balancing|caching|cdn|edge|computing|serverless|microservices|api|gateway|service|mesh|container|orchestration|kubernetes|docker|vm|bare|metal|cloud|hybrid|multi|public|private|on|premise|datacenter|region|zone|availability|redundancy|replication|sharding|partitioning|clustering|federation|synchronization|consistency|eventual|strong|weak|acid|base|cap|theorem|nosql|sql|relational|document|key|value|graph|time|series|search|index|full|text|geospatial|vector|embedding|machine|learning|ai|artificial|intelligence|deep|learning|neural|network|nlp|computer|vision|recommendation|system|personalization|automation|workflow|orchestration|pipeline|etl|data|warehouse|lake|mart|mining|science|analytics|business|intelligence|reporting|dashboard|visualization|chart|graph|table|pivot|slice|dice|drill|down|up|across|kpi|metric|dimension|measure|fact|star|schema|snowflake|kimball|inmon|data|governance|quality|lineage|catalog|discovery|profiling|cleansing|transformation|enrichment|validation|monitoring|alerting|incident|response|sla|slo|sli|error|budget|postmortem|retrospective|lessons|learned|improvement|action|plan|roadmap|strategy|vision|mission|goal|objective|okr|kpi|metric|target|milestone|deadline|timeline|schedule|resource|allocation|capacity|planning|estimation|effort|complexity|risk|assessment|mitigation|contingency|plan|stakeholder|communication|collaboration|coordination|alignment|consensus|decision|making|governance|oversight|compliance|audit|review|approval|sign|off|go|live|launch|release|rollout|deployment|cutover|migration|transition|change|management|training|support|maintenance|operations|monitoring|troubleshooting|incident|response|escalation|resolution|closure|feedback|improvement|optimization|enhancement|feature|request|bug|report|issue|ticket|backlog|sprint|epic|story|task|subtask|acceptance|criteria|definition|done|ready|review|testing|qa|uat|staging|production|environment|configuration|setup|installation|deployment|monitoring|logging|alerting|backup|restore|disaster|recovery|business|continuity|risk|management|security|compliance|governance|policy|procedure|standard|guideline|best|practice|framework|methodology|process|workflow|automation|integration|api|service|microservice|component|module|library|package|dependency|version|control|git|branch|merge|pull|request|code|review|ci|cd|pipeline|build|test|deploy|release|artifact|repository|registry|package|manager|npm|yarn|pip|composer|maven|gradle|nuget|gem|cargo|go|mod|docker|image|container|registry|kubernetes|helm|chart|operator|crd|namespace|pod|service|ingress|configmap|secret|volume|pvc|pv|storage|class|node|cluster|master|worker|etcd|api|server|controller|scheduler|kubelet|proxy|dns|network|policy|rbac|service|account|role|binding|cluster|role|admission|controller|webhook|mutating|validating|custom|resource|definition|operator|helm|chart|tiller|release|upgrade|rollback|history|status|get|describe|logs|exec|port|forward|proxy|tunnel|context|config|current|switch|namespace|default|system|public|monitoring|logging|prometheus|grafana|alertmanager|jaeger|zipkin|fluentd|elasticsearch|kibana|logstash|beats|apm|tracing|metrics|logs|events|alerts|dashboards|queries|rules|targets|scrape|push|pull|federation|remote|write|read|storage|retention|compaction|backup|restore|snapshot|disaster|recovery|high|availability|clustering|replication|sharding|partitioning|load|balancing|auto|scaling|horizontal|vertical|pod|autoscaler|cluster|autoscaler|vertical|pod|autoscaler|custom|metrics|external|metrics|resource|metrics|hpa|vpa|ca|pdb|pod|disruption|budget|priority|class|qos|guaranteed|burstable|besteffort|limit|range|resource|quota|network|policy|security|context|pod|security|policy|admission|controller|opa|gatekeeper|falco|twistlock|aqua|sysdig|datadog|newrelic|dynatrace|appdynamics|splunk|sumologic|loggly|papertrail|rollbar|sentry|bugsnag|honeybadger|airbrake|raygun|crashlytics|firebase|analytics|mixpanel|amplitude|segment|heap|fullstory|hotjar|crazy|egg|optimizely|google|analytics|tag|manager|facebook|pixel|linkedin|insight|twitter|analytics|pinterest|analytics|snapchat|analytics|tiktok|analytics|youtube|analytics|instagram|analytics|whatsapp|business|telegram|analytics|discord|analytics|slack|analytics|teams|analytics|zoom|analytics|webex|analytics|gotomeeting|analytics|skype|analytics|hangouts|analytics|meet|analytics|duo|analytics|facetime|analytics|messenger|analytics|viber|analytics|line|analytics|wechat|analytics|kakaotalk|analytics|signal|analytics|threema|analytics|wickr|analytics|element|analytics|matrix|analytics|riot|analytics|discord|analytics|teamspeak|analytics|mumble|analytics|ventrilo|analytics|roger|wilco|analytics|steam|analytics|origin|analytics|uplay|analytics|epic|games|analytics|battle|net|analytics|gog|analytics|itch|io|analytics|humble|bundle|analytics|green|man|gaming|analytics|fanatical|analytics|chrono|gg|analytics|indiegala|analytics|bundle|stars|analytics|groupees|analytics|indie|royale|analytics|pay|what|you|want|analytics|humble|choice|analytics|xbox|game|pass|analytics|playstation|now|analytics|nintendo|switch|online|analytics|stadia|analytics|geforce|now|analytics|shadow|analytics|parsec|analytics|moonlight|analytics|steam|link|analytics|nvidia|shield|analytics|apple|tv|analytics|roku|analytics|chromecast|analytics|fire|tv|analytics|android|tv|analytics|smart|tv|analytics|samsung|tv|analytics|lg|tv|analytics|sony|tv|analytics|panasonic|tv|analytics|philips|tv|analytics|tcl|tv|analytics|hisense|tv|analytics|vizio|tv|analytics|insignia|tv|analytics|toshiba|tv|analytics|sharp|tv|analytics|jvc|tv|analytics|mitsubishi|tv|analytics|pioneer|tv|analytics|yamaha|tv|analytics|denon|tv|analytics|marantz|tv|analytics|onkyo|tv|analytics|harman|kardon|analytics|bose|analytics|sonos|analytics|klipsch|analytics|polk|audio|analytics|definitive|technology|analytics|martin|logan|analytics|magnolia|analytics|mcintosh|analytics|audio|research|analytics|krell|analytics|mark|levinson|analytics|theta|digital|analytics|bryston|analytics|parasound|analytics|rotel|analytics|cambridge|audio|analytics|nad|analytics|rega|analytics|pro|ject|analytics|music|hall|analytics|clearaudio|analytics|vpi|analytics|technics|analytics|pioneer|dj|analytics|numark|analytics|denon|dj|analytics|rane|analytics|allen|heath|analytics|mackie|analytics|behringer|analytics|yamaha|analytics|roland|analytics|korg|analytics|moog|analytics|sequential|analytics|dave|smith|analytics|oberheim|analytics|prophet|analytics|juno|analytics|jupiter|analytics|sh|analytics|tr|analytics|tb|analytics|mc|analytics|sp|analytics|mpc|analytics|maschine|analytics|push|analytics|live|analytics|logic|analytics|pro|tools|analytics|cubase|analytics|nuendo|analytics|reaper|analytics|studio|one|analytics|fl|studio|analytics|reason|analytics|bitwig|analytics|garageband|analytics|audacity|analytics|hindenburg|analytics|adobe|audition|analytics|soundforge|analytics|wavelab|analytics|izotope|analytics|fabfilter|analytics|waves|analytics|universal|audio|analytics|plugin|alliance|analytics|slate|digital|analytics|softube|analytics|eventide|analytics|lexicon|analytics|tc|electronic|analytics|strymon|analytics|empress|analytics|chase|bliss|analytics|meris|analytics|source|audio|analytics|erica|synths|analytics|make|noise|analytics|mutable|instruments|analytics|intellijel|analytics|mannequins|analytics|monome|analytics|teenage|engineering|analytics|elektron|analytics|arturia|analytics|novation|analytics|akai|analytics|native|instruments|analytics|ableton|analytics|steinberg|analytics|presonus|analytics|avid|analytics|apple|analytics|microsoft|analytics|google|analytics|amazon|analytics|facebook|analytics|twitter|analytics|linkedin|analytics|youtube|analytics|instagram|analytics|tiktok|analytics|snapchat|analytics|pinterest|analytics|reddit|analytics|discord|analytics|slack|analytics|teams|analytics|zoom|analytics|skype|analytics|whatsapp|analytics|telegram|analytics|signal|analytics|element|analytics|matrix|analytics|riot|analytics|mastodon|analytics|diaspora|analytics|friendica|analytics|hubzilla|analytics|pleroma|analytics|peertube|analytics|pixelfed|analytics|writefreely|analytics|plume|analytics|lemmy|analytics|kbin|analytics|tildes|analytics|hacker|news|analytics|lobsters|analytics|slashdot|analytics|digg|analytics|stumbleupon|analytics|delicious|analytics|pocket|analytics|instapaper|analytics|readwise|analytics|goodreads|analytics|bookbub|analytics|scribd|analytics|kindle|unlimited|analytics|audible|analytics|spotify|analytics|apple|music|analytics|youtube|music|analytics|amazon|music|analytics|tidal|analytics|deezer|analytics|pandora|analytics|soundcloud|analytics|bandcamp|analytics|mixcloud|analytics|last|fm|analytics|discogs|analytics|musicbrainz|analytics|allmusic|analytics|rateyourmusic|analytics|genius|analytics|azlyrics|analytics|metrolyrics|analytics|lyricsgenius|analytics|musixmatch|analytics|shazam|analytics|soundhound|analytics|tunein|analytics|iheartradio|analytics|radio|com|analytics|npr|analytics|bbc|analytics|cbc|analytics|abc|analytics|nbc|analytics|cbs|analytics|fox|analytics|cnn|analytics|msnbc|analytics|fox|news|analytics|bloomberg|analytics|reuters|analytics|ap|news|analytics|wall|street|journal|analytics|new|york|times|analytics|washington|post|analytics|guardian|analytics|bbc|news|analytics|cnn|analytics|al|jazeera|analytics|rt|analytics|dw|analytics|france|analytics|euronews|analytics|sky|news|analytics|itv|news|analytics|channel|analytics|news|analytics|abc|news|analytics|cbs|news|analytics|nbc|news|analytics|pbs|news|analytics|npr|news|analytics|c|span|analytics|politico|analytics|the|hill|analytics|axios|analytics|vox|analytics|buzzfeed|analytics|huffpost|analytics|daily|beast|analytics|slate|analytics|salon|analytics|mother|jones|analytics|the|nation|analytics|new|republic|analytics|atlantic|analytics|new|yorker|analytics|harper|magazine|analytics|esquire|analytics|gq|analytics|vanity|fair|analytics|vogue|analytics|elle|analytics|marie|claire|analytics|cosmopolitan|analytics|glamour|analytics|allure|analytics|self|analytics|shape|analytics|fitness|analytics|men|health|analytics|women|health|analytics|prevention|analytics|good|housekeeping|analytics|better|homes|gardens|analytics|southern|living|analytics|country|living|analytics|real|simple|analytics|martha|stewart|analytics|food|network|analytics|cooking|channel|analytics|bon|appetit|analytics|food|wine|analytics|epicurious|analytics|allrecipes|analytics|taste|home|analytics|delish|analytics|eater|analytics|serious|eats|analytics|the|kitchn|analytics|simply|recipes|analytics|budget|bytes|analytics|minimalist|baker|analytics|cookie|kate|analytics|smitten|kitchen|analytics|pioneer|woman|analytics|half|baked|harvest|analytics|love|lemons|analytics|cafe|delites|analytics|damn|delicious|analytics|gimme|some|oven|analytics|sally|baking|addiction|analytics|king|arthur|baking|analytics|williams|sonoma|analytics|sur|la|table|analytics|crate|barrel|analytics|pottery|barn|analytics|west|elm|analytics|cb|analytics|restoration|hardware|analytics|anthropologie|analytics|urban|outfitters|analytics|free|people|analytics|madewell|analytics|everlane|analytics|reformation|analytics|ganni|analytics|staud|analytics|rouje|analytics|sezane|analytics|cos|analytics|arket|analytics|weekday|analytics|monki|analytics|other|stories|analytics|zara|analytics|mango|analytics|massimo|dutti|analytics|pull|bear|analytics|bershka|analytics|stradivarius|analytics|oysho|analytics|uterque|analytics|lefties|analytics|uniqlo|analytics|muji|analytics|cos|analytics|arket|analytics|weekday|analytics|monki|analytics|other|stories|analytics|zara|analytics|mango|analytics|massimo|dutti|analytics|pull|bear|analytics|bershka|analytics|stradivarius|analytics|oysho|analytics|uterque|analytics|lefties|analytics|uniqlo|analytics|muji|analytics)\b/gi
        ];
        const entities = new Set();
        for (const pattern of entityPatterns) {
            const matches = text.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    const entity = match.toLowerCase().trim();
                    if (entity.length > 2 && entity.length < 20) {
                        entities.add(entity.charAt(0).toUpperCase() + entity.slice(1));
                    }
                });
            }
        }
        // If no entities found, generate default ones based on common patterns
        if (entities.size === 0) {
            const defaultEntities = ['User', 'Product', 'Order', 'Category', 'Payment'];
            defaultEntities.forEach(entity => entities.add(entity));
        }
        return Array.from(entities).slice(0, 8); // Limit to 8 entities for manageability
    }
    extractAPIsFromText(text) {
        const apiPatterns = [
            /\/api\/\w+/gi,
            /\b(get|post|put|delete|patch)\s+\/\w+/gi,
            /\b(create|read|update|delete|list|search|filter)\s+\w+/gi
        ];
        const apis = new Set();
        for (const pattern of apiPatterns) {
            const matches = text.match(pattern);
            if (matches) {
                matches.forEach(match => apis.add(match.toLowerCase()));
            }
        }
        return Array.from(apis).slice(0, 15); // Limit to 15 APIs
    }
    generateDefaultFields(entityName) {
        const commonFields = [
            { name: 'id', type: 'string', required: true, description: `Unique identifier for ${entityName}` },
            { name: 'createdAt', type: 'Date', required: true, description: 'Creation timestamp' },
            { name: 'updatedAt', type: 'Date', required: true, description: 'Last update timestamp' }
        ];
        const entitySpecificFields = {
            user: [
                { name: 'email', type: 'string', required: true, description: 'User email address' },
                { name: 'name', type: 'string', required: true, description: 'User full name' },
                { name: 'role', type: 'string', required: false, description: 'User role' }
            ],
            product: [
                { name: 'name', type: 'string', required: true, description: 'Product name' },
                { name: 'price', type: 'number', required: true, description: 'Product price' },
                { name: 'description', type: 'string', required: false, description: 'Product description' }
            ],
            order: [
                { name: 'total', type: 'number', required: true, description: 'Order total amount' },
                { name: 'status', type: 'string', required: true, description: 'Order status' },
                { name: 'userId', type: 'string', required: true, description: 'User who placed the order' }
            ]
        };
        const specificFields = entitySpecificFields[entityName.toLowerCase()] || [
            { name: 'name', type: 'string', required: true, description: `${entityName} name` },
            { name: 'description', type: 'string', required: false, description: `${entityName} description` }
        ];
        return [...commonFields, ...specificFields];
    }
    generateWorkflowsFromFeatures(features) {
        return features.map(feature => ({
            name: `${feature} Workflow`,
            description: `Complete workflow for ${feature} functionality`,
            steps: [
                'User authentication',
                'Input validation',
                'Business logic execution',
                'Data persistence',
                'Response generation'
            ],
            entities: ['User', 'System']
        }));
    }
    generateUserRoles(features) {
        const roles = [
            {
                name: 'Admin',
                permissions: ['create', 'read', 'update', 'delete', 'manage_users', 'view_analytics'],
                description: 'Full system access and management capabilities'
            },
            {
                name: 'User',
                permissions: ['read', 'create_own', 'update_own'],
                description: 'Standard user with limited permissions'
            }
        ];
        if (features.some(f => f.toLowerCase().includes('manager') || f.toLowerCase().includes('admin'))) {
            roles.push({
                name: 'Manager',
                permissions: ['read', 'update', 'create', 'view_reports'],
                description: 'Management level access with reporting capabilities'
            });
        }
        return roles;
    }
    generateComprehensiveFields(entityName) {
        const baseFields = this.generateDefaultFields(entityName);
        // Add entity-specific comprehensive fields
        const comprehensiveFields = {
            user: [
                { name: 'firstName', type: 'string', required: true, description: 'User first name' },
                { name: 'lastName', type: 'string', required: true, description: 'User last name' },
                { name: 'phone', type: 'string', required: false, description: 'User phone number' },
                { name: 'avatar', type: 'string', required: false, description: 'User avatar URL' },
                { name: 'isActive', type: 'boolean', required: true, description: 'User active status' },
                { name: 'lastLoginAt', type: 'Date', required: false, description: 'Last login timestamp' }
            ],
            product: [
                { name: 'sku', type: 'string', required: true, description: 'Product SKU' },
                { name: 'category', type: 'string', required: true, description: 'Product category' },
                { name: 'stock', type: 'number', required: true, description: 'Available stock' },
                { name: 'images', type: 'string[]', required: false, description: 'Product images' },
                { name: 'isActive', type: 'boolean', required: true, description: 'Product active status' },
                { name: 'weight', type: 'number', required: false, description: 'Product weight' }
            ],
            order: [
                { name: 'orderNumber', type: 'string', required: true, description: 'Unique order number' },
                { name: 'items', type: 'OrderItem[]', required: true, description: 'Order items' },
                { name: 'shippingAddress', type: 'Address', required: true, description: 'Shipping address' },
                { name: 'billingAddress', type: 'Address', required: false, description: 'Billing address' },
                { name: 'paymentMethod', type: 'string', required: true, description: 'Payment method' },
                { name: 'shippingCost', type: 'number', required: true, description: 'Shipping cost' }
            ]
        };
        const specificFields = comprehensiveFields[entityName.toLowerCase()] || [
            { name: 'title', type: 'string', required: true, description: `${entityName} title` },
            { name: 'status', type: 'string', required: true, description: `${entityName} status` },
            { name: 'metadata', type: 'object', required: false, description: `${entityName} metadata` }
        ];
        return [...baseFields, ...specificFields];
    }
    generateEntityRelationships(entityName, allEntities) {
        const relationships = [];
        // Generate logical relationships based on entity names
        const relationshipRules = {
            user: [
                { type: 'oneToMany', target: 'Order', description: 'User can have multiple orders' },
                { type: 'oneToMany', target: 'Review', description: 'User can write multiple reviews' }
            ],
            order: [
                { type: 'manyToOne', target: 'User', description: 'Order belongs to a user' },
                { type: 'oneToMany', target: 'OrderItem', description: 'Order contains multiple items' }
            ],
            product: [
                { type: 'oneToMany', target: 'OrderItem', description: 'Product can be in multiple orders' },
                { type: 'manyToOne', target: 'Category', description: 'Product belongs to a category' }
            ]
        };
        const entityRules = relationshipRules[entityName.toLowerCase()] || [];
        entityRules.forEach(rule => {
            if (allEntities.includes(rule.target)) {
                relationships.push(rule);
            }
        });
        return relationships;
    }
    generateComprehensiveOperations(entityName) {
        const baseOperations = [
            { name: 'create', type: 'create', description: `Create new ${entityName}` },
            { name: 'findById', type: 'read', description: `Find ${entityName} by ID` },
            { name: 'findAll', type: 'read', description: `Find all ${entityName}s` },
            { name: 'update', type: 'update', description: `Update ${entityName}` },
            { name: 'delete', type: 'delete', description: `Delete ${entityName}` }
        ];
        const advancedOperations = [
            { name: 'search', type: 'custom', description: `Search ${entityName}s` },
            { name: 'filter', type: 'custom', description: `Filter ${entityName}s` },
            { name: 'paginate', type: 'custom', description: `Paginate ${entityName}s` },
            { name: 'count', type: 'custom', description: `Count ${entityName}s` },
            { name: 'exists', type: 'custom', description: `Check if ${entityName} exists` }
        ];
        return [...baseOperations, ...advancedOperations];
    }
    generateComprehensiveWorkflows(features, entities) {
        const workflows = [
            {
                name: 'User Registration',
                description: 'Complete user registration workflow',
                steps: ['Email validation', 'Password hashing', 'Account creation', 'Welcome email'],
                entities: ['User', 'Email']
            },
            {
                name: 'Authentication',
                description: 'User authentication and authorization workflow',
                steps: ['Credential validation', 'JWT generation', 'Session management', 'Permission check'],
                entities: ['User', 'Session', 'Token']
            }
        ];
        features.forEach(feature => {
            workflows.push({
                name: `${feature} Management`,
                description: `Complete ${feature} management workflow`,
                steps: [
                    'Input validation',
                    'Business logic execution',
                    'Data persistence',
                    'Event notification',
                    'Response generation'
                ],
                entities: entities.slice(0, 3)
            });
        });
        return workflows;
    }
    generateComprehensiveUserRoles(features) {
        const roles = [
            {
                name: 'SuperAdmin',
                permissions: ['*'],
                description: 'Full system access with all permissions'
            },
            {
                name: 'Admin',
                permissions: [
                    'users:create', 'users:read', 'users:update', 'users:delete',
                    'products:create', 'products:read', 'products:update', 'products:delete',
                    'orders:read', 'orders:update', 'analytics:read', 'settings:update'
                ],
                description: 'Administrative access with management capabilities'
            },
            {
                name: 'Manager',
                permissions: [
                    'products:create', 'products:read', 'products:update',
                    'orders:read', 'orders:update', 'analytics:read'
                ],
                description: 'Management level access with operational capabilities'
            },
            {
                name: 'User',
                permissions: [
                    'profile:read', 'profile:update', 'orders:create', 'orders:read'
                ],
                description: 'Standard user with basic permissions'
            },
            {
                name: 'Guest',
                permissions: ['products:read', 'categories:read'],
                description: 'Limited access for unauthenticated users'
            }
        ];
        return roles;
    }
    generateComprehensiveArchitecture(features, entities) {
        return {
            frontend: {
                framework: 'React with TypeScript',
                components: [
                    'App', 'Router', 'Layout', 'Header', 'Sidebar', 'Footer',
                    'Dashboard', 'AuthGuard', 'ErrorBoundary', 'LoadingSpinner',
                    ...entities.map(entity => `${entity}List`),
                    ...entities.map(entity => `${entity}Form`),
                    ...entities.map(entity => `${entity}Detail`)
                ],
                routing: true,
                stateManagement: 'Redux Toolkit with RTK Query',
                styling: 'Tailwind CSS with Headless UI',
                testing: 'Jest + React Testing Library',
                bundler: 'Vite',
                linting: 'ESLint + Prettier'
            },
            backend: {
                framework: 'Express.js with TypeScript',
                database: 'PostgreSQL with Prisma ORM',
                authentication: 'JWT with refresh tokens',
                authorization: 'Role-based access control (RBAC)',
                apis: [
                    '/api/auth/login', '/api/auth/register', '/api/auth/refresh',
                    '/api/auth/logout', '/api/auth/forgot-password', '/api/auth/reset-password',
                    ...entities.flatMap(entity => [
                        `/api/${entity.toLowerCase()}`,
                        `/api/${entity.toLowerCase()}/:id`,
                        `/api/${entity.toLowerCase()}/search`,
                        `/api/${entity.toLowerCase()}/filter`
                    ])
                ],
                middleware: ['cors', 'helmet', 'compression', 'rate-limiting', 'logging'],
                validation: 'Joi schema validation',
                documentation: 'OpenAPI/Swagger',
                testing: 'Jest + Supertest'
            },
            database: {
                type: 'PostgreSQL',
                orm: 'Prisma',
                migrations: 'Prisma Migrate',
                seeding: 'Prisma Seed',
                backup: 'pg_dump automated backups',
                monitoring: 'PostgreSQL built-in stats',
                indexing: 'Optimized indexes for queries',
                constraints: 'Foreign keys and check constraints'
            },
            deployment: {
                containerization: 'Docker with multi-stage builds',
                orchestration: 'Docker Compose for development, Kubernetes for production',
                environment: 'production',
                ports: [3000, 5000, 5432, 6379],
                loadBalancer: 'Nginx',
                ssl: 'Let\'s Encrypt certificates',
                monitoring: 'Prometheus + Grafana',
                logging: 'Winston + ELK Stack',
                cicd: 'GitHub Actions'
            },
            security: {
                authentication: 'JWT with RS256 signing',
                authorization: 'Role-based permissions',
                encryption: 'bcrypt for passwords, AES-256 for sensitive data',
                headers: 'Security headers via Helmet.js',
                cors: 'Configured CORS policies',
                rateLimiting: 'Express rate limit',
                inputValidation: 'Joi schema validation',
                sqlInjection: 'Parameterized queries via Prisma',
                xss: 'Content Security Policy',
                csrf: 'CSRF tokens for state-changing operations'
            }
        };
    }
    generateProductionArchitecture(features, entities) {
        const baseArch = this.generateComprehensiveArchitecture(features, entities);
        // Enhance for production readiness
        baseArch.performance = {
            caching: 'Redis for session and query caching',
            cdn: 'CloudFront for static assets',
            compression: 'Gzip compression for responses',
            optimization: 'Code splitting and lazy loading',
            monitoring: 'Application Performance Monitoring (APM)',
            scaling: 'Horizontal pod autoscaling'
        };
        baseArch.reliability = {
            healthChecks: 'Kubernetes liveness and readiness probes',
            gracefulShutdown: 'SIGTERM handling',
            circuitBreaker: 'Circuit breaker pattern for external services',
            retries: 'Exponential backoff retry logic',
            fallbacks: 'Graceful degradation strategies'
        };
        return baseArch;
    }
    generateComprehensiveDependencies(features) {
        return {
            frontend: [
                // Core React
                'react', 'react-dom', '@types/react', '@types/react-dom',
                // Routing
                'react-router-dom', '@types/react-router-dom',
                // State Management
                '@reduxjs/toolkit', 'react-redux', '@types/react-redux',
                // API Client
                'axios', '@types/axios',
                // UI Components
                '@headlessui/react', '@heroicons/react',
                // Styling
                'tailwindcss', '@tailwindcss/forms', '@tailwindcss/typography',
                // Forms
                'react-hook-form', '@hookform/resolvers', 'yup',
                // Utilities
                'date-fns', 'lodash', '@types/lodash', 'uuid', '@types/uuid',
                // Development
                'vite', '@vitejs/plugin-react', 'typescript'
            ],
            backend: [
                // Core Express
                'express', '@types/express',
                // Database
                'prisma', '@prisma/client', 'pg', '@types/pg',
                // Authentication
                'jsonwebtoken', '@types/jsonwebtoken', 'bcryptjs', '@types/bcryptjs',
                // Validation
                'joi', '@types/joi',
                // Security
                'cors', '@types/cors', 'helmet', '@types/helmet',
                // Utilities
                'dotenv', 'compression', 'express-rate-limit',
                // Logging
                'winston', 'morgan', '@types/morgan',
                // Documentation
                'swagger-jsdoc', 'swagger-ui-express',
                // Development
                'typescript', 'ts-node', 'nodemon', '@types/node'
            ],
            dev: [
                // Testing
                'jest', '@types/jest', 'supertest', '@types/supertest',
                'jest-environment-jsdom', '@testing-library/react',
                '@testing-library/jest-dom', '@testing-library/user-event',
                // Linting
                'eslint', '@typescript-eslint/parser', '@typescript-eslint/eslint-plugin',
                'eslint-plugin-react', 'eslint-plugin-react-hooks',
                // Formatting
                'prettier', 'eslint-config-prettier', 'eslint-plugin-prettier',
                // Build Tools
                'concurrently', 'cross-env', 'rimraf'
            ]
        };
    }
    generateProductionFileStructure(entities, features) {
        return {
            root: [
                'docker-compose.yml', 'docker-compose.prod.yml', 'Dockerfile',
                'README.md', '.env.example', '.gitignore', '.dockerignore',
                'package.json', 'tsconfig.json', 'jest.config.js',
                'nginx.conf', 'k8s/', 'scripts/', 'docs/'
            ],
            frontend: [
                'src/components/common/', 'src/components/layout/',
                ...entities.map(entity => `src/components/${entity.toLowerCase()}/`),
                'src/pages/', 'src/hooks/', 'src/store/', 'src/services/',
                'src/utils/', 'src/types/', 'src/constants/', 'src/styles/',
                'src/assets/', 'src/tests/', 'public/', 'dist/'
            ],
            backend: [
                'src/controllers/', 'src/services/', 'src/models/',
                'src/middleware/', 'src/routes/', 'src/utils/',
                'src/types/', 'src/config/', 'src/validators/',
                'src/tests/', 'prisma/', 'uploads/', 'logs/', 'dist/'
            ],
            database: [
                'migrations/', 'seeds/', 'backups/', 'scripts/'
            ],
            deployment: [
                'k8s/namespace.yaml', 'k8s/configmap.yaml', 'k8s/secret.yaml',
                'k8s/deployment.yaml', 'k8s/service.yaml', 'k8s/ingress.yaml',
                'k8s/hpa.yaml', 'k8s/pdb.yaml', 'scripts/deploy.sh',
                'scripts/backup.sh', 'scripts/restore.sh'
            ],
            monitoring: [
                'prometheus/', 'grafana/', 'alertmanager/', 'logs/'
            ]
        };
    }
    generateDefaultFeatures(entities) {
        const baseFeatures = [
            'User Authentication', 'User Authorization', 'User Management',
            'Dashboard', 'Analytics', 'Search', 'Filtering', 'Pagination',
            'File Upload', 'Email Notifications', 'Audit Logging',
            'API Documentation', 'Error Handling', 'Data Validation'
        ];
        const entityFeatures = entities.flatMap(entity => [
            `${entity} Management`,
            `${entity} CRUD Operations`,
            `${entity} Search and Filter`
        ]);
        return [...baseFeatures, ...entityFeatures];
    }
    generateComprehensiveTestingStrategy(features, entities) {
        return [
            // Unit Testing
            'Unit tests for all service functions',
            'Unit tests for all utility functions',
            'Unit tests for all validation schemas',
            'Unit tests for all middleware',
            // Integration Testing
            'API endpoint integration tests',
            'Database integration tests',
            'Authentication flow tests',
            'Authorization tests',
            // Frontend Testing
            'Component unit tests',
            'Hook tests',
            'Store/reducer tests',
            'User interaction tests',
            // End-to-End Testing
            'User registration and login flows',
            'Complete user journeys',
            'Cross-browser compatibility tests',
            'Mobile responsiveness tests',
            // Performance Testing
            'Load testing for API endpoints',
            'Database query performance tests',
            'Frontend bundle size optimization',
            'Lighthouse performance audits',
            // Security Testing
            'Authentication bypass tests',
            'Authorization tests',
            'Input validation tests',
            'SQL injection tests',
            'XSS vulnerability tests',
            // Accessibility Testing
            'WCAG compliance tests',
            'Screen reader compatibility',
            'Keyboard navigation tests',
            // Test Coverage
            'Minimum 80% code coverage',
            'Critical path 100% coverage',
            'Automated coverage reporting'
        ];
    }
}
exports.PlanningAgent = PlanningAgent;
//# sourceMappingURL=PlanningAgent.js.map
import { BaseAgent } from '../core/BaseAgent';
import { AgentTask, AgentResult } from '../core/BaseAgent';
import { Logger } from '../core/Logger';
export declare class SecurityAgent extends BaseAgent {
    constructor(logger?: Logger);
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    private implementSecurityHardening;
    private generateSecureEnvironmentConfig;
    private generateSecureSecret;
    private generateSecurePassword;
    private implementPasswordSecurity;
    private addRateLimitingMiddleware;
    private updateServerWithRateLimiting;
    private createInputValidationSchemas;
    private addSecurityHeaders;
    private updateAuthenticationMiddleware;
    private createComprehensiveInputValidation;
    private implementSQLInjectionPrevention;
    private calculateSecurityScore;
    private implementXSSProtection;
    private addAdvancedSecurityHeaders;
    private implementCSRFProtection;
    private enhanceAuthenticationSecurity;
    private implementJWTSecurityBestPractices;
    private implementSecurityLogging;
    private addSecurityMiddleware;
    private performSecurityAudit;
    private auditSecurityVulnerabilities;
    private checkForHardcodedSecrets;
    private checkInsecureDependencies;
    private checkSecurityHeaders;
    private autoFixVulnerability;
    private fixHardcodedSecret;
    private addSecurityHeadersToServer;
    private findFiles;
    private calculateAdvancedSecurityScore;
    private implementEnhancedSecurity;
    private implementSSLTLSEnforcement;
    private implementSecretsManagement;
}
//# sourceMappingURL=SecurityAgent.d.ts.map
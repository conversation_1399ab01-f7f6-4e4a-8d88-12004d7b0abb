"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityAgent = void 0;
const BaseAgent_1 = require("../core/BaseAgent");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
class SecurityAgent extends BaseAgent_1.BaseAgent {
    constructor(logger) {
        super('SecurityAgent', 'Hardens application security by implementing secure configurations, authentication, and validation', `# IDENTITY
You are DroidBotX SecurityAgent, a cybersecurity expert with 15+ years of experience implementing enterprise-grade security measures. You specialize in preventing security vulnerabilities and implementing production-ready security configurations.

# CRITICAL SECURITY CONSTRAINTS (NEVER VIOLATE)
🚨 SECRETS MANAGEMENT:
- NEVER allow hardcoded secrets in any configuration
- NEVER use default or placeholder values for JWT secrets
- ALWAYS require environment variables for sensitive data
- FORBIDDEN: Any hardcoded passwords, API keys, or tokens

🚨 AUTHENTICATION SECURITY:
- ALWAYS implement proper JWT secret validation
- ALWAYS use bcrypt for password hashing with salt rounds >= 12
- ALWAYS implement rate limiting on authentication endpoints
- FORBIDDEN: Plain text passwords or weak hashing algorithms

🚨 INPUT VALIDATION:
- ALWAYS validate and sanitize all user inputs
- ALWAYS implement proper schema validation
- ALWAYS prevent SQL injection with parameterized queries
- FORBIDDEN: Direct string concatenation in database queries

🚨 SECURITY HEADERS:
- ALWAYS implement comprehensive security headers
- ALWAYS configure CORS properly for production
- ALWAYS implement CSRF protection
- FORBIDDEN: Permissive CORS or missing security headers

# MANDATORY SECURITY IMPLEMENTATIONS:
1. Replace all hardcoded secrets with environment variable requirements
2. Implement comprehensive input validation schemas
3. Add rate limiting to all authentication endpoints
4. Configure security headers (helmet.js)
5. Implement proper error handling without information leakage
6. Add request logging for security monitoring

# SECURITY VALIDATION CHECKLIST:
□ No hardcoded secrets or default values
□ JWT secret properly validated from environment
□ All inputs validated with schemas
□ Rate limiting implemented on auth endpoints
□ Security headers configured
□ Error messages don't leak sensitive information
□ Database queries use parameterized statements
□ Password hashing uses bcrypt with proper salt rounds`);
    }
    canHandle(task) {
        return task.type === 'security' || task.type === 'SECURITY';
    }
    async execute(task) {
        this.logger.info('Starting security hardening process', {
            taskId: task.id,
            description: task.description
        });
        try {
            const generatedCode = task.parameters.previousResult;
            if (!generatedCode) {
                throw new Error('No generated code provided from previous agent');
            }
            const securityResult = await this.implementSecurityHardening(generatedCode);
            return {
                success: securityResult.success,
                data: {
                    ...generatedCode,
                    securityResult,
                    isSecurityHardened: securityResult.success
                },
                metadata: {
                    agent: 'SecurityAgent',
                    timestamp: new Date().toISOString(),
                    securityFixes: securityResult.fixesApplied,
                    vulnerabilitiesFixed: securityResult.vulnerabilitiesFixed
                }
            };
        }
        catch (error) {
            this.logger.error('Security hardening failed', {
                error: error instanceof Error ? error.message : String(error),
                taskId: task.id
            });
            return {
                success: false,
                data: task.parameters.previousResult,
                metadata: {
                    agent: 'SecurityAgent',
                    timestamp: new Date().toISOString(),
                    error: error instanceof Error ? error.message : 'Unknown security error'
                }
            };
        }
    }
    async implementSecurityHardening(generatedCode) {
        const result = {
            success: false,
            fixesApplied: 0,
            vulnerabilitiesFixed: [],
            securityScore: 0
        };
        const projectPath = generatedCode.projectPath;
        if (!projectPath || !fs.existsSync(projectPath)) {
            throw new Error(`Project path not found: ${projectPath}`);
        }
        this.logger.info('Starting comprehensive security hardening', { projectPath });
        try {
            // Phase 1: Core Security Infrastructure
            await this.generateSecureEnvironmentConfig(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('Secure environment configuration generated');
            await this.implementPasswordSecurity(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('Password security implemented');
            await this.addRateLimitingMiddleware(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('Rate limiting middleware added');
            // Phase 2: Input Validation & Sanitization
            await this.createComprehensiveInputValidation(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('Comprehensive input validation implemented');
            await this.implementSQLInjectionPrevention(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('SQL injection prevention implemented');
            await this.implementXSSProtection(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('XSS protection implemented');
            // Phase 3: Security Headers & CORS
            await this.addAdvancedSecurityHeaders(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('Advanced security headers configured');
            await this.implementCSRFProtection(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('CSRF protection implemented');
            // Phase 4: Authentication & Authorization
            await this.enhanceAuthenticationSecurity(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('Authentication security enhanced');
            await this.implementJWTSecurityBestPractices(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('JWT security best practices implemented');
            // Phase 5: Security Monitoring & Logging
            await this.implementSecurityLogging(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('Security logging implemented');
            await this.addSecurityMiddleware(projectPath);
            result.fixesApplied++;
            result.vulnerabilitiesFixed.push('Security middleware stack added');
            // Phase 6: Vulnerability Scanning & Auto-fixing
            await this.performSecurityAudit(projectPath, result);
            // Calculate final security score
            result.securityScore = this.calculateAdvancedSecurityScore(result.vulnerabilitiesFixed.length);
            result.success = result.securityScore >= 90; // Higher threshold for production readiness
            this.logger.info('Comprehensive security hardening completed', {
                fixesApplied: result.fixesApplied,
                vulnerabilitiesFixed: result.vulnerabilitiesFixed.length,
                securityScore: result.securityScore
            });
        }
        catch (error) {
            this.logger.error('Security hardening failed', { error: error instanceof Error ? error.message : String(error) });
            result.success = false;
        }
        return result;
    }
    async generateSecureEnvironmentConfig(projectPath) {
        const backendPath = path.join(projectPath, 'backend');
        const envPath = path.join(backendPath, '.env');
        const envExamplePath = path.join(backendPath, '.env.example');
        // Generate secure secrets
        const jwtSecret = this.generateSecureSecret(64);
        const jwtRefreshSecret = this.generateSecureSecret(64);
        const encryptionKey = this.generateSecureSecret(32);
        const sessionSecret = this.generateSecureSecret(32);
        const secureEnvConfig = `# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tech_store_ecommerce_db
DB_USER=postgres
DB_PASSWORD=${this.generateSecurePassword(16)}

# JWT Configuration
JWT_SECRET=${jwtSecret}
JWT_REFRESH_SECRET=${jwtRefreshSecret}
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Encryption
ENCRYPTION_KEY=${encryptionKey}

# Session Configuration
SESSION_SECRET=${sessionSecret}

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Environment
NODE_ENV=development
PORT=5000

# Logging
LOG_LEVEL=info
`;
        const envExampleConfig = secureEnvConfig
            .replace(/=.+$/gm, '=')
            .replace(/# Example values for development/g, '# Copy to .env and fill in actual values');
        // Write secure .env file
        fs.writeFileSync(envPath, secureEnvConfig);
        fs.writeFileSync(envExamplePath, envExampleConfig);
        this.logger.info('Generated secure environment configuration', {
            envPath,
            envExamplePath
        });
    }
    generateSecureSecret(length) {
        return crypto.randomBytes(length).toString('hex');
    }
    generateSecurePassword(length) {
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return password;
    }
    async implementPasswordSecurity(projectPath) {
        const authRoutePath = path.join(projectPath, 'backend', 'src', 'routes', 'auth.ts');
        if (!fs.existsSync(authRoutePath)) {
            this.logger.warn('Auth route file not found, skipping password security implementation');
            return;
        }
        let authContent = fs.readFileSync(authRoutePath, 'utf8');
        // Add bcrypt import if not present
        if (!authContent.includes('import bcrypt from')) {
            authContent = `import bcrypt from 'bcrypt';\n${authContent}`;
        }
        // Replace weak password hashing with secure bcrypt implementation
        const securePasswordHashing = `
// Secure password hashing with bcrypt
const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
  return await bcrypt.hash(password, saltRounds);
};

const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return await bcrypt.compare(password, hashedPassword);
};

// Password policy validation
const validatePasswordPolicy = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 12) {
    errors.push('Password must be at least 12 characters long');
  }
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  if (!/\\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  if (!/[!@#$%^&*()_+\\-=\\[\\]{};':"\\\\|,.<>\\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return { valid: errors.length === 0, errors };
};
`;
        // Check if secure password functions already exist to prevent duplicates
        if (!authContent.includes('const hashPassword = async')) {
            // Insert secure password functions after the last import statement
            const lastImportMatch = authContent.match(/import.*from.*['"].*['"];?\s*$/gm);
            if (lastImportMatch) {
                const lastImport = lastImportMatch[lastImportMatch.length - 1];
                const lastImportIndex = authContent.lastIndexOf(lastImport);
                const insertIndex = lastImportIndex + lastImport.length;
                authContent = authContent.slice(0, insertIndex) +
                    securePasswordHashing +
                    authContent.slice(insertIndex);
            }
            else {
                // If no imports found, add at the beginning
                authContent = securePasswordHashing + authContent;
            }
        }
        fs.writeFileSync(authRoutePath, authContent);
        this.logger.info('Implemented secure password policies', { authRoutePath });
    }
    async addRateLimitingMiddleware(projectPath) {
        const middlewarePath = path.join(projectPath, 'backend', 'src', 'middleware');
        const rateLimitPath = path.join(middlewarePath, 'rateLimiter.ts');
        // Ensure middleware directory exists
        if (!fs.existsSync(middlewarePath)) {
            fs.mkdirSync(middlewarePath, { recursive: true });
        }
        const rateLimiterContent = `import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

// General API rate limiter
export const apiLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Strict rate limiter for authentication endpoints
export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
});

// Password reset rate limiter
export const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 password reset requests per hour
  message: {
    error: 'Too many password reset attempts, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
`;
        fs.writeFileSync(rateLimitPath, rateLimiterContent);
        this.logger.info('Created rate limiting middleware', { rateLimitPath });
        // Update server.ts to use rate limiting
        await this.updateServerWithRateLimiting(projectPath);
    }
    async updateServerWithRateLimiting(projectPath) {
        const serverPath = path.join(projectPath, 'backend', 'src', 'server.ts');
        if (!fs.existsSync(serverPath)) {
            this.logger.warn('Server file not found, skipping rate limiting integration');
            return;
        }
        let serverContent = fs.readFileSync(serverPath, 'utf8');
        // Add rate limiting imports
        if (!serverContent.includes('rateLimiter')) {
            const rateLimitImport = `import { apiLimiter, authLimiter } from './middleware/rateLimiter';\n`;
            serverContent = serverContent.replace(/import.*from.*['"].*middleware.*['"];?\s*/, (match) => match + rateLimitImport);
        }
        // Add rate limiting middleware to app
        if (!serverContent.includes('app.use(apiLimiter)')) {
            const rateLimitMiddleware = `
// Apply rate limiting
app.use(apiLimiter);
app.use('/api/auth', authLimiter);
`;
            serverContent = serverContent.replace(/app\.use\(cors\(\)\);/, (match) => match + rateLimitMiddleware);
        }
        fs.writeFileSync(serverPath, serverContent);
        this.logger.info('Updated server with rate limiting', { serverPath });
    }
    async createInputValidationSchemas(projectPath) {
        const validationPath = path.join(projectPath, 'backend', 'src', 'validation');
        // Ensure validation directory exists
        if (!fs.existsSync(validationPath)) {
            fs.mkdirSync(validationPath, { recursive: true });
        }
        // Create comprehensive validation schemas
        const authValidationPath = path.join(validationPath, 'authValidation.ts');
        const authValidationContent = `import Joi from 'joi';

// Strong password validation schema
const passwordSchema = Joi.string()
  .min(12)
  .max(128)
  .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':"\\\\|,.<>\\/?])/)
  .required()
  .messages({
    'string.min': 'Password must be at least 12 characters long',
    'string.max': 'Password must not exceed 128 characters',
    'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'
  });

// Email validation schema
const emailSchema = Joi.string()
  .email({ tlds: { allow: false } })
  .max(254)
  .required()
  .messages({
    'string.email': 'Please provide a valid email address',
    'string.max': 'Email must not exceed 254 characters'
  });

// Registration validation schema
export const registerSchema = Joi.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': 'Passwords do not match'
    }),
  firstName: Joi.string()
    .trim()
    .min(1)
    .max(50)
    .pattern(/^[a-zA-Z\\s]+$/)
    .required()
    .messages({
      'string.pattern.base': 'First name can only contain letters and spaces'
    }),
  lastName: Joi.string()
    .trim()
    .min(1)
    .max(50)
    .pattern(/^[a-zA-Z\\s]+$/)
    .required()
    .messages({
      'string.pattern.base': 'Last name can only contain letters and spaces'
    })
});

// Login validation schema
export const loginSchema = Joi.object({
  email: emailSchema,
  password: Joi.string().required()
});

// Password reset validation schema
export const passwordResetSchema = Joi.object({
  email: emailSchema
});

// Change password validation schema
export const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: passwordSchema,
  confirmPassword: Joi.string()
    .valid(Joi.ref('newPassword'))
    .required()
    .messages({
      'any.only': 'Passwords do not match'
    })
});
`;
        fs.writeFileSync(authValidationPath, authValidationContent);
        this.logger.info('Created authentication validation schemas', { authValidationPath });
    }
    async addSecurityHeaders(projectPath) {
        const serverPath = path.join(projectPath, 'backend', 'src', 'server.ts');
        if (!fs.existsSync(serverPath)) {
            this.logger.warn('Server file not found, skipping security headers');
            return;
        }
        let serverContent = fs.readFileSync(serverPath, 'utf8');
        // Add helmet import if not present
        if (!serverContent.includes('import helmet from')) {
            serverContent = `import helmet from 'helmet';\n${serverContent}`;
        }
        // Configure secure CORS
        const secureCorsConfig = `
// Secure CORS configuration
const corsOptions = {
  origin: function (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// Security headers configuration
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

app.use(cors(corsOptions));
`;
        // Replace basic CORS with secure configuration
        serverContent = serverContent.replace(/app\.use\(cors\(\)\);/, secureCorsConfig);
        fs.writeFileSync(serverPath, serverContent);
        this.logger.info('Added security headers and secure CORS', { serverPath });
    }
    async updateAuthenticationMiddleware(projectPath) {
        const authMiddlewarePath = path.join(projectPath, 'backend', 'src', 'middleware', 'auth.ts');
        // Create secure authentication middleware
        const secureAuthMiddleware = `import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
  };
}

export const authenticateToken = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      error: 'Access token required',
      code: 'TOKEN_MISSING'
    });
  }

  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    return res.status(500).json({
      error: 'Server configuration error',
      code: 'JWT_SECRET_MISSING'
    });
  }

  jwt.verify(token, jwtSecret, (err: any, decoded: any) => {
    if (err) {
      if (err.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: 'Token expired',
          code: 'TOKEN_EXPIRED'
        });
      }
      if (err.name === 'JsonWebTokenError') {
        return res.status(401).json({
          error: 'Invalid token',
          code: 'TOKEN_INVALID'
        });
      }
      return res.status(403).json({
        error: 'Token verification failed',
        code: 'TOKEN_VERIFICATION_FAILED'
      });
    }

    req.user = decoded;
    next();
  });
};

export const requireRole = (roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    next();
  };
};
`;
        // Ensure middleware directory exists
        const middlewareDir = path.dirname(authMiddlewarePath);
        if (!fs.existsSync(middlewareDir)) {
            fs.mkdirSync(middlewareDir, { recursive: true });
        }
        fs.writeFileSync(authMiddlewarePath, secureAuthMiddleware);
        this.logger.info('Created secure authentication middleware', { authMiddlewarePath });
    }
    async createComprehensiveInputValidation(projectPath) {
        const validationPath = path.join(projectPath, 'backend', 'src', 'validation');
        if (!fs.existsSync(validationPath)) {
            fs.mkdirSync(validationPath, { recursive: true });
        }
        // Create comprehensive validation schemas
        const validationSchemas = `import Joi from 'joi';
import DOMPurify from 'isomorphic-dompurify';

// Base validation patterns
const patterns = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,
  phone: /^\\+?[1-9]\\d{1,14}$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  noScript: /^(?!.*<script).*$/i,
  sqlInjection: /^(?!.*(union|select|insert|update|delete|drop|create|alter|exec|execute)).*$/i
};

// Sanitization functions
export const sanitize = {
  html: (input: string): string => DOMPurify.sanitize(input),
  sql: (input: string): string => input.replace(/['";\\\\]/g, ''),
  xss: (input: string): string => input.replace(/<script[^>]*>.*?<\\/script>/gi, ''),
  trim: (input: string): string => input.trim().replace(/\\s+/g, ' ')
};

// Enhanced validation schemas
export const validationSchemas = {
  user: {
    register: Joi.object({
      email: Joi.string().email().pattern(patterns.email).required()
        .messages({ 'string.pattern.base': 'Invalid email format' }),
      password: Joi.string().min(8).max(128)
        .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)
        .required()
        .messages({ 'string.pattern.base': 'Password must contain uppercase, lowercase, number and special character' }),
      name: Joi.string().min(2).max(50).pattern(/^[a-zA-Z\\s]+$/).required()
        .custom((value, helpers) => sanitize.html(value))
    }),
    login: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required()
    })
  },
  product: {
    create: Joi.object({
      name: Joi.string().min(1).max(200).required()
        .custom((value, helpers) => sanitize.html(value)),
      description: Joi.string().max(2000).optional()
        .custom((value, helpers) => sanitize.html(value)),
      price: Joi.number().positive().precision(2).required(),
      category: Joi.string().min(1).max(100).required()
        .custom((value, helpers) => sanitize.html(value)),
      barcode: Joi.string().pattern(patterns.alphanumeric).max(50).optional()
    })
  }
};

// Validation middleware
export const validateRequest = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    req.body = value;
    next();
  };
};
`;
        fs.writeFileSync(path.join(validationPath, 'schemas.ts'), validationSchemas);
        this.logger.info('Created comprehensive input validation schemas');
    }
    async implementSQLInjectionPrevention(projectPath) {
        const middlewarePath = path.join(projectPath, 'backend', 'src', 'middleware');
        if (!fs.existsSync(middlewarePath)) {
            fs.mkdirSync(middlewarePath, { recursive: true });
        }
        const sqlInjectionMiddleware = `import { Request, Response, NextFunction } from 'express';

// SQL Injection prevention middleware
export const preventSQLInjection = (req: Request, res: Response, next: NextFunction) => {
  const sqlInjectionPatterns = [
    /('|(\\-\\-)|;|\\||\\*|(%27)|(%2D%2D)|(%7C)|(%2A))/i,
    /(union|select|insert|update|delete|drop|create|alter|exec|execute)/i,
    /(script|javascript|vbscript|onload|onerror|onclick)/i
  ];

  const checkForSQLInjection = (obj: any): boolean => {
    if (typeof obj === 'string') {
      return sqlInjectionPatterns.some(pattern => pattern.test(obj));
    }

    if (typeof obj === 'object' && obj !== null) {
      return Object.values(obj).some(value => checkForSQLInjection(value));
    }

    return false;
  };

  // Check request body, query parameters, and URL parameters
  const requestData = { ...req.body, ...req.query, ...req.params };

  if (checkForSQLInjection(requestData)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid request detected',
      code: 'SECURITY_VIOLATION'
    });
  }

  next();
};
`;
        fs.writeFileSync(path.join(middlewarePath, 'sqlInjectionPrevention.ts'), sqlInjectionMiddleware);
        this.logger.info('Implemented SQL injection prevention middleware');
    }
    calculateSecurityScore(vulnerabilitiesFixed) {
        // Base score calculation: each vulnerability fixed adds points
        const baseScore = Math.min(vulnerabilitiesFixed * 15, 90);
        return baseScore;
    }
    async implementXSSProtection(projectPath) {
        const middlewarePath = path.join(projectPath, 'backend', 'src', 'middleware');
        const xssProtectionMiddleware = `import { Request, Response, NextFunction } from 'express';
import DOMPurify from 'isomorphic-dompurify';

// XSS Protection middleware
export const xssProtection = (req: Request, res: Response, next: NextFunction) => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      return DOMPurify.sanitize(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => sanitizeObject(item));
    }

    if (typeof obj === 'object' && obj !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }

    return obj;
  };

  // Sanitize request body
  if (req.body) {
    req.body = sanitizeObject(req.body);
  }

  // Sanitize query parameters
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  next();
};
`;
        fs.writeFileSync(path.join(middlewarePath, 'xssProtection.ts'), xssProtectionMiddleware);
        this.logger.info('Implemented XSS protection middleware');
    }
    async addAdvancedSecurityHeaders(projectPath) {
        const middlewarePath = path.join(projectPath, 'backend', 'src', 'middleware');
        const securityHeadersMiddleware = `import helmet from 'helmet';
import { Request, Response, NextFunction } from 'express';

// Advanced security headers configuration
export const advancedSecurityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: "strict-origin-when-cross-origin" },
  permittedCrossDomainPolicies: false,
  crossOriginEmbedderPolicy: true,
  crossOriginOpenerPolicy: true,
  crossOriginResourcePolicy: { policy: "cross-origin" }
});

// Custom security headers
export const customSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Remove server information
  res.removeHeader('X-Powered-By');

  // Add custom security headers
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

  next();
};
`;
        fs.writeFileSync(path.join(middlewarePath, 'securityHeaders.ts'), securityHeadersMiddleware);
        this.logger.info('Implemented advanced security headers');
    }
    async implementCSRFProtection(projectPath) {
        const middlewarePath = path.join(projectPath, 'backend', 'src', 'middleware');
        const csrfMiddleware = `import csrf from 'csurf';
import { Request, Response, NextFunction } from 'express';

// CSRF protection configuration
export const csrfProtection = csrf({
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  }
});

// CSRF token endpoint
export const csrfToken = (req: Request, res: Response) => {
  res.json({ csrfToken: req.csrfToken() });
};

// CSRF error handler
export const csrfErrorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  if (err.code === 'EBADCSRFTOKEN') {
    return res.status(403).json({
      success: false,
      message: 'Invalid CSRF token',
      code: 'CSRF_INVALID'
    });
  }
  next(err);
};
`;
        fs.writeFileSync(path.join(middlewarePath, 'csrfProtection.ts'), csrfMiddleware);
        this.logger.info('Implemented CSRF protection');
    }
    async enhanceAuthenticationSecurity(projectPath) {
        const middlewarePath = path.join(projectPath, 'backend', 'src', 'middleware');
        const enhancedAuthMiddleware = `import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';

// Enhanced JWT verification with blacklist support
const tokenBlacklist = new Set<string>();

export const enhancedJWTAuth = (req: Request, res: Response, next: NextFunction) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access denied. No token provided.',
      code: 'NO_TOKEN'
    });
  }

  // Check if token is blacklisted
  if (tokenBlacklist.has(token)) {
    return res.status(401).json({
      success: false,
      message: 'Token has been revoked',
      code: 'TOKEN_REVOKED'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      code: 'INVALID_TOKEN'
    });
  }
};

// Token blacklist management
export const blacklistToken = (token: string) => {
  tokenBlacklist.add(token);
};

// Enhanced password validation
export const validatePassword = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (password.length < 8) errors.push('Password must be at least 8 characters long');
  if (!/[a-z]/.test(password)) errors.push('Password must contain lowercase letters');
  if (!/[A-Z]/.test(password)) errors.push('Password must contain uppercase letters');
  if (!/\\d/.test(password)) errors.push('Password must contain numbers');
  if (!/[@$!%*?&]/.test(password)) errors.push('Password must contain special characters');

  return { valid: errors.length === 0, errors };
};

// Account lockout protection
const loginAttempts = new Map<string, { count: number; lastAttempt: Date }>();

export const accountLockoutProtection = (req: Request, res: Response, next: NextFunction) => {
  const { email } = req.body;
  const attempts = loginAttempts.get(email);

  if (attempts && attempts.count >= 5) {
    const timeDiff = Date.now() - attempts.lastAttempt.getTime();
    if (timeDiff < 15 * 60 * 1000) { // 15 minutes lockout
      return res.status(429).json({
        success: false,
        message: 'Account temporarily locked due to too many failed attempts',
        code: 'ACCOUNT_LOCKED'
      });
    } else {
      loginAttempts.delete(email);
    }
  }

  next();
};

export const recordFailedLogin = (email: string) => {
  const attempts = loginAttempts.get(email) || { count: 0, lastAttempt: new Date() };
  attempts.count++;
  attempts.lastAttempt = new Date();
  loginAttempts.set(email, attempts);
};

export const clearFailedLogins = (email: string) => {
  loginAttempts.delete(email);
};
`;
        fs.writeFileSync(path.join(middlewarePath, 'enhancedAuth.ts'), enhancedAuthMiddleware);
        this.logger.info('Enhanced authentication security');
    }
    async implementJWTSecurityBestPractices(projectPath) {
        const utilsPath = path.join(projectPath, 'backend', 'src', 'utils');
        if (!fs.existsSync(utilsPath)) {
            fs.mkdirSync(utilsPath, { recursive: true });
        }
        const jwtSecurityUtils = `import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// JWT Security best practices
export class JWTSecurity {
  private static readonly ALGORITHM = 'HS256';
  private static readonly ISSUER = 'droidbotx-app';
  private static readonly AUDIENCE = 'droidbotx-users';

  // Generate secure JWT with proper claims
  static generateToken(payload: any, expiresIn: string = '15m'): string {
    const jwtPayload = {
      ...payload,
      iss: this.ISSUER,
      aud: this.AUDIENCE,
      iat: Math.floor(Date.now() / 1000),
      jti: crypto.randomUUID() // Unique token ID
    };

    return jwt.sign(jwtPayload, process.env.JWT_SECRET!, {
      algorithm: this.ALGORITHM,
      expiresIn,
      issuer: this.ISSUER,
      audience: this.AUDIENCE
    });
  }

  // Generate refresh token
  static generateRefreshToken(userId: string): string {
    return jwt.sign(
      { userId, type: 'refresh' },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: '7d', algorithm: this.ALGORITHM }
    );
  }

  // Verify token with comprehensive validation
  static verifyToken(token: string): any {
    try {
      return jwt.verify(token, process.env.JWT_SECRET!, {
        algorithms: [this.ALGORITHM],
        issuer: this.ISSUER,
        audience: this.AUDIENCE
      });
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  // Verify refresh token
  static verifyRefreshToken(token: string): any {
    try {
      return jwt.verify(token, process.env.JWT_REFRESH_SECRET!, {
        algorithms: [this.ALGORITHM]
      });
    } catch (error) {
      throw new Error('Invalid or expired refresh token');
    }
  }

  // Extract token from request headers
  static extractToken(authHeader: string | undefined): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }
}
`;
        fs.writeFileSync(path.join(utilsPath, 'jwtSecurity.ts'), jwtSecurityUtils);
        this.logger.info('Implemented JWT security best practices');
    }
    async implementSecurityLogging(projectPath) {
        const utilsPath = path.join(projectPath, 'backend', 'src', 'utils');
        const securityLogger = `import winston from 'winston';
import { Request, Response, NextFunction } from 'express';

// Security-focused logger configuration
const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'security' },
  transports: [
    new winston.transports.File({ filename: 'logs/security-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/security-audit.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Security event types
export enum SecurityEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  SQL_INJECTION_ATTEMPT = 'SQL_INJECTION_ATTEMPT',
  XSS_ATTEMPT = 'XSS_ATTEMPT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  TOKEN_VALIDATION_FAILED = 'TOKEN_VALIDATION_FAILED'
}

// Security logging middleware
export const securityAuditLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  // Log request details
  const requestLog = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer'),
    userId: req.user?.id || 'anonymous'
  };

  // Override res.json to capture response
  const originalJson = res.json;
  res.json = function(body: any) {
    const responseTime = Date.now() - startTime;

    // Log security-relevant responses
    if (res.statusCode >= 400) {
      securityLogger.warn('Security event detected', {
        ...requestLog,
        statusCode: res.statusCode,
        responseTime,
        responseBody: body
      });
    }

    return originalJson.call(this, body);
  };

  next();
};

// Log specific security events
export const logSecurityEvent = (
  eventType: SecurityEventType,
  details: any,
  req?: Request
) => {
  const logData = {
    eventType,
    timestamp: new Date().toISOString(),
    details,
    ...(req && {
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id || 'anonymous'
    })
  };

  if (eventType.includes('FAILURE') || eventType.includes('ATTEMPT')) {
    securityLogger.error('Security threat detected', logData);
  } else {
    securityLogger.info('Security event', logData);
  }
};

export { securityLogger };
`;
        fs.writeFileSync(path.join(utilsPath, 'securityLogger.ts'), securityLogger);
        this.logger.info('Implemented security logging system');
    }
    async addSecurityMiddleware(projectPath) {
        const middlewarePath = path.join(projectPath, 'backend', 'src', 'middleware');
        const securityMiddlewareStack = `import { Express } from 'express';
import { advancedSecurityHeaders, customSecurityHeaders } from './securityHeaders';
import { preventSQLInjection } from './sqlInjectionPrevention';
import { xssProtection } from './xssProtection';
import { csrfProtection, csrfErrorHandler } from './csrfProtection';
import { securityAuditLogger } from '../utils/securityLogger';
import { rateLimiter } from './rateLimiter';

// Comprehensive security middleware stack
export const applySecurityMiddleware = (app: Express) => {
  // 1. Security logging (first to capture all requests)
  app.use(securityAuditLogger);

  // 2. Rate limiting
  app.use('/api/', rateLimiter);

  // 3. Security headers
  app.use(advancedSecurityHeaders);
  app.use(customSecurityHeaders);

  // 4. Input sanitization and validation
  app.use(xssProtection);
  app.use(preventSQLInjection);

  // 5. CSRF protection (for non-API routes)
  app.use('/api/csrf-token', csrfProtection);

  // 6. CSRF error handling
  app.use(csrfErrorHandler);
};

// Security middleware for API routes
export const apiSecurityMiddleware = [
  securityAuditLogger,
  rateLimiter,
  xssProtection,
  preventSQLInjection
];

// Security middleware for authentication routes
export const authSecurityMiddleware = [
  securityAuditLogger,
  rateLimiter,
  xssProtection,
  preventSQLInjection
];
`;
        fs.writeFileSync(path.join(middlewarePath, 'securityStack.ts'), securityMiddlewareStack);
        this.logger.info('Created comprehensive security middleware stack');
    }
    async performSecurityAudit(projectPath, result) {
        this.logger.info('Performing comprehensive security audit');
        // Check for common security vulnerabilities
        const auditResults = await this.auditSecurityVulnerabilities(projectPath);
        // Auto-fix detected issues
        for (const vulnerability of auditResults.vulnerabilities) {
            try {
                await this.autoFixVulnerability(projectPath, vulnerability);
                result.fixesApplied++;
                result.vulnerabilitiesFixed.push(`Auto-fixed: ${vulnerability.type}`);
            }
            catch (error) {
                this.logger.warn(`Could not auto-fix vulnerability: ${vulnerability.type}`, { error: error instanceof Error ? error.message : String(error) });
            }
        }
        this.logger.info('Security audit completed', {
            vulnerabilitiesFound: auditResults.vulnerabilities.length,
            autoFixed: auditResults.vulnerabilities.length
        });
    }
    async auditSecurityVulnerabilities(projectPath) {
        const vulnerabilities = [];
        // Check for hardcoded secrets
        const secretsCheck = await this.checkForHardcodedSecrets(projectPath);
        vulnerabilities.push(...secretsCheck);
        // Check for insecure dependencies
        const dependencyCheck = await this.checkInsecureDependencies(projectPath);
        vulnerabilities.push(...dependencyCheck);
        // Check for missing security headers
        const headersCheck = await this.checkSecurityHeaders(projectPath);
        vulnerabilities.push(...headersCheck);
        return { vulnerabilities };
    }
    async checkForHardcodedSecrets(projectPath) {
        const vulnerabilities = [];
        const secretPatterns = [
            /password\s*=\s*["'][^"']+["']/i,
            /secret\s*=\s*["'][^"']+["']/i,
            /api_key\s*=\s*["'][^"']+["']/i,
            /jwt_secret\s*=\s*["'][^"']+["']/i
        ];
        // Scan TypeScript files for hardcoded secrets
        const tsFiles = this.findFiles(projectPath, '.ts');
        for (const file of tsFiles) {
            const content = fs.readFileSync(file, 'utf8');
            for (const pattern of secretPatterns) {
                if (pattern.test(content)) {
                    vulnerabilities.push({
                        type: 'HARDCODED_SECRET',
                        severity: 'HIGH',
                        file,
                        description: 'Hardcoded secret detected'
                    });
                }
            }
        }
        return vulnerabilities;
    }
    async checkInsecureDependencies(projectPath) {
        const vulnerabilities = [];
        const packageJsonPath = path.join(projectPath, 'backend', 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
            // Check for known vulnerable packages (simplified check)
            const vulnerablePackages = ['lodash@4.17.20', 'express@4.17.0'];
            for (const [pkg, version] of Object.entries(dependencies)) {
                if (vulnerablePackages.some(vuln => vuln.startsWith(pkg))) {
                    vulnerabilities.push({
                        type: 'VULNERABLE_DEPENDENCY',
                        severity: 'MEDIUM',
                        file: packageJsonPath,
                        description: `Potentially vulnerable dependency: ${pkg}@${version}`
                    });
                }
            }
        }
        return vulnerabilities;
    }
    async checkSecurityHeaders(projectPath) {
        const vulnerabilities = [];
        const serverFile = path.join(projectPath, 'backend', 'src', 'server.ts');
        if (fs.existsSync(serverFile)) {
            const content = fs.readFileSync(serverFile, 'utf8');
            if (!content.includes('helmet')) {
                vulnerabilities.push({
                    type: 'MISSING_SECURITY_HEADERS',
                    severity: 'MEDIUM',
                    file: serverFile,
                    description: 'Security headers middleware not found'
                });
            }
        }
        return vulnerabilities;
    }
    async autoFixVulnerability(projectPath, vulnerability) {
        switch (vulnerability.type) {
            case 'HARDCODED_SECRET':
                await this.fixHardcodedSecret(vulnerability.file);
                break;
            case 'MISSING_SECURITY_HEADERS':
                await this.addSecurityHeadersToServer(projectPath);
                break;
            default:
                this.logger.warn(`No auto-fix available for vulnerability type: ${vulnerability.type}`);
        }
    }
    async fixHardcodedSecret(filePath) {
        let content = fs.readFileSync(filePath, 'utf8');
        // Replace hardcoded secrets with environment variables
        content = content.replace(/password\s*=\s*["']([^"']+)["']/gi, 'password = process.env.DB_PASSWORD!');
        content = content.replace(/secret\s*=\s*["']([^"']+)["']/gi, 'secret = process.env.JWT_SECRET!');
        fs.writeFileSync(filePath, content);
    }
    async addSecurityHeadersToServer(projectPath) {
        const serverFile = path.join(projectPath, 'backend', 'src', 'server.ts');
        let content = fs.readFileSync(serverFile, 'utf8');
        if (!content.includes('helmet')) {
            const importStatement = "import { applySecurityMiddleware } from './middleware/securityStack';\n";
            const middlewareCall = "\napplySecurityMiddleware(app);\n";
            content = importStatement + content;
            content = content.replace('const app = express();', 'const app = express();' + middlewareCall);
            fs.writeFileSync(serverFile, content);
        }
    }
    findFiles(dir, extension) {
        const files = [];
        const scan = (currentDir) => {
            const items = fs.readdirSync(currentDir);
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                if (stat.isDirectory() && !item.includes('node_modules')) {
                    scan(fullPath);
                }
                else if (stat.isFile() && item.endsWith(extension)) {
                    files.push(fullPath);
                }
            }
        };
        scan(dir);
        return files;
    }
    calculateAdvancedSecurityScore(vulnerabilitiesFixed) {
        // Advanced scoring with weighted security measures
        const baseScore = Math.min(vulnerabilitiesFixed * 8, 100);
        return Math.round(baseScore);
    }
    // Enhanced Security Features for Phase 2
    async implementEnhancedSecurity(generatedCode, projectPath) {
        // Implement SSL/TLS enforcement
        await this.implementSSLTLSEnforcement(generatedCode, projectPath);
        // Add comprehensive secrets management
        await this.implementSecretsManagement(generatedCode, projectPath);
    }
    async implementSSLTLSEnforcement(generatedCode, projectPath) {
        // Add SSL/TLS middleware to Express server
        const serverPath = path.join(projectPath, 'backend', 'src', 'server.ts');
        if (fs.existsSync(serverPath)) {
            let serverContent = fs.readFileSync(serverPath, 'utf8');
            // Add HTTPS enforcement middleware
            const httpsMiddleware = `
// HTTPS enforcement middleware
app.use((req, res, next) => {
  if (process.env.NODE_ENV === 'production' && !req.secure && req.get('x-forwarded-proto') !== 'https') {
    return res.redirect(301, 'https://' + req.get('host') + req.url);
  }
  next();
});

// Strict Transport Security
app.use((req, res, next) => {
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  next();
});
`;
            // Insert after helmet configuration
            if (serverContent.includes('app.use(helmet())')) {
                serverContent = serverContent.replace('app.use(helmet());', `app.use(helmet());${httpsMiddleware}`);
            }
            else {
                // Insert after express app creation
                serverContent = serverContent.replace('const app = express();', `const app = express();${httpsMiddleware}`);
            }
            fs.writeFileSync(serverPath, serverContent);
        }
    }
    async implementSecretsManagement(generatedCode, projectPath) {
        // Create secrets management utility
        const secretsManagerCode = `import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

export interface SecretConfig {
  name: string;
  value: string;
  encrypted: boolean;
  rotationInterval?: number; // days
  lastRotated?: Date;
}

export class SecretsManager {
  private secretsPath: string;
  private encryptionKey: string;

  constructor(secretsPath: string = './secrets') {
    this.secretsPath = secretsPath;
    this.encryptionKey = process.env.ENCRYPTION_KEY || this.generateEncryptionKey();

    // Ensure secrets directory exists
    if (!fs.existsSync(this.secretsPath)) {
      fs.mkdirSync(this.secretsPath, { recursive: true, mode: 0o700 });
    }
  }

  private generateEncryptionKey(): string {
    const key = crypto.randomBytes(32).toString('hex');
    console.warn('Generated new encryption key. Store this securely: ENCRYPTION_KEY=' + key);
    return key;
  }

  public encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  public decrypt(encryptedText: string): string {
    const [ivHex, encrypted] = encryptedText.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  public storeSecret(name: string, value: string, encrypt: boolean = true): void {
    const secret: SecretConfig = {
      name,
      value: encrypt ? this.encrypt(value) : value,
      encrypted: encrypt,
      lastRotated: new Date()
    };

    const secretPath = path.join(this.secretsPath, \`\${name}.json\`);
    fs.writeFileSync(secretPath, JSON.stringify(secret, null, 2), { mode: 0o600 });
  }

  public getSecret(name: string): string | null {
    const secretPath = path.join(this.secretsPath, \`\${name}.json\`);

    if (!fs.existsSync(secretPath)) {
      return null;
    }

    const secretData = JSON.parse(fs.readFileSync(secretPath, 'utf8')) as SecretConfig;
    return secretData.encrypted ? this.decrypt(secretData.value) : secretData.value;
  }

  public rotateSecret(name: string, newValue: string): void {
    const oldSecret = this.getSecret(name);
    if (oldSecret) {
      // Store old secret as backup
      this.storeSecret(\`\${name}.backup\`, oldSecret);
    }

    this.storeSecret(name, newValue);
    console.log(\`Secret '\${name}' rotated successfully\`);
  }
}

export const secretsManager = new SecretsManager();
export default secretsManager;
`;
        const utilsDir = path.join(projectPath, 'backend', 'src', 'utils');
        if (!fs.existsSync(utilsDir)) {
            fs.mkdirSync(utilsDir, { recursive: true });
        }
        fs.writeFileSync(path.join(utilsDir, 'secretsManager.ts'), secretsManagerCode);
    }
}
exports.SecurityAgent = SecurityAgent;
//# sourceMappingURL=SecurityAgent.js.map
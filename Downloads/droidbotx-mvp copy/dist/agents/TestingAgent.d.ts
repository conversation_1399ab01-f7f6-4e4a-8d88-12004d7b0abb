import { BaseAgent, AgentTask, AgentResult } from '../core/BaseAgent';
export interface ProductionReadinessMetrics {
    compilation: {
        backend: boolean;
        frontend: boolean;
        errorCount: number;
        warningCount: number;
    };
    dependencies: {
        resolved: boolean;
        conflicts: number;
        missingCount: number;
    };
    apis: {
        generated: number;
        functional: number;
        coverage: number;
    };
    database: {
        connected: boolean;
        schema: boolean;
        migrationStatus: boolean;
    };
    security: {
        authentication: boolean;
        validation: boolean;
        encryption: boolean;
    };
    deployment: {
        docker: boolean;
        scripts: boolean;
        environment: boolean;
    };
    quality: {
        codeStructure: number;
        errorHandling: number;
        documentation: number;
    };
    overall: {
        compilationScore: number;
        functionalityScore: number;
        qualityScore: number;
        isProductionReady: boolean;
    };
}
export interface TestingResult {
    success: boolean;
    testsRun: number;
    testsPassed: number;
    testsFailed: number;
    fixesApplied: number;
    actualSuccessRate?: number;
    compilationErrors: string[];
    missingFiles: string[];
    dependencyIssues: string[];
    apiConnectivityIssues: string[];
    databaseIssues: string[];
    iterations: number;
}
export interface DatabaseConfig {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    containerId?: string;
}
export declare class TestingAgent extends BaseAgent {
    private maxIterations;
    private databaseConfig;
    private feedbackManager;
    private coordinatedFixingStrategy;
    private preCompilationValidator;
    private coordinationLockManager;
    constructor();
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    private runComprehensiveTesting;
    private setupTestingInfrastructure;
    private resolveDependencyIssues;
    private validateCodeQuality;
    private setupTestDirectories;
    private generateTestConfiguration;
    private removeJestConfigFromPackageJson;
    private installTestingDependencies;
    private generateAndRunUnitTests;
    private generateAndRunIntegrationTests;
    private generateAndRunE2ETests;
    private runSecurityTests;
    private runPerformanceTests;
    private performAutoFixing;
    private calculateTestingSuccess;
    private detectMissingFiles;
    private findMissingCSSFiles;
    private checkAndInstallDependencies;
    /**
     * SecurityAgent-style defensive route checking with simple existence validation
     * Replaces complex coordination logic with defensive programming
     */
    private checkAndFixMissingRoutes;
    /**
     * SecurityAgent-style safe route file discovery
     */
    private getRouteFilesSafely;
    private generateMissingReactComponents;
    private extractComponentReferencesFromApp;
    private extractPropsFromJSX;
    private resolveComponentPath;
    private generateComponentStub;
    private createMissingFiles;
    private createEnvironmentFile;
    private createCSSFile;
    private createReadmeFile;
    private createDockerComposeFile;
    private checkDependencies;
    private fixDependencyIssues;
    private addBackendDependency;
    private addFrontendDependency;
    private testCompilation;
    private runEnhancedCompilationValidation;
    private runPreCompilationValidation;
    private validateTypeScriptFiles;
    private getAllTypeScriptFiles;
    private validateSingleTypeScriptFile;
    private validateSchemaConsistency;
    private validateFieldMappings;
    private validateRouteFieldUsage;
    private validateEntityReferences;
    private validateCrossLayerIntegration;
    private validateAPIContracts;
    private extractBackendEndpoints;
    private extractFrontendAPICalls;
    private validateServiceIntegration;
    private validateServiceUsageInRoute;
    private validateImportExportConsistency;
    private validateServiceInstantiation;
    private validateTypeDefinitions;
    private validateDatabaseFieldMapping;
    private fixSchemaConsistencyIssues;
    private fixAPIContractIssues;
    private fixServiceInstantiationIssues;
    private fixFieldMappingIssues;
    private fixMissingCustomerEntity;
    private fixDuplicateInventoryTracking;
    private addMissingBackendEndpoint;
    private generateEndpointCode;
    private fixMixedServiceCalls;
    private fixMissingServiceInstantiation;
    private prioritizeErrors;
    private fixCompilationErrors;
    private fixImportExportIssues;
    private fixMissingComponents;
    private generateReactComponent;
    private fixMissingServices;
    private generateServiceContent;
    private fixMissingRouteFiles;
    private generateRouteContent;
    private generateMissingServiceIfNeeded;
    private fixMissingTypeImports;
    private findTypeDefinition;
    private fixMissingPropTypes;
    private fixTypeScriptErrorHandling;
    private testDatabaseConnection;
    /**
     * Attempt direct database connection with multiple password attempts
     * Priority 3 Fix: Graceful handling of database connection issues
     */
    private attemptDirectDatabaseConnection;
    /**
     * Create fallback database configuration when connection fails
     * SecurityAgent-style defensive fallback database configuration
     */
    private createFallbackDatabaseConfig;
    private fixDatabaseIssues;
    private testAPIConnectivity;
    private fixAPIConnectivityIssues;
    private calculateProductionReadinessMetrics;
    private countGeneratedAPIs;
    private countFunctionalAPIs;
    private checkDatabaseSchema;
    private checkMigrationStatus;
    private checkAuthentication;
    private checkInputValidation;
    private checkEncryption;
    private checkDockerConfiguration;
    private checkDeploymentScripts;
    private checkEnvironmentConfiguration;
    private assessCodeStructure;
    private assessErrorHandling;
    private assessDocumentation;
    private calculateCompilationScore;
    private calculateFunctionalityScore;
    private calculateQualityScore;
    private generateServiceUnitTests;
    /**
     * Generate enhanced service test with meaningful assertions and proper error handling
     */
    /**
     * SecurityAgent-style defensive test generation with proper TypeScript types
     */
    private generateEnhancedServiceTest;
    private generateComponentUnitTests;
    private runUnitTests;
    private generateAPIIntegrationTests;
    private generateDatabaseIntegrationTests;
    private runIntegrationTests;
    private generateE2ETests;
    private runE2ETests;
    private runSecurityAudit;
    private fixSecurityIssues;
    private checkPerformance;
    private optimizePerformance;
    private autoFixCodeIssues;
    private autoFixTestFailures;
    private getAllSourceFiles;
    private fixCriticalImportExportIssues;
    private fixMissingServiceFiles;
    private fixDatabaseConfigurationIssues;
    private rerunTestsAfterFixes;
    private fixRemainingCompilationIssues;
    private clearModuleCache;
    /**
     * SecurityAgent-style defensive pre-compilation validation
     * Replaces complex coordination with simple defensive checks
     */
    private performCoordinatedPreCompilationValidation;
    /**
     * Detect actual methods in a service file to ensure tests only reference existing methods
     * This addresses Priority 2: Service Method Alignment in Generated Tests
     */
    private detectServiceMethods;
    /**
     * Generate enhanced service test with dynamic method detection
     * Only generates tests for methods that actually exist in the service
     */
    private generateEnhancedServiceTestWithMethodDetection;
    /**
     * SecurityAgent-style defensive service method pattern fixing
     */
    private fixServiceMethodPatterns;
    /**
     * Fix route files to use correct service imports and resolve naming conflicts
     */
    private fixRouteServiceImports;
    /**
     * SecurityAgent-style comprehensive file extension fixing
     */
    private fixMissingFileExtensions;
    private fixServiceExportPatterns;
    private fixTestServiceAlignment;
    private setupEnvironmentVariables;
    private getAllTestFiles;
    private fixDuplicateDeclarations;
    private getAllTSFiles;
    private applyPreCompilationAutoFixes;
    private applyFileSpecificAutoFixes;
    /**
     * Enhanced comprehensive testing with coordination
     */
    private runEnhancedComprehensiveTesting;
    /**
     * Generate feedback for coordination with other agents
     */
    private generateTestingFeedback;
    /**
     * Validate TypeScript compilation before running tests
     */
    private validateCompilation;
    /**
     * Check TypeScript compilation for a specific project
     */
    private checkTypeScriptCompilation;
    /**
     * Run basic validation tests when compilation fails
     * This allows the testing to proceed with partial validation instead of complete failure
     */
    private runBasicValidationTests;
}
//# sourceMappingURL=TestingAgent.d.ts.map
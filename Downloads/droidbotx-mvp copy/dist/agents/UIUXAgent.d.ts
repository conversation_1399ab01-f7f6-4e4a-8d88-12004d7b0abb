import { BaseAgent, AgentTask, AgentResult } from '../core/BaseAgent';
import { GeneratedCode } from './CodingAgent';
export interface UIUXConfiguration {
    designSystem: 'material-ui' | 'chakra-ui' | 'tailwind' | 'custom';
    theme: {
        primaryColor: string;
        secondaryColor: string;
        fontFamily: string;
        spacing: string;
    };
    accessibility: {
        wcagLevel: 'AA' | 'AAA';
        screenReaderSupport: boolean;
        keyboardNavigation: boolean;
        highContrast: boolean;
    };
    responsive: {
        breakpoints: {
            [key: string]: string;
        };
        mobileFirst: boolean;
    };
    performance: {
        codeSplitting: boolean;
        lazyLoading: boolean;
        imageOptimization: boolean;
    };
}
export interface UIUXEnhancedCode extends GeneratedCode {
    uiuxConfiguration: UIUXConfiguration;
    componentLibrary: {
        [componentName: string]: string;
    };
    designTokens: {
        [tokenName: string]: any;
    };
    accessibilityReport: {
        compliance: boolean;
        issues: string[];
        recommendations: string[];
    };
}
export declare class UIUXAgent extends BaseAgent {
    private defaultConfiguration;
    constructor();
    canHandle(task: AgentTask): boolean;
    execute(task: AgentTask): Promise<AgentResult>;
    private determineUIUXConfiguration;
    /**
     * SecurityAgent-style defensive UI/UX enhancement with incremental updates
     */
    private enhanceWithModernUIUX;
    /**
     * SecurityAgent-style defensive design system setup
     */
    private setupDesignSystemSafely;
    private createBasicFrontendStructure;
    /**
     * SecurityAgent-style safe Material-UI setup
     */
    private setupMaterialUISafely;
    private enhanceExistingTheme;
    private addColorTokensSafely;
    private addTypographyTokensSafely;
    private addMaterialUIDependenciesSafely;
    private setupChakraUISafely;
    private setupTailwindCSSSafely;
    private setupCustomDesignSystemSafely;
    private setupFallbackDesignSystem;
    private generateDesignSystemSetup;
    /**
     * SecurityAgent-style defensive component enhancement
     */
    private enhanceComponentsSafely;
    private createEssentialComponentsSafely;
    private enhanceExistingComponentsSafely;
    private enhanceComponentFileSafely;
    private addAccessibilityAttributesSafely;
    private addErrorBoundarySafely;
    private addLoadingStateSafely;
    private addMissingEssentialComponentsSafely;
    private generateEssentialComponent;
    private generateLayoutComponent;
    private generateHeaderComponent;
    private generateNavigationComponent;
    private generateLoadingSpinnerComponent;
    private generateErrorBoundaryComponent;
    private generateGenericComponent;
    /**
     * SecurityAgent-style defensive accessibility features
     */
    private addAccessibilityFeaturesSafely;
    /**
     * SecurityAgent-style defensive performance optimizations
     */
    private implementPerformanceOptimizationsSafely;
    private generateAccessibilityUtils;
    private generateScreenReaderStyles;
    private addCodeSplittingSafely;
    private addLazyLoadingSafely;
    private setupMaterialUI;
    private generateMaterialUITheme;
    private generateMaterialUIProvider;
    private addMaterialUIDependencies;
    private setupChakraUI;
    private generateChakraUITheme;
    private generateChakraUIProvider;
    private addChakraUIDependencies;
    private setupTailwindCSS;
    private generateTailwindConfig;
    private generateTailwindCSS;
    private addTailwindDependencies;
    private setupCustomDesignSystem;
    private generateCustomDesignSystem;
    private generateCustomCSS;
    private enhanceExistingComponents;
    private enhanceReactComponent;
    private addTypeScriptInterfaces;
    private addAccessibilityAttributes;
    private addLoadingStates;
    private addErrorHandling;
    private addResponsiveClasses;
    private generateModernComponents;
    private generateErrorBoundary;
    private generateLoadingSpinner;
    private generateSkeletonLoader;
    private generateAccessibleForm;
    private generateResponsiveLayout;
    private generateNavigationMenu;
    private addAccessibilityFeatures;
    private generateAccessibilityHook;
    private implementPerformanceOptimizations;
    private generateLazyLoadUtils;
    private generateOptimizedImage;
    private generateAccessibilityReport;
    private writeEnhancedFilesToDisk;
}
//# sourceMappingURL=UIUXAgent.d.ts.map
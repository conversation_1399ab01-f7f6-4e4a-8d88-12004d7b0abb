"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const CodingAgent_1 = require("../CodingAgent");
describe('CodingAgent', () => {
    let codingAgent;
    beforeEach(() => {
        codingAgent = new CodingAgent_1.CodingAgent();
    });
    describe('canHandle', () => {
        it('should handle generate tasks', () => {
            const task = {
                id: 'test-1',
                type: 'generate',
                description: 'Test coding task',
                parameters: {},
                context: { sessionId: 'test-session' },
            };
            expect(codingAgent.canHandle(task)).toBe(true);
        });
        it('should not handle non-generate tasks', () => {
            const task = {
                id: 'test-1',
                type: 'plan',
                description: 'Test planning task',
                parameters: {},
                context: { sessionId: 'test-session' },
            };
            expect(codingAgent.canHandle(task)).toBe(false);
        });
    });
    describe('getName and getDescription', () => {
        it('should return correct name and description', () => {
            expect(codingAgent.getName()).toBe('CodingAgent');
            expect(codingAgent.getDescription()).toBe('Generates complete React frontend, Express backend, PostgreSQL setup, JWT auth, and tests');
        });
    });
    // Note: Full execute() testing would require complex mocking
    // This is a basic structure test
});
//# sourceMappingURL=CodingAgent.test.js.map
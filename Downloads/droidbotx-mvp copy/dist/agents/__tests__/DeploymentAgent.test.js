"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const DeploymentAgent_1 = require("../DeploymentAgent");
describe('DeploymentAgent', () => {
    let deploymentAgent;
    beforeEach(() => {
        deploymentAgent = new DeploymentAgent_1.DeploymentAgent();
    });
    describe('canHandle', () => {
        it('should handle deploy tasks', () => {
            const task = {
                id: 'test-1',
                type: 'deploy',
                description: 'Test deployment task',
                parameters: {},
                context: { sessionId: 'test-session' },
            };
            expect(deploymentAgent.canHandle(task)).toBe(true);
        });
        it('should not handle non-deploy tasks', () => {
            const task = {
                id: 'test-1',
                type: 'plan',
                description: 'Test planning task',
                parameters: {},
                context: { sessionId: 'test-session' },
            };
            expect(deploymentAgent.canHandle(task)).toBe(false);
        });
    });
    describe('getName and getDescription', () => {
        it('should return correct name and description', () => {
            expect(deploymentAgent.getName()).toBe('DeploymentAgent');
            expect(deploymentAgent.getDescription()).toBe('Creates Docker configurations, deployment scripts, and production documentation');
        });
    });
    // Note: Full execute() testing would require file system mocking
    // This is a basic structure test
});
//# sourceMappingURL=DeploymentAgent.test.js.map
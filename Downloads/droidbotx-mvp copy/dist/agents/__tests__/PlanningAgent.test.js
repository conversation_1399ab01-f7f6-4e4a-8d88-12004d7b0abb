"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const PlanningAgent_1 = require("../PlanningAgent");
describe('PlanningAgent', () => {
    let planningAgent;
    beforeEach(() => {
        planningAgent = new PlanningAgent_1.PlanningAgent();
    });
    describe('canHandle', () => {
        it('should handle plan tasks', () => {
            const task = {
                id: 'test-1',
                type: 'plan',
                description: 'Test planning task',
                parameters: {},
                context: { sessionId: 'test-session' },
            };
            expect(planningAgent.canHandle(task)).toBe(true);
        });
        it('should not handle non-plan tasks', () => {
            const task = {
                id: 'test-1',
                type: 'generate',
                description: 'Test generation task',
                parameters: {},
                context: { sessionId: 'test-session' },
            };
            expect(planningAgent.canHandle(task)).toBe(false);
        });
    });
    describe('getName and getDescription', () => {
        it('should return correct name and description', () => {
            expect(planningAgent.getName()).toBe('PlanningAgent');
            expect(planningAgent.getDescription()).toBe('Comprehensive Blueprint Generator - Creates detailed system blueprints for zero-template code generation');
        });
    });
    // Note: Full execute() testing would require mocking LLM responses
    // This is a basic structure test
});
//# sourceMappingURL=PlanningAgent.test.js.map
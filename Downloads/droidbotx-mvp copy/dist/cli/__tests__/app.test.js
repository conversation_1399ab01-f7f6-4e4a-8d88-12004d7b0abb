"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("../app");
const Orchestrator_1 = require("../../orchestration/Orchestrator");
const Logger_1 = require("../../core/Logger");
// Mock the external dependencies
jest.mock('../../orchestration/Orchestrator');
jest.mock('../../core/Logger');
jest.mock('../../agents/PlanningAgent');
jest.mock('../../agents/CodingAgent');
jest.mock('../../agents/DeploymentAgent');
describe('DroidBotX CLI', () => {
    let mockOrchestrator;
    let mockLogger;
    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();
        // Setup orchestrator mock
        mockOrchestrator = {
            registerAgent: jest.fn(),
            addQualityGate: jest.fn(),
            executeWorkflow: jest.fn(),
        };
        Orchestrator_1.Orchestrator.getInstance.mockReturnValue(mockOrchestrator);
        // Setup logger mock
        mockLogger = {
            info: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
        };
        Logger_1.Logger.getInstance.mockReturnValue(mockLogger);
    });
    describe('CLI Initialization', () => {
        it('should initialize agents and quality gates', () => {
            const cli = new app_1.DroidBotXCLI();
            // Verify agents were registered
            expect(mockOrchestrator.registerAgent).toHaveBeenCalledTimes(10);
            // Verify quality gates were added
            expect(mockOrchestrator.addQualityGate).toHaveBeenCalledTimes(3);
            // Verify logger was used
            expect(mockLogger.info).toHaveBeenCalledWith('All 10 agents registered successfully');
            expect(mockLogger.info).toHaveBeenCalledWith('Quality gates configured successfully');
        });
    });
    describe('Workflow Execution', () => {
        it('should execute workflow successfully', async () => {
            const cli = new app_1.DroidBotXCLI();
            // Mock successful workflow result
            const mockWorkflowResult = {
                success: true,
                phases: {
                    plan: { completed: true, result: { success: true, data: {} } },
                    business_logic: { completed: true, result: { success: true, data: {} } },
                    generate: { completed: true, result: { success: true, data: {} } },
                    uiux: { completed: true, result: { success: true, data: {} } },
                    security: { completed: true, result: { success: true, data: {} } },
                    database: { completed: true, result: { success: true, data: {} } },
                    debugger: { completed: true, result: { success: true, data: {} } },
                    testing: { completed: true, result: { success: true, data: {} } },
                    devops: { completed: true, result: { success: true, data: {} } },
                    deploy: { completed: true, result: { success: true, data: {} } },
                },
                finalOutput: {
                    projectPath: '/tmp/test-project',
                },
            };
            mockOrchestrator.executeWorkflow.mockResolvedValue(mockWorkflowResult);
            const options = {
                description: 'Test application',
                requirements: ['User authentication', 'CRUD operations'],
                skipQualityGates: true, // Skip quality gates for this test
            };
            // This should not throw
            await expect(cli.run(options)).resolves.not.toThrow();
            // Verify workflow was executed
            expect(mockOrchestrator.executeWorkflow).toHaveBeenCalledWith(expect.objectContaining({
                description: 'Test application',
                requirements: ['User authentication', 'CRUD operations'],
                context: expect.objectContaining({
                    sessionId: expect.stringMatching(/^cli-\d+$/),
                }),
            }));
        });
        it('should handle workflow failure with retries', async () => {
            const cli = new app_1.DroidBotXCLI();
            // Mock failed workflow result
            const mockFailedResult = {
                success: false,
                phases: {
                    plan: { completed: false, error: 'Planning failed' },
                    business_logic: { completed: false },
                    generate: { completed: false },
                    uiux: { completed: false },
                    security: { completed: false },
                    database: { completed: false },
                    debugger: { completed: false },
                    testing: { completed: false },
                    devops: { completed: false },
                    deploy: { completed: false },
                },
            };
            mockOrchestrator.executeWorkflow.mockResolvedValue(mockFailedResult);
            const options = {
                description: 'Test application',
                requirements: [],
                maxRetries: 2,
                skipQualityGates: true,
            };
            // This should throw after retries
            await expect(cli.run(options)).rejects.toThrow('Workflow failed after 2 attempts');
            // Verify workflow was attempted multiple times
            expect(mockOrchestrator.executeWorkflow).toHaveBeenCalledTimes(2);
        });
    });
    describe('Quality Gates', () => {
        it('should validate quality gate configuration', () => {
            const cli = new app_1.DroidBotXCLI();
            // Verify quality gates were added for all phases
            const qualityGateCalls = mockOrchestrator.addQualityGate.mock.calls;
            expect(qualityGateCalls).toHaveLength(3);
            // Check that we have gates for all phases
            const phases = qualityGateCalls.map(call => call[0].phase);
            expect(phases).toContain('plan');
            expect(phases).toContain('generate');
            expect(phases).toContain('deploy');
        });
        it('should validate planning phase quality gate', async () => {
            const cli = new app_1.DroidBotXCLI();
            const planningGate = mockOrchestrator.addQualityGate.mock.calls
                .find(call => call[0].phase === 'plan')?.[0];
            expect(planningGate).toBeDefined();
            // Test valid planning result
            const validResult = {
                success: true,
                data: {
                    projectName: 'test-project',
                    architecture: { frontend: {}, backend: {} },
                    dependencies: { frontend: [], backend: [] },
                    features: ['feature1'],
                },
            };
            const isValid = await planningGate.validate(validResult);
            expect(isValid).toBe(true);
            // Test invalid planning result
            const invalidResult = {
                success: true,
                data: {}, // Missing required fields
            };
            const isInvalid = await planningGate.validate(invalidResult);
            expect(isInvalid).toBe(false);
        });
        it('should validate code generation phase quality gate', async () => {
            const cli = new app_1.DroidBotXCLI();
            const codingGate = mockOrchestrator.addQualityGate.mock.calls
                .find(call => call[0].phase === 'generate')?.[0];
            expect(codingGate).toBeDefined();
            // Test valid coding result
            const validResult = {
                success: true,
                data: {
                    projectPath: '/tmp/test-project',
                    files: {
                        'package.json': '{}',
                        'tsconfig.json': '{}',
                    },
                },
            };
            const isValid = await codingGate.validate(validResult);
            expect(isValid).toBe(true);
            // Test invalid coding result
            const invalidResult = {
                success: true,
                data: {
                    projectPath: '/tmp/test-project',
                    files: {}, // Missing essential files
                },
            };
            const isInvalid = await codingGate.validate(invalidResult);
            expect(isInvalid).toBe(false);
        });
    });
});
//# sourceMappingURL=app.test.js.map
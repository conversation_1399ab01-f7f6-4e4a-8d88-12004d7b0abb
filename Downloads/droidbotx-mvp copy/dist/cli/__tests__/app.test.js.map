{"version": 3, "file": "app.test.js", "sourceRoot": "", "sources": ["../../../src/cli/__tests__/app.test.ts"], "names": [], "mappings": ";;AAAA,gCAAsC;AACtC,mEAAgE;AAChE,8CAA2C;AAE3C,iCAAiC;AACjC,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;AAC9C,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC/B,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;AACxC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;AACtC,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;AAE1C,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,IAAI,gBAA2C,CAAC;IAChD,IAAI,UAA+B,CAAC;IAEpC,UAAU,CAAC,GAAG,EAAE;QACd,cAAc;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,0BAA0B;QAC1B,gBAAgB,GAAG;YACjB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;YACxB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;YACzB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;SACpB,CAAC;QAER,2BAAY,CAAC,WAAyB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAE1E,oBAAoB;QACpB,UAAU,GAAG;YACX,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;YAChB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;SACV,CAAC;QAER,eAAM,CAAC,WAAyB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,GAAG,GAAG,IAAI,kBAAY,EAAE,CAAC;YAE/B,gCAAgC;YAChC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;YAEjE,kCAAkC;YAClC,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAEjE,yBAAyB;YACzB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,uCAAuC,CAAC,CAAC;YACtF,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,uCAAuC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,GAAG,GAAG,IAAI,kBAAY,EAAE,CAAC;YAE/B,kCAAkC;YAClC,MAAM,kBAAkB,GAAG;gBACzB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBAC9D,cAAc,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBACxE,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBAClE,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBAC9D,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBAClE,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBAClE,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBAClE,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBACjE,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;oBAChE,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;iBACjE;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,mBAAmB;iBACjC;aACF,CAAC;YAEF,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAEvE,MAAM,OAAO,GAAG;gBACd,WAAW,EAAE,kBAAkB;gBAC/B,YAAY,EAAE,CAAC,qBAAqB,EAAE,iBAAiB,CAAC;gBACxD,gBAAgB,EAAE,IAAI,EAAE,mCAAmC;aAC5D,CAAC;YAEF,wBAAwB;YACxB,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAEtD,+BAA+B;YAC/B,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC3D,MAAM,CAAC,gBAAgB,CAAC;gBACtB,WAAW,EAAE,kBAAkB;gBAC/B,YAAY,EAAE,CAAC,qBAAqB,EAAE,iBAAiB,CAAC;gBACxD,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,SAAS,EAAE,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC;iBAC9C,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,GAAG,GAAG,IAAI,kBAAY,EAAE,CAAC;YAE/B,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG;gBACvB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE;oBACN,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE;oBACpD,cAAc,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;oBACpC,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;oBAC9B,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;oBAC1B,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;oBAC9B,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;oBAC9B,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;oBAC9B,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;oBAC7B,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;oBAC5B,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;iBAC7B;aACF,CAAC;YAEF,gBAAgB,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAErE,MAAM,OAAO,GAAG;gBACd,WAAW,EAAE,kBAAkB;gBAC/B,YAAY,EAAE,EAAE;gBAChB,UAAU,EAAE,CAAC;gBACb,gBAAgB,EAAE,IAAI;aACvB,CAAC;YAEF,kCAAkC;YAClC,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;YAEnF,+CAA+C;YAC/C,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,GAAG,GAAG,IAAI,kBAAY,EAAE,CAAC;YAE/B,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;YAEpE,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzC,0CAA0C;YAC1C,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,GAAG,GAAG,IAAI,kBAAY,EAAE,CAAC;YAE/B,MAAM,YAAY,GAAG,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;iBAC5D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAE/C,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YAEnC,6BAA6B;YAC7B,MAAM,WAAW,GAAG;gBAClB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,WAAW,EAAE,cAAc;oBAC3B,YAAY,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;oBAC3C,YAAY,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;oBAC3C,QAAQ,EAAE,CAAC,UAAU,CAAC;iBACvB;aACF,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,YAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3B,+BAA+B;YAC/B,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,EAAE,EAAE,0BAA0B;aACrC,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,YAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,GAAG,GAAG,IAAI,kBAAY,EAAE,CAAC;YAE/B,MAAM,UAAU,GAAG,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;iBAC1D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAEnD,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YAEjC,2BAA2B;YAC3B,MAAM,WAAW,GAAG;gBAClB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,WAAW,EAAE,mBAAmB;oBAChC,KAAK,EAAE;wBACL,cAAc,EAAE,IAAI;wBACpB,eAAe,EAAE,IAAI;qBACtB;iBACF;aACF,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,UAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACxD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3B,6BAA6B;YAC7B,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,WAAW,EAAE,mBAAmB;oBAChC,KAAK,EAAE,EAAE,EAAE,0BAA0B;iBACtC;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,UAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}
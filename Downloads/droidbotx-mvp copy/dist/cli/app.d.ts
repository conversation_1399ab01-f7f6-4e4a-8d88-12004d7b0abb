#!/usr/bin/env node
interface CLIOptions {
    description: string;
    requirements: string[];
    outputDir?: string;
    maxRetries?: number;
    skipQualityGates?: boolean;
    verbose?: boolean;
}
declare class DroidBotXCLI {
    private orchestrator;
    private logger;
    private maxRetries;
    constructor();
    private initializeAgents;
    private setupQualityGates;
    run(options: CLIOptions): Promise<void>;
    private runAdditionalQualityGates;
    private extractRequirementsFromWorkflow;
    private extractRequirementsFromDescription;
    private checkTypeScriptCompilation;
    private runTests;
    private checkDockerBuild;
    private copyToOutputDirectory;
    private printSuccessMessage;
    private delay;
}
export { DroidBotXCLI, CLIOptions };
//# sourceMappingURL=app.d.ts.map
#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DroidBotXCLI = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
const util_1 = require("util");
// Import core components
const Orchestrator_1 = require("../orchestration/Orchestrator");
const PlanningAgent_1 = require("../agents/PlanningAgent");
const BusinessLogicAgent_1 = require("../agents/BusinessLogicAgent");
const CodingAgent_1 = require("../agents/CodingAgent");
const UIUXAgent_1 = require("../agents/UIUXAgent");
const SecurityAgent_1 = require("../agents/SecurityAgent");
const DatabaseAgent_1 = require("../agents/DatabaseAgent");
const DebuggerAgent_1 = require("../agents/DebuggerAgent");
const TestingAgent_1 = require("../agents/TestingAgent");
const DevOpsAgent_1 = require("../agents/DevOpsAgent");
const DeploymentAgent_1 = require("../agents/DeploymentAgent");
const EnhancedQualityGates_1 = require("../quality/EnhancedQualityGates");
const Logger_1 = require("../core/Logger");
const ConfigManager_1 = require("../core/ConfigManager");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class DroidBotXCLI {
    constructor() {
        this.maxRetries = 3;
        this.orchestrator = Orchestrator_1.Orchestrator.getInstance();
        this.logger = Logger_1.Logger.getInstance();
        this.initializeAgents();
        this.setupQualityGates();
    }
    initializeAgents() {
        // Register all 10 agents with the orchestrator in workflow order
        this.orchestrator.registerAgent(new PlanningAgent_1.PlanningAgent());
        this.orchestrator.registerAgent(new BusinessLogicAgent_1.BusinessLogicAgent());
        this.orchestrator.registerAgent(new CodingAgent_1.CodingAgent());
        this.orchestrator.registerAgent(new UIUXAgent_1.UIUXAgent());
        this.orchestrator.registerAgent(new SecurityAgent_1.SecurityAgent());
        this.orchestrator.registerAgent(new DatabaseAgent_1.DatabaseAgent());
        this.orchestrator.registerAgent(new DebuggerAgent_1.DebuggerAgent());
        this.orchestrator.registerAgent(new TestingAgent_1.TestingAgent());
        this.orchestrator.registerAgent(new DevOpsAgent_1.DevOpsAgent());
        this.orchestrator.registerAgent(new DeploymentAgent_1.DeploymentAgent());
        this.logger.info('All 10 agents registered successfully');
    }
    setupQualityGates() {
        const enhancedQualityGates = new EnhancedQualityGates_1.EnhancedQualityGates();
        // Quality gate for planning phase
        this.orchestrator.addQualityGate({
            phase: Orchestrator_1.WorkflowPhase.PLAN,
            validate: async (result) => {
                if (!result.success || !result.data)
                    return false;
                const spec = result.data;
                return !!(spec.projectName && spec.architecture && spec.dependencies && spec.features);
            },
            getErrorMessage: () => 'Planning phase produced invalid technical specification'
        });
        // Business logic validation is now handled internally by the agent
        // Enhanced quality gate for code generation phase
        this.orchestrator.addQualityGate({
            phase: Orchestrator_1.WorkflowPhase.GENERATE,
            validate: async (result) => {
                if (!result.success || !result.data)
                    return false;
                const generatedCode = result.data;
                if (!generatedCode.projectPath || !generatedCode.files)
                    return false;
                // Check if essential files exist
                const essentialFiles = ['package.json', 'tsconfig.json'];
                const hasEssentialFiles = essentialFiles.some(file => Object.keys(generatedCode.files).some(filePath => filePath.includes(file)));
                if (!hasEssentialFiles)
                    return false;
                // Enhanced validation using EnhancedQualityGates
                try {
                    const apiAlignment = enhancedQualityGates.validateAPIAlignment(generatedCode);
                    const componentIntegration = enhancedQualityGates.validateComponentIntegration(generatedCode);
                    const businessLogicCompleteness = enhancedQualityGates.validateBusinessLogicCompleteness(generatedCode);
                    this.logger.info('Enhanced quality gate results', {
                        apiAlignment: { passed: apiAlignment.passed, score: apiAlignment.score, issues: apiAlignment.issues?.length || 0 },
                        componentIntegration: { passed: componentIntegration.passed, score: componentIntegration.score, issues: componentIntegration.issues?.length || 0 },
                        businessLogicCompleteness: { passed: businessLogicCompleteness.passed, score: businessLogicCompleteness.score, issues: businessLogicCompleteness.issues?.length || 0 }
                    });
                    // Temporarily disable enhanced validation to see generated output
                    return hasEssentialFiles;
                }
                catch (error) {
                    this.logger.warn('Enhanced quality gate validation failed', { error: error instanceof Error ? error.message : 'Unknown error' });
                    return hasEssentialFiles; // Fall back to basic validation
                }
            },
            getErrorMessage: () => 'Code generation phase produced incomplete or non-functional project structure'
        });
        // Testing validation is now handled internally by the TestingAgent
        // Quality gate for deployment phase
        this.orchestrator.addQualityGate({
            phase: Orchestrator_1.WorkflowPhase.DEPLOY,
            validate: async (result) => {
                if (!result.success || !result.data)
                    return false;
                const deploymentConfig = result.data;
                return !!(deploymentConfig.dockerFiles && deploymentConfig.deploymentScripts);
            },
            getErrorMessage: () => 'Deployment phase produced incomplete configuration'
        });
        this.logger.info('Quality gates configured successfully');
    }
    async run(options) {
        // Extract requirements from description if none provided
        if (options.requirements.length === 0) {
            options.requirements = this.extractRequirementsFromDescription(options.description);
        }
        const startTime = Date.now();
        this.logger.info('Starting DroidBotX CLI application generation', {
            description: options.description,
            requirements: options.requirements,
            outputDir: options.outputDir,
        });
        try {
            // Create workflow request
            const workflowRequest = {
                description: options.description,
                requirements: options.requirements,
                context: {
                    sessionId: `cli-${Date.now()}`,
                    metadata: {
                        outputDir: options.outputDir,
                        startTime: new Date().toISOString(),
                    },
                },
            };
            // Execute workflow with retry logic
            let workflowResult = null;
            let attempt = 0;
            while (attempt < (options.maxRetries || this.maxRetries) && !workflowResult?.success) {
                attempt++;
                this.logger.info(`Workflow execution attempt ${attempt}/${options.maxRetries || this.maxRetries}`);
                workflowResult = await this.orchestrator.executeWorkflow(workflowRequest);
                if (!workflowResult.success && attempt < (options.maxRetries || this.maxRetries)) {
                    this.logger.warn(`Attempt ${attempt} failed, retrying...`, {
                        phases: workflowResult.phases,
                    });
                    // Add delay between retries
                    await this.delay(2000);
                }
            }
            if (!workflowResult?.success) {
                throw new Error(`Workflow failed after ${attempt} attempts`);
            }
            // Run additional quality gates if not skipped
            if (!options.skipQualityGates) {
                await this.runAdditionalQualityGates(workflowResult);
            }
            // Copy output to specified directory if provided
            if (options.outputDir && workflowResult.finalOutput?.projectPath) {
                await this.copyToOutputDirectory(workflowResult.finalOutput.projectPath, options.outputDir);
            }
            const duration = Date.now() - startTime;
            this.logger.info('Application generation completed successfully', {
                duration: `${duration}ms`,
                outputPath: options.outputDir || workflowResult.finalOutput?.projectPath,
            });
            // Print success message
            this.printSuccessMessage(workflowResult, options.outputDir, duration);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Application generation failed', { error: errorMessage });
            console.error('\n❌ Application generation failed:');
            console.error(`   ${errorMessage}\n`);
            throw error; // Re-throw for proper error handling
        }
    }
    async runAdditionalQualityGates(workflowResult) {
        this.logger.info('Running additional quality gates...');
        const projectPath = workflowResult.finalOutput?.projectPath;
        if (!projectPath) {
            throw new Error('No project path available for quality gates');
        }
        // Enhanced Quality Assessment
        const enhancedQualityGates = new EnhancedQualityGates_1.EnhancedQualityGates();
        const generatedCode = workflowResult.finalOutput;
        if (generatedCode && generatedCode.files) {
            this.logger.info('Running enhanced quality assessment...');
            try {
                // Get requirements from the workflow
                const requirements = this.extractRequirementsFromWorkflow(workflowResult);
                // Run comprehensive quality assessment
                const qualityMetrics = enhancedQualityGates.assessOverallQuality(generatedCode, requirements);
                this.logger.info('Quality Assessment Results:', {
                    overallScore: qualityMetrics.overallScore,
                    apiCoverage: qualityMetrics.apiCoverage,
                    componentIntegration: qualityMetrics.componentIntegration,
                    databaseCompleteness: qualityMetrics.databaseCompleteness,
                    businessLogicCompleteness: qualityMetrics.businessLogicCompleteness
                });
                // Fail if overall score is too low
                if (qualityMetrics.overallScore < 70) {
                    this.logger.warn('Generated application quality score is below threshold', {
                        score: qualityMetrics.overallScore,
                        threshold: 70
                    });
                }
            }
            catch (error) {
                this.logger.warn('Enhanced quality assessment failed', { error: error instanceof Error ? error.message : 'Unknown error' });
            }
        }
        // Quality Gate 1: TypeScript Compilation
        const compileResult = await this.checkTypeScriptCompilation(projectPath);
        if (!compileResult.passed) {
            throw new Error(`TypeScript compilation failed: ${compileResult.error}`);
        }
        // Quality Gate 2: Test Execution
        const testResult = await this.runTests(projectPath);
        if (!testResult.passed) {
            this.logger.warn('Tests failed but continuing deployment', { error: testResult.error });
            // Don't fail the entire process for test failures, just warn
        }
        // Quality Gate 3: Docker Build
        const dockerResult = await this.checkDockerBuild(projectPath);
        if (!dockerResult.passed) {
            this.logger.warn('Docker build failed but continuing', { error: dockerResult.error });
            // Don't fail for Docker issues in CLI mode
        }
        this.logger.info('Additional quality gates completed');
    }
    extractRequirementsFromWorkflow(workflowResult) {
        // Try to extract requirements from the planning phase result
        const planPhase = workflowResult.phases[Orchestrator_1.WorkflowPhase.PLAN];
        if (planPhase?.result?.data?.features) {
            return planPhase.result.data.features;
        }
        // Fallback to empty array
        return [];
    }
    extractRequirementsFromDescription(description) {
        const requirements = [];
        const descLower = description.toLowerCase();
        // Education/E-learning domain
        if (descLower.includes('course') || descLower.includes('lesson') || descLower.includes('student') || descLower.includes('learning') || descLower.includes('education')) {
            if (descLower.includes('course'))
                requirements.push('course management and creation');
            if (descLower.includes('student') || descLower.includes('user'))
                requirements.push('student/user management');
            if (descLower.includes('lesson') || descLower.includes('content'))
                requirements.push('lesson content delivery');
            if (descLower.includes('progress') || descLower.includes('tracking'))
                requirements.push('progress tracking and analytics');
            if (descLower.includes('assignment') || descLower.includes('quiz'))
                requirements.push('assignment and assessment system');
            if (descLower.includes('instructor') || descLower.includes('teacher'))
                requirements.push('instructor management portal');
        }
        // Healthcare domain
        else if (descLower.includes('patient') || descLower.includes('doctor') || descLower.includes('medical') || descLower.includes('healthcare') || descLower.includes('hospital')) {
            if (descLower.includes('patient'))
                requirements.push('patient management system');
            if (descLower.includes('appointment'))
                requirements.push('appointment scheduling');
            if (descLower.includes('medical') || descLower.includes('record'))
                requirements.push('medical records management');
            if (descLower.includes('doctor') || descLower.includes('physician'))
                requirements.push('doctor portal and management');
            if (descLower.includes('prescription'))
                requirements.push('prescription management');
        }
        // E-commerce domain
        else if (descLower.includes('product') || descLower.includes('shop') || descLower.includes('store') || descLower.includes('ecommerce') || descLower.includes('marketplace')) {
            if (descLower.includes('product'))
                requirements.push('product catalog management');
            if (descLower.includes('cart') || descLower.includes('shopping'))
                requirements.push('shopping cart functionality');
            if (descLower.includes('payment'))
                requirements.push('payment processing');
            if (descLower.includes('order'))
                requirements.push('order management');
            if (descLower.includes('inventory'))
                requirements.push('inventory management');
        }
        // Generic business requirements
        else {
            if (descLower.includes('user') || descLower.includes('customer') || descLower.includes('client')) {
                requirements.push('user management');
            }
            if (descLower.includes('payment') || descLower.includes('cash') || descLower.includes('card')) {
                requirements.push('payment processing');
            }
            if (descLower.includes('report') || descLower.includes('analytics')) {
                requirements.push('reporting and analytics');
            }
            if (descLower.includes('dashboard')) {
                requirements.push('dashboard and visualization');
            }
        }
        // If no specific requirements found, add the full description
        if (requirements.length === 0) {
            requirements.push(description);
        }
        return requirements;
    }
    async checkTypeScriptCompilation(projectPath) {
        try {
            this.logger.debug('Checking TypeScript compilation', { projectPath });
            // Check frontend compilation
            const frontendPath = path.join(projectPath, 'frontend');
            if (fs.existsSync(frontendPath)) {
                const { stdout, stderr } = await execAsync('npx tsc --noEmit', {
                    cwd: frontendPath,
                    timeout: 30000
                });
                if (stderr && stderr.includes('error')) {
                    return { passed: false, error: 'Frontend TypeScript compilation errors', details: stderr };
                }
            }
            // Check backend compilation
            const backendPath = path.join(projectPath, 'backend');
            if (fs.existsSync(backendPath)) {
                const { stdout, stderr } = await execAsync('npx tsc --noEmit', {
                    cwd: backendPath,
                    timeout: 30000
                });
                if (stderr && stderr.includes('error')) {
                    return { passed: false, error: 'Backend TypeScript compilation errors', details: stderr };
                }
            }
            return { passed: true };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown compilation error';
            return { passed: false, error: errorMessage };
        }
    }
    async runTests(projectPath) {
        try {
            this.logger.debug('Running tests', { projectPath });
            // Run frontend tests
            const frontendPath = path.join(projectPath, 'frontend');
            if (fs.existsSync(frontendPath) && fs.existsSync(path.join(frontendPath, 'package.json'))) {
                try {
                    await execAsync('npm test -- --passWithNoTests --watchAll=false', {
                        cwd: frontendPath,
                        timeout: 60000
                    });
                }
                catch (error) {
                    return { passed: false, error: 'Frontend tests failed', details: error instanceof Error ? error.message : 'Unknown test error' };
                }
            }
            // Run backend tests
            const backendPath = path.join(projectPath, 'backend');
            if (fs.existsSync(backendPath) && fs.existsSync(path.join(backendPath, 'package.json'))) {
                try {
                    await execAsync('npm test -- --passWithNoTests', {
                        cwd: backendPath,
                        timeout: 60000
                    });
                }
                catch (error) {
                    return { passed: false, error: 'Backend tests failed', details: error instanceof Error ? error.message : 'Unknown test error' };
                }
            }
            return { passed: true };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown test error';
            return { passed: false, error: errorMessage };
        }
    }
    async checkDockerBuild(projectPath) {
        try {
            this.logger.debug('Checking Docker build', { projectPath });
            const dockerComposePath = path.join(projectPath, 'docker-compose.yml');
            if (!fs.existsSync(dockerComposePath)) {
                return { passed: false, error: 'docker-compose.yml not found' };
            }
            // Try to validate docker-compose file
            const { stdout, stderr } = await execAsync('docker-compose config', {
                cwd: projectPath,
                timeout: 30000
            });
            if (stderr && stderr.includes('error')) {
                return { passed: false, error: 'Docker compose configuration invalid', details: stderr };
            }
            return { passed: true };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown Docker error';
            return { passed: false, error: errorMessage };
        }
    }
    async copyToOutputDirectory(sourcePath, outputDir) {
        try {
            this.logger.info('Copying project to output directory', { sourcePath, outputDir });
            // Create output directory if it doesn't exist
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            // Copy all files from source to output
            await execAsync(`cp -r "${sourcePath}"/* "${outputDir}"/`, { timeout: 30000 });
            this.logger.info('Project copied successfully', { outputDir });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown copy error';
            throw new Error(`Failed to copy project to output directory: ${errorMessage}`);
        }
    }
    printSuccessMessage(workflowResult, outputDir, duration) {
        const projectPath = outputDir || workflowResult.finalOutput?.projectPath;
        console.log('\n🎉 Application generated successfully!\n');
        console.log(`📁 Project location: ${projectPath}`);
        console.log(`⏱️  Generation time: ${duration ? Math.round(duration / 1000) : '?'}s\n`);
        console.log('📋 What was generated:');
        console.log('   ✅ React frontend with TypeScript');
        console.log('   ✅ Express.js backend with TypeScript');
        console.log('   ✅ PostgreSQL database setup');
        console.log('   ✅ JWT authentication system');
        console.log('   ✅ Docker configuration');
        console.log('   ✅ Tests and documentation\n');
        console.log('🚀 Next steps:');
        console.log(`   1. cd ${projectPath}`);
        console.log('   2. docker-compose up -d');
        console.log('   3. Open http://localhost:3000 in your browser\n');
        console.log('📖 For more information, check the README.md file in your project.\n');
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.DroidBotXCLI = DroidBotXCLI;
// CLI argument parsing
function parseArguments() {
    const args = process.argv.slice(2);
    if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
        printUsage();
        process.exit(0);
    }
    let description = '';
    const requirements = [];
    let outputDir;
    let maxRetries = 3;
    let skipQualityGates = false;
    let verbose = false;
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        switch (arg) {
            case '--description':
            case '-d':
                description = args[++i] || '';
                break;
            case '--requirement':
            case '-r':
                requirements.push(args[++i] || '');
                break;
            case '--output':
            case '-o':
                outputDir = args[++i];
                break;
            case '--max-retries':
                maxRetries = parseInt(args[++i]) || 3;
                break;
            case '--skip-quality-gates':
                skipQualityGates = true;
                break;
            case '--verbose':
            case '-v':
                verbose = true;
                break;
            default:
                if (!description && !arg.startsWith('-')) {
                    description = arg;
                }
                break;
        }
    }
    if (!description) {
        console.error('❌ Error: Description is required\n');
        printUsage();
        process.exit(1);
    }
    return {
        description,
        requirements,
        outputDir,
        maxRetries,
        skipQualityGates,
        verbose,
    };
}
function printUsage() {
    console.log(`
🤖 DroidBotX MVP - AI-Powered Full-Stack Application Generator

Usage:
  droidbotx [options] <description>

Arguments:
  description                 Description of the application to generate

Options:
  -d, --description <text>    Application description (alternative to positional arg)
  -r, --requirement <text>    Add a specific requirement (can be used multiple times)
  -o, --output <dir>          Output directory for generated project
  --max-retries <number>      Maximum retry attempts (default: 3)
  --skip-quality-gates        Skip additional quality validation
  -v, --verbose               Enable verbose logging
  -h, --help                  Show this help message

Examples:
  droidbotx "A todo list app with user authentication"
  
  droidbotx -d "E-commerce platform" \\
    -r "User registration and login" \\
    -r "Product catalog with search" \\
    -r "Shopping cart functionality" \\
    -o ./my-ecommerce-app
  
  droidbotx "Blog platform" --skip-quality-gates -v

Quality Gates:
  - TypeScript compilation check
  - Test execution
  - Docker configuration validation
  - Code structure validation

Generated Stack:
  - Frontend: React + TypeScript + Tailwind CSS
  - Backend: Express.js + TypeScript + PostgreSQL
  - Auth: JWT-based authentication
  - Deployment: Docker + docker-compose
  - Testing: Jest + Supertest
`);
}
// Main execution
async function main() {
    try {
        // Initialize configuration
        const config = ConfigManager_1.ConfigManager.getInstance();
        // Parse CLI arguments
        const options = parseArguments();
        // Set log level based on verbose flag
        if (options.verbose) {
            process.env.LOG_LEVEL = 'debug';
        }
        // Create and run CLI
        const cli = new DroidBotXCLI();
        await cli.run(options);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(`\n❌ Fatal error: ${errorMessage}\n`);
        // Only exit in non-test environments
        if (process.env.NODE_ENV !== 'test') {
            process.exit(1);
        }
        else {
            throw error; // Re-throw in test environment
        }
    }
}
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
// Run the CLI if this file is executed directly
if (require.main === module) {
    main();
}
//# sourceMappingURL=app.js.map
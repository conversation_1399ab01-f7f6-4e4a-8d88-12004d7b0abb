import { BusinessLogicResult } from '../agents/BusinessLogicAgent';
import { GeneratedCode } from '../agents/CodingAgent';
export interface IntegrationValidationResult {
    isValid: boolean;
    score: number;
    issues: string[];
    recommendations: string[];
    domainAPIsCoverage: number;
    databaseSchemaConsistency: number;
    serviceControllerAlignment: number;
}
export interface ValidationContext {
    businessLogic: BusinessLogicResult;
    generatedCode: GeneratedCode;
    projectPath: string;
    sessionId: string;
}
/**
 * Validates that business logic from BusinessLogicAgent is properly integrated
 * into the generated code from CodingAgent
 */
export declare class BusinessLogicIntegrationValidator {
    private logger;
    constructor();
    /**
     * Validate complete business logic integration
     */
    validateIntegration(context: ValidationContext): Promise<IntegrationValidationResult>;
    /**
     * Validate that all domain APIs are properly implemented
     */
    private validateDomainAPIsCoverage;
    /**
     * Enhanced database schema consistency validation with comprehensive init.sql analysis
     */
    private validateDatabaseSchemaConsistency;
    /**
     * Validate tables and their fields in the schema
     */
    private validateTablesInSchema;
    /**
     * Validate foreign key relationships
     */
    private validateForeignKeyRelationships;
    /**
     * Validate database constraints and indexes
     */
    private validateDatabaseConstraints;
    /**
     * Validate data type consistency
     */
    private validateDataTypeConsistency;
    /**
     * Map business logic field types to SQL types
     */
    private mapFieldTypeToSQLType;
    /**
     * Validate service-controller alignment across the codebase
     */
    private validateServiceControllerAlignment;
    /**
     * Generate integration report
     */
    generateIntegrationReport(result: IntegrationValidationResult): string;
}
export default BusinessLogicIntegrationValidator;
//# sourceMappingURL=BusinessLogicIntegrationValidator.d.ts.map
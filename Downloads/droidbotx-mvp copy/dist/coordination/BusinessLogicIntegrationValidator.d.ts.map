{"version": 3, "file": "BusinessLogicIntegrationValidator.d.ts", "sourceRoot": "", "sources": ["../../src/coordination/BusinessLogicIntegrationValidator.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAItD,MAAM,WAAW,2BAA2B;IAC1C,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,kBAAkB,EAAE,MAAM,CAAC;IAC3B,yBAAyB,EAAE,MAAM,CAAC;IAClC,0BAA0B,EAAE,MAAM,CAAC;CACpC;AAED,MAAM,WAAW,iBAAiB;IAChC,aAAa,EAAE,mBAAmB,CAAC;IACnC,aAAa,EAAE,aAAa,CAAC;IAC7B,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED;;;GAGG;AACH,qBAAa,iCAAiC;IAC5C,OAAO,CAAC,MAAM,CAAS;;IAMvB;;OAEG;IACU,mBAAmB,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,2BAA2B,CAAC;IA0ElG;;OAEG;YACW,0BAA0B;IAyExC;;OAEG;YACW,iCAAiC;IA2F/C;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAuC9B;;OAEG;IACH,OAAO,CAAC,+BAA+B;IA8CvC;;OAEG;IACH,OAAO,CAAC,2BAA2B;IA8CnC;;OAEG;IACH,OAAO,CAAC,2BAA2B;IA0BnC;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAa7B;;OAEG;YACW,kCAAkC;IAiGhD;;OAEG;IACI,yBAAyB,CAAC,MAAM,EAAE,2BAA2B,GAAG,MAAM;CAsB9E;AAED,eAAe,iCAAiC,CAAC"}
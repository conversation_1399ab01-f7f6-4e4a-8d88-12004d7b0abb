"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessLogicIntegrationValidator = void 0;
const Logger_1 = require("../core/Logger");
const path = __importStar(require("path"));
/**
 * Validates that business logic from BusinessLogicAgent is properly integrated
 * into the generated code from CodingAgent
 */
class BusinessLogicIntegrationValidator {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Validate complete business logic integration
     */
    async validateIntegration(context) {
        try {
            this.logger.info('Starting business logic integration validation', {
                sessionId: context.sessionId,
                domainAPIsCount: Object.keys(context.businessLogic.domainAPIs || {}).length,
                generatedFilesCount: Object.keys(context.generatedCode.files).length
            });
            // Validate domain APIs coverage
            const domainAPIsCoverage = await this.validateDomainAPIsCoverage(context);
            // Validate database schema consistency
            const databaseSchemaConsistency = await this.validateDatabaseSchemaConsistency(context);
            // Validate service-controller alignment
            const serviceControllerAlignment = await this.validateServiceControllerAlignment(context);
            // Calculate overall score
            const score = Math.round((domainAPIsCoverage.score + databaseSchemaConsistency.score + serviceControllerAlignment.score) / 3);
            // Collect all issues and recommendations
            const issues = [
                ...domainAPIsCoverage.issues,
                ...databaseSchemaConsistency.issues,
                ...serviceControllerAlignment.issues
            ];
            const recommendations = [
                ...domainAPIsCoverage.recommendations,
                ...databaseSchemaConsistency.recommendations,
                ...serviceControllerAlignment.recommendations
            ];
            const isValid = score >= 85 && issues.filter(issue => issue.includes('critical')).length === 0;
            this.logger.info('Business logic integration validation completed', {
                sessionId: context.sessionId,
                score,
                isValid,
                issuesCount: issues.length,
                domainAPIsCoverage: domainAPIsCoverage.score,
                databaseSchemaConsistency: databaseSchemaConsistency.score,
                serviceControllerAlignment: serviceControllerAlignment.score
            });
            return {
                isValid,
                score,
                issues,
                recommendations,
                domainAPIsCoverage: domainAPIsCoverage.score,
                databaseSchemaConsistency: databaseSchemaConsistency.score,
                serviceControllerAlignment: serviceControllerAlignment.score
            };
        }
        catch (error) {
            this.logger.error('Error in business logic integration validation', {
                error: error instanceof Error ? error.message : String(error),
                sessionId: context.sessionId
            });
            return {
                isValid: false,
                score: 0,
                issues: ['Critical error during validation'],
                recommendations: ['Retry validation with enhanced error handling'],
                domainAPIsCoverage: 0,
                databaseSchemaConsistency: 0,
                serviceControllerAlignment: 0
            };
        }
    }
    /**
     * Validate that all domain APIs are properly implemented
     */
    async validateDomainAPIsCoverage(context) {
        const issues = [];
        const recommendations = [];
        let implementedAPIs = 0;
        let totalAPIs = 0;
        try {
            const domainAPIs = context.businessLogic.domainAPIs || {};
            totalAPIs = Object.keys(domainAPIs).length;
            if (totalAPIs === 0) {
                issues.push('No domain APIs found in business logic');
                recommendations.push('Ensure BusinessLogicAgent generates domain APIs');
                return { score: 0, issues, recommendations };
            }
            // Check if each domain API is implemented in generated code
            for (const [apiPath, apiContent] of Object.entries(domainAPIs)) {
                if (context.generatedCode.files[apiPath]) {
                    implementedAPIs++;
                }
                else {
                    issues.push(`Missing implementation for domain API: ${apiPath}`);
                    recommendations.push(`Ensure CodingAgent includes ${apiPath} in generated files`);
                }
                // Validate API content quality
                if (typeof apiContent === 'string') {
                    if (apiContent.length < 100) {
                        issues.push(`Domain API ${apiPath} appears to be incomplete (too short)`);
                        recommendations.push(`Enhance ${apiPath} with complete implementation`);
                    }
                    if (!apiContent.includes('export')) {
                        issues.push(`Domain API ${apiPath} missing proper exports`);
                        recommendations.push(`Add proper export statements to ${apiPath}`);
                    }
                }
            }
            // Check for service files
            const serviceFiles = Object.keys(context.generatedCode.files).filter(path => path.includes('/services/') && path.endsWith('.ts'));
            if (serviceFiles.length === 0) {
                issues.push('Critical: No service files found in generated code');
                recommendations.push('Ensure CodingAgent generates service layer');
            }
            // Check for route files
            const routeFiles = Object.keys(context.generatedCode.files).filter(path => path.includes('/routes/') && path.endsWith('.ts'));
            if (routeFiles.length === 0) {
                issues.push('Critical: No route files found in generated code');
                recommendations.push('Ensure CodingAgent generates route layer');
            }
            const score = totalAPIs > 0 ? Math.round((implementedAPIs / totalAPIs) * 100) : 0;
            return { score, issues, recommendations };
        }
        catch (error) {
            issues.push('Error validating domain APIs coverage');
            recommendations.push('Fix domain APIs validation logic');
            return { score: 0, issues, recommendations };
        }
    }
    /**
     * Enhanced database schema consistency validation with comprehensive init.sql analysis
     */
    async validateDatabaseSchemaConsistency(context) {
        const issues = [];
        const recommendations = [];
        let consistentTables = 0;
        let totalTables = 0;
        let foreignKeyScore = 0;
        let constraintScore = 0;
        try {
            const businessLogicSchema = context.businessLogic.databaseSchema;
            if (!businessLogicSchema || !businessLogicSchema.tables) {
                issues.push('No database schema found in business logic');
                recommendations.push('Ensure BusinessLogicAgent generates database schema');
                return { score: 0, issues, recommendations };
            }
            totalTables = businessLogicSchema.tables.length;
            // Check multiple possible locations for init.sql
            const possibleInitSqlPaths = [
                'init.sql',
                'backend/init.sql',
                'backend/src/init.sql',
                'database/init.sql'
            ];
            let initSqlContent = '';
            let initSqlPath = '';
            for (const path of possibleInitSqlPaths) {
                if (context.generatedCode.files[path]) {
                    initSqlContent = context.generatedCode.files[path];
                    initSqlPath = path;
                    break;
                }
            }
            if (!initSqlContent) {
                issues.push('Critical: No init.sql file found in any expected location');
                recommendations.push('Generate init.sql file with proper database schema');
                return { score: 0, issues, recommendations };
            }
            // Enhanced table validation
            const tableValidationResults = this.validateTablesInSchema(businessLogicSchema.tables, initSqlContent, issues, recommendations);
            consistentTables = tableValidationResults.consistentTables;
            // Enhanced foreign key validation
            foreignKeyScore = this.validateForeignKeyRelationships(initSqlContent, businessLogicSchema.tables, issues, recommendations);
            // Validate database constraints and indexes
            constraintScore = this.validateDatabaseConstraints(initSqlContent, issues, recommendations);
            // Validate data types consistency
            this.validateDataTypeConsistency(businessLogicSchema.tables, initSqlContent, issues, recommendations);
            // Calculate comprehensive score
            const tableScore = totalTables > 0 ? (consistentTables / totalTables) * 100 : 0;
            const overallScore = Math.round((tableScore * 0.5) + (foreignKeyScore * 0.3) + (constraintScore * 0.2));
            return { score: overallScore, issues, recommendations };
        }
        catch (error) {
            issues.push(`Error validating database schema consistency: ${error instanceof Error ? error.message : String(error)}`);
            recommendations.push('Fix database schema validation logic');
            return { score: 0, issues, recommendations };
        }
    }
    /**
     * Validate tables and their fields in the schema
     */
    validateTablesInSchema(tables, initSqlContent, issues, recommendations) {
        let consistentTables = 0;
        for (const table of tables) {
            const tableName = table.name;
            const tableRegex = new RegExp(`CREATE\\s+TABLE\\s+(IF\\s+NOT\\s+EXISTS\\s+)?${tableName}\\s*\\(`, 'i');
            if (tableRegex.test(initSqlContent)) {
                consistentTables++;
                // Validate table fields
                const tableDefinitionMatch = initSqlContent.match(new RegExp(`CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?${tableName}\\s*\\(([^;]+)\\)`, 'is'));
                if (tableDefinitionMatch) {
                    const tableDefinition = tableDefinitionMatch[1];
                    for (const field of table.fields || []) {
                        if (!tableDefinition.includes(field.name)) {
                            issues.push(`Field ${field.name} missing from table ${tableName} in init.sql`);
                            recommendations.push(`Add field ${field.name} to table ${tableName} definition`);
                        }
                    }
                }
            }
            else {
                issues.push(`Table ${tableName} from business logic not found in init.sql`);
                recommendations.push(`Add CREATE TABLE statement for ${tableName}`);
            }
        }
        return { consistentTables };
    }
    /**
     * Validate foreign key relationships
     */
    validateForeignKeyRelationships(initSqlContent, tables, issues, recommendations) {
        let score = 0;
        // Check for explicit foreign key constraints
        const foreignKeyRegex = /FOREIGN\s+KEY\s*\([^)]+\)\s+REFERENCES\s+\w+\s*\([^)]+\)/gi;
        const foreignKeys = initSqlContent.match(foreignKeyRegex) || [];
        // Check for REFERENCES clauses
        const referencesRegex = /REFERENCES\s+\w+\s*\([^)]+\)/gi;
        const references = initSqlContent.match(referencesRegex) || [];
        const totalRelationships = foreignKeys.length + references.length;
        if (totalRelationships > 0) {
            score = Math.min(100, totalRelationships * 20); // 20 points per relationship, max 100
        }
        else {
            // Check if there should be relationships based on field patterns
            const relationshipFields = [
                'user_id', 'product_id', 'order_id', 'category_id',
                'customer_id', 'supplier_id', 'payment_id'
            ];
            let expectedRelationships = 0;
            for (const field of relationshipFields) {
                if (initSqlContent.includes(field)) {
                    expectedRelationships++;
                }
            }
            if (expectedRelationships > 0) {
                issues.push(`Found ${expectedRelationships} potential foreign key fields but no foreign key constraints`);
                recommendations.push('Add foreign key constraints for referential integrity');
                score = 0;
            }
            else {
                score = 50; // No relationships needed
            }
        }
        return score;
    }
    /**
     * Validate database constraints and indexes
     */
    validateDatabaseConstraints(initSqlContent, issues, recommendations) {
        let score = 0;
        let constraintCount = 0;
        // Check for primary key constraints
        if (initSqlContent.includes('PRIMARY KEY')) {
            constraintCount += 2;
        }
        else {
            issues.push('No PRIMARY KEY constraints found in database schema');
            recommendations.push('Add PRIMARY KEY constraints to tables');
        }
        // Check for unique constraints
        if (initSqlContent.includes('UNIQUE')) {
            constraintCount += 1;
        }
        // Check for not null constraints
        if (initSqlContent.includes('NOT NULL')) {
            constraintCount += 1;
        }
        else {
            issues.push('No NOT NULL constraints found in database schema');
            recommendations.push('Add NOT NULL constraints for required fields');
        }
        // Check for check constraints
        if (initSqlContent.includes('CHECK')) {
            constraintCount += 1;
        }
        // Check for indexes
        if (initSqlContent.includes('CREATE INDEX') || initSqlContent.includes('CREATE UNIQUE INDEX')) {
            constraintCount += 1;
        }
        else {
            recommendations.push('Consider adding indexes for better query performance');
        }
        score = Math.min(100, constraintCount * 20);
        return score;
    }
    /**
     * Validate data type consistency
     */
    validateDataTypeConsistency(tables, initSqlContent, issues, recommendations) {
        const commonDataTypes = ['VARCHAR', 'INTEGER', 'TIMESTAMP', 'BOOLEAN', 'TEXT', 'SERIAL', 'UUID'];
        for (const table of tables) {
            for (const field of table.fields || []) {
                if (field.type) {
                    const expectedType = this.mapFieldTypeToSQLType(field.type);
                    // Check if the field exists with the correct type in init.sql
                    const fieldRegex = new RegExp(`${field.name}\\s+(${expectedType}|\\w+)`, 'i');
                    const match = initSqlContent.match(fieldRegex);
                    if (match && !match[1].toUpperCase().includes(expectedType.toUpperCase())) {
                        issues.push(`Field ${field.name} in table ${table.name} has inconsistent data type`);
                        recommendations.push(`Ensure ${field.name} uses ${expectedType} data type`);
                    }
                }
            }
        }
    }
    /**
     * Map business logic field types to SQL types
     */
    mapFieldTypeToSQLType(fieldType) {
        const typeMap = {
            'string': 'VARCHAR',
            'number': 'INTEGER',
            'boolean': 'BOOLEAN',
            'Date': 'TIMESTAMP',
            'text': 'TEXT',
            'id': 'UUID'
        };
        return typeMap[fieldType] || 'VARCHAR';
    }
    /**
     * Validate service-controller alignment across the codebase
     */
    async validateServiceControllerAlignment(context) {
        const issues = [];
        const recommendations = [];
        let alignedPairs = 0;
        let totalServices = 0;
        try {
            // Find all service files
            const serviceFiles = Object.keys(context.generatedCode.files).filter(path => path.includes('/services/') && path.endsWith('.ts'));
            // Find all controller files
            const controllerFiles = Object.keys(context.generatedCode.files).filter(path => path.includes('/controllers/') && path.endsWith('.ts'));
            // Find all route files
            const routeFiles = Object.keys(context.generatedCode.files).filter(path => path.includes('/routes/') && path.endsWith('.ts'));
            totalServices = serviceFiles.length;
            if (totalServices === 0) {
                issues.push('Critical: No service files found');
                recommendations.push('Generate service layer files');
                return { score: 0, issues, recommendations };
            }
            // Check service-controller alignment
            for (const servicePath of serviceFiles) {
                const serviceName = path.basename(servicePath, '.ts');
                const expectedControllerName = serviceName.replace('Service', '');
                // Look for corresponding controller
                const hasController = controllerFiles.some(controllerPath => controllerPath.includes(expectedControllerName.toLowerCase()));
                // Look for corresponding route
                const hasRoute = routeFiles.some(routePath => routePath.includes(expectedControllerName.toLowerCase()) ||
                    routePath.includes(serviceName.toLowerCase()));
                if (hasController || hasRoute) {
                    alignedPairs++;
                }
                else {
                    issues.push(`Service ${serviceName} has no corresponding controller or route`);
                    recommendations.push(`Create controller and route for ${serviceName}`);
                }
                // Validate service content
                const serviceContent = context.generatedCode.files[servicePath];
                if (typeof serviceContent === 'string') {
                    if (!serviceContent.includes('export class')) {
                        issues.push(`Service ${serviceName} missing proper class export`);
                        recommendations.push(`Add proper class export to ${serviceName}`);
                    }
                    if (!serviceContent.includes('async') && !serviceContent.includes('Promise')) {
                        issues.push(`Service ${serviceName} appears to lack async methods`);
                        recommendations.push(`Add async methods to ${serviceName}`);
                    }
                }
            }
            // Check for orphaned controllers
            for (const controllerPath of controllerFiles) {
                const controllerName = path.basename(controllerPath, '.ts');
                const expectedServiceName = controllerName.replace('controller', '') + 'Service';
                const hasService = serviceFiles.some(servicePath => servicePath.includes(expectedServiceName));
                if (!hasService) {
                    issues.push(`Controller ${controllerName} has no corresponding service`);
                    recommendations.push(`Create service for ${controllerName}`);
                }
            }
            const score = totalServices > 0 ? Math.round((alignedPairs / totalServices) * 100) : 0;
            return { score, issues, recommendations };
        }
        catch (error) {
            issues.push('Error validating service-controller alignment');
            recommendations.push('Fix service-controller alignment validation logic');
            return { score: 0, issues, recommendations };
        }
    }
    /**
     * Generate integration report
     */
    generateIntegrationReport(result) {
        const report = `
# Business Logic Integration Validation Report

## Overall Score: ${result.score}/100 ${result.isValid ? '✅' : '❌'}

### Component Scores:
- Domain APIs Coverage: ${result.domainAPIsCoverage}/100
- Database Schema Consistency: ${result.databaseSchemaConsistency}/100
- Service-Controller Alignment: ${result.serviceControllerAlignment}/100

### Issues Found (${result.issues.length}):
${result.issues.map(issue => `- ${issue}`).join('\n')}

### Recommendations (${result.recommendations.length}):
${result.recommendations.map(rec => `- ${rec}`).join('\n')}

### Status: ${result.isValid ? 'PASSED' : 'FAILED'}
`;
        return report;
    }
}
exports.BusinessLogicIntegrationValidator = BusinessLogicIntegrationValidator;
exports.default = BusinessLogicIntegrationValidator;
//# sourceMappingURL=BusinessLogicIntegrationValidator.js.map
import { FeedbackManager } from './FeedbackManager';
import { WorkflowPhase } from '../orchestration/Orchestrator';
export interface FixingContext {
    projectPath: string;
    sessionId: string;
    phase: WorkflowPhase;
    previousFixes: string[];
    targetIssues: string[];
}
export interface FixingResult {
    success: boolean;
    fixesApplied: string[];
    remainingIssues: string[];
    validationPassed: boolean;
    nextActions: string[];
}
/**
 * Coordinates fixing strategies between DebuggerAgent and TestingAgent
 * to ensure consistent and effective issue resolution
 */
export declare class CoordinatedFixingStrategy {
    private logger;
    private feedbackManager;
    private sharedPatterns;
    constructor(feedbackManager: FeedbackManager);
    /**
     * Apply coordinated fixes based on feedback and context
     */
    applyCoordinatedFixes(context: FixingContext): Promise<FixingResult>;
    /**
     * Synchronize fixes between DebuggerAgent and TestingAgent
     */
    synchronizeFixes(debuggerFixes: string[], testingIssues: string[], context: FixingContext): Promise<string[]>;
    /**
     * Initialize shared patterns between agents
     */
    private initializeSharedPatterns;
    /**
     * Identify pattern from issue description
     */
    private identifyPattern;
    /**
     * Apply fixes for a specific pattern
     */
    private applyPatternFixes;
    /**
     * Enhanced duplicate import detection and resolution
     */
    private fixDuplicateImports;
    /**
     * Normalize import identifier for duplicate detection
     */
    private normalizeImportIdentifier;
    /**
     * Normalize import path for duplicate detection
     */
    private normalizeImportPath;
    /**
     * Fix duplicates in all TypeScript files in a directory
     */
    private fixDuplicatesInDirectory;
    /**
     * Find all TypeScript files in a directory recursively
     */
    private findTypeScriptFiles;
    /**
     * Fix missing types
     */
    private fixMissingTypes;
    /**
     * Fix route registration issues
     */
    private fixRouteRegistration;
    /**
     * Fix service instantiation issues
     */
    private fixServiceInstantiation;
    /**
     * Fix test quality issues
     */
    private fixTestQuality;
    /**
     * Validate applied fixes
     */
    private validateFixes;
    /**
     * Generate next actions based on fixing results
     */
    private generateNextActions;
    /**
     * Check if a fix is relevant to an issue
     */
    private isFixRelevantToIssue;
}
export default CoordinatedFixingStrategy;
//# sourceMappingURL=CoordinatedFixingStrategy.d.ts.map
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoordinatedFixingStrategy = void 0;
const Logger_1 = require("../core/Logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Coordinates fixing strategies between DebuggerAgent and TestingAgent
 * to ensure consistent and effective issue resolution
 */
class CoordinatedFixingStrategy {
    constructor(feedbackManager) {
        this.sharedPatterns = new Map();
        this.logger = Logger_1.Logger.getInstance();
        this.feedbackManager = feedbackManager;
        this.initializeSharedPatterns();
    }
    /**
     * Apply coordinated fixes based on feedback and context
     */
    async applyCoordinatedFixes(context) {
        try {
            this.logger.info('Starting coordinated fixing strategy', {
                sessionId: context.sessionId,
                phase: context.phase,
                targetIssues: context.targetIssues.length
            });
            const fixesApplied = [];
            const remainingIssues = [];
            // Apply fixes based on issue patterns
            for (const issue of context.targetIssues) {
                const pattern = this.identifyPattern(issue);
                const fixes = await this.applyPatternFixes(pattern, issue, context);
                fixesApplied.push(...fixes);
            }
            // Validate fixes
            const validationResult = await this.validateFixes(context, fixesApplied);
            // Generate next actions
            const nextActions = this.generateNextActions(context, fixesApplied, validationResult);
            return {
                success: validationResult.passed,
                fixesApplied,
                remainingIssues: validationResult.remainingIssues,
                validationPassed: validationResult.passed,
                nextActions
            };
        }
        catch (error) {
            this.logger.error('Error in coordinated fixing strategy', {
                error: error instanceof Error ? error.message : String(error),
                sessionId: context.sessionId
            });
            return {
                success: false,
                fixesApplied: [],
                remainingIssues: context.targetIssues,
                validationPassed: false,
                nextActions: ['Retry with enhanced error handling']
            };
        }
    }
    /**
     * Synchronize fixes between DebuggerAgent and TestingAgent
     */
    async synchronizeFixes(debuggerFixes, testingIssues, context) {
        const synchronizedFixes = [];
        // Analyze overlap between debugger fixes and testing issues
        for (const issue of testingIssues) {
            const pattern = this.identifyPattern(issue);
            // Check if debugger already addressed this pattern
            const relevantDebuggerFixes = debuggerFixes.filter(fix => this.isFixRelevantToIssue(fix, issue));
            if (relevantDebuggerFixes.length === 0) {
                // Apply additional fixes that debugger missed
                const additionalFixes = await this.applyPatternFixes(pattern, issue, context);
                synchronizedFixes.push(...additionalFixes);
            }
        }
        this.logger.info('Synchronized fixes between agents', {
            debuggerFixes: debuggerFixes.length,
            testingIssues: testingIssues.length,
            synchronizedFixes: synchronizedFixes.length,
            sessionId: context.sessionId
        });
        return synchronizedFixes;
    }
    /**
     * Initialize shared patterns between agents
     */
    initializeSharedPatterns() {
        // Duplicate import patterns
        this.sharedPatterns.set('duplicate_imports', [
            'Remove duplicate import statements',
            'Consolidate import declarations',
            'Fix import path conflicts'
        ]);
        // Missing type patterns
        this.sharedPatterns.set('missing_types', [
            'Generate missing TypeScript interfaces',
            'Add CreateData and UpdateData types',
            'Export required type definitions'
        ]);
        // Route registration patterns
        this.sharedPatterns.set('route_registration', [
            'Fix duplicate route registrations',
            'Consolidate route imports',
            'Remove conflicting route declarations'
        ]);
        // Service instantiation patterns
        this.sharedPatterns.set('service_instantiation', [
            'Convert static methods to instance methods',
            'Add proper service instantiation',
            'Fix service export patterns'
        ]);
        // Test quality patterns
        this.sharedPatterns.set('test_quality', [
            'Improve test error handling',
            'Add proper test assertions',
            'Generate realistic test data'
        ]);
    }
    /**
     * Identify pattern from issue description
     */
    identifyPattern(issue) {
        const lowerIssue = issue.toLowerCase();
        if (lowerIssue.includes('duplicate') && lowerIssue.includes('import')) {
            return 'duplicate_imports';
        }
        if (lowerIssue.includes('missing') && (lowerIssue.includes('type') || lowerIssue.includes('interface'))) {
            return 'missing_types';
        }
        if (lowerIssue.includes('route') && lowerIssue.includes('registration')) {
            return 'route_registration';
        }
        if (lowerIssue.includes('service') && lowerIssue.includes('instantiation')) {
            return 'service_instantiation';
        }
        if (lowerIssue.includes('test') && (lowerIssue.includes('empty') || lowerIssue.includes('error'))) {
            return 'test_quality';
        }
        return 'general_issue';
    }
    /**
     * Apply fixes for a specific pattern
     */
    async applyPatternFixes(pattern, issue, context) {
        const fixes = [];
        switch (pattern) {
            case 'duplicate_imports':
                fixes.push(...await this.fixDuplicateImports(context));
                break;
            case 'missing_types':
                fixes.push(...await this.fixMissingTypes(context));
                break;
            case 'route_registration':
                fixes.push(...await this.fixRouteRegistration(context));
                break;
            case 'service_instantiation':
                fixes.push(...await this.fixServiceInstantiation(context));
                break;
            case 'test_quality':
                fixes.push(...await this.fixTestQuality(context));
                break;
            default:
                fixes.push(`Applied general fix for: ${issue}`);
        }
        return fixes;
    }
    /**
     * Enhanced duplicate import detection and resolution
     */
    async fixDuplicateImports(context) {
        const fixes = [];
        try {
            const serverPath = path.join(context.projectPath, 'backend/src/server.ts');
            if (fs.existsSync(serverPath)) {
                let content = fs.readFileSync(serverPath, 'utf8');
                // Advanced duplicate detection with path normalization
                const lines = content.split('\n');
                const importMap = new Map();
                const filteredLines = [];
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    if (line.trim().startsWith('import') && line.includes('from')) {
                        const importMatch = line.match(/import\s+(.+?)\s+from\s+['"](.+?)['"]/);
                        if (importMatch) {
                            const [, importedItems, importPath] = importMatch;
                            // Normalize import identifier and path
                            const normalizedIdentifier = this.normalizeImportIdentifier(importedItems);
                            const normalizedPath = this.normalizeImportPath(importPath);
                            // Create a unique key for duplicate detection
                            const duplicateKey = `${normalizedIdentifier}:${normalizedPath}`;
                            if (importMap.has(duplicateKey)) {
                                // Found duplicate - skip this line
                                fixes.push(`Removed duplicate import: ${line.trim()}`);
                                continue;
                            }
                            else {
                                // Store this import for future duplicate detection
                                importMap.set(duplicateKey, { line, index: i, path: importPath });
                            }
                        }
                    }
                    filteredLines.push(line);
                }
                // Also check for route registrations and remove duplicates
                const registrationMap = new Map();
                const finalLines = [];
                for (const line of filteredLines) {
                    if (line.trim().startsWith('app.use(') && line.includes('Routes')) {
                        const registrationMatch = line.match(/app\.use\(['"](.+?)['"],\s*(.+?)\)/);
                        if (registrationMatch) {
                            const [, routePath, routeVariable] = registrationMatch;
                            const registrationKey = `${routePath}:${routeVariable}`;
                            if (registrationMap.has(registrationKey)) {
                                fixes.push(`Removed duplicate route registration: ${line.trim()}`);
                                continue;
                            }
                            else {
                                registrationMap.set(registrationKey, true);
                            }
                        }
                    }
                    finalLines.push(line);
                }
                if (fixes.length > 0) {
                    fs.writeFileSync(serverPath, finalLines.join('\n'));
                    this.logger.info(`Fixed ${fixes.length} duplicate imports/registrations in server.ts`);
                }
            }
            // Apply the same logic to other files that might have duplicates
            await this.fixDuplicatesInDirectory(context.projectPath, fixes);
        }
        catch (error) {
            this.logger.error('Error fixing duplicate imports', {
                error: error instanceof Error ? error.message : String(error)
            });
        }
        return fixes;
    }
    /**
     * Normalize import identifier for duplicate detection
     */
    normalizeImportIdentifier(importedItems) {
        // Remove whitespace and handle default vs named imports
        const cleaned = importedItems.trim().replace(/\s+/g, ' ');
        // Handle default imports
        if (!cleaned.includes('{') && !cleaned.includes(',')) {
            return cleaned;
        }
        // Handle named imports - sort them for consistent comparison
        if (cleaned.includes('{')) {
            const match = cleaned.match(/\{(.+?)\}/);
            if (match) {
                const namedImports = match[1].split(',').map(item => item.trim()).sort();
                return `{${namedImports.join(', ')}}`;
            }
        }
        return cleaned;
    }
    /**
     * Normalize import path for duplicate detection
     */
    normalizeImportPath(importPath) {
        // Remove file extensions and normalize path separators
        let normalized = importPath.replace(/\.(ts|js)$/, '');
        // Handle different route path patterns
        if (normalized.includes('/routes/')) {
            // Normalize route paths: './routes/auth' and './routes/auth.routes' should be considered the same
            normalized = normalized.replace(/\.routes$/, '');
        }
        // Normalize relative path indicators
        normalized = normalized.replace(/^\.\//, '');
        return normalized;
    }
    /**
     * Fix duplicates in all TypeScript files in a directory
     */
    async fixDuplicatesInDirectory(projectPath, fixes) {
        const backendSrcPath = path.join(projectPath, 'backend/src');
        if (!fs.existsSync(backendSrcPath))
            return;
        const tsFiles = this.findTypeScriptFiles(backendSrcPath);
        for (const filePath of tsFiles) {
            if (filePath.endsWith('server.ts'))
                continue; // Already handled
            try {
                let content = fs.readFileSync(filePath, 'utf8');
                const lines = content.split('\n');
                const seenImports = new Set();
                const filteredLines = [];
                let fileFixed = false;
                for (const line of lines) {
                    if (line.trim().startsWith('import') && line.includes('from')) {
                        const importMatch = line.match(/import\s+(.+?)\s+from\s+['"](.+?)['"]/);
                        if (importMatch) {
                            const [, importedItems, importPath] = importMatch;
                            const normalizedKey = `${this.normalizeImportIdentifier(importedItems)}:${this.normalizeImportPath(importPath)}`;
                            if (seenImports.has(normalizedKey)) {
                                fixes.push(`Removed duplicate import in ${path.basename(filePath)}: ${line.trim()}`);
                                fileFixed = true;
                                continue;
                            }
                            else {
                                seenImports.add(normalizedKey);
                            }
                        }
                    }
                    filteredLines.push(line);
                }
                if (fileFixed) {
                    fs.writeFileSync(filePath, filteredLines.join('\n'));
                }
            }
            catch (error) {
                this.logger.error(`Error fixing duplicates in ${filePath}`, {
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
    }
    /**
     * Find all TypeScript files in a directory recursively
     */
    findTypeScriptFiles(dirPath) {
        const tsFiles = [];
        try {
            const items = fs.readdirSync(dirPath);
            for (const item of items) {
                const itemPath = path.join(dirPath, item);
                const stat = fs.statSync(itemPath);
                if (stat.isDirectory()) {
                    tsFiles.push(...this.findTypeScriptFiles(itemPath));
                }
                else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                    tsFiles.push(itemPath);
                }
            }
        }
        catch (error) {
            // Directory doesn't exist or can't be read
        }
        return tsFiles;
    }
    /**
     * Fix missing types
     */
    async fixMissingTypes(context) {
        const fixes = [];
        try {
            // Generate missing type definitions for models
            const modelsDir = path.join(context.projectPath, 'backend/src/models');
            if (fs.existsSync(modelsDir)) {
                const modelFiles = fs.readdirSync(modelsDir).filter(f => f.endsWith('.ts'));
                for (const modelFile of modelFiles) {
                    const modelPath = path.join(modelsDir, modelFile);
                    let content = fs.readFileSync(modelPath, 'utf8');
                    const modelName = modelFile.replace('.ts', '');
                    const createDataType = `${modelName}CreateData`;
                    const updateDataType = `${modelName}UpdateData`;
                    // Check if types are missing
                    if (!content.includes(createDataType)) {
                        content += `\n\nexport interface ${createDataType} {\n  [key: string]: any;\n}`;
                        fixes.push(`Added ${createDataType} interface`);
                    }
                    if (!content.includes(updateDataType)) {
                        content += `\n\nexport interface ${updateDataType} {\n  [key: string]: any;\n}`;
                        fixes.push(`Added ${updateDataType} interface`);
                    }
                    if (fixes.length > 0) {
                        fs.writeFileSync(modelPath, content);
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('Error fixing missing types', {
                error: error instanceof Error ? error.message : String(error)
            });
        }
        return fixes;
    }
    /**
     * Fix route registration issues
     */
    async fixRouteRegistration(context) {
        const fixes = [];
        try {
            const serverPath = path.join(context.projectPath, 'backend/src/server.ts');
            if (fs.existsSync(serverPath)) {
                let content = fs.readFileSync(serverPath, 'utf8');
                // Remove duplicate route registrations
                const lines = content.split('\n');
                const seenRegistrations = new Set();
                const filteredLines = [];
                for (const line of lines) {
                    if (line.trim().startsWith('app.use(') && line.includes('Routes')) {
                        const registrationKey = line.trim();
                        if (!seenRegistrations.has(registrationKey)) {
                            seenRegistrations.add(registrationKey);
                            filteredLines.push(line);
                        }
                        else {
                            fixes.push(`Removed duplicate route registration: ${line.trim()}`);
                        }
                    }
                    else {
                        filteredLines.push(line);
                    }
                }
                if (fixes.length > 0) {
                    fs.writeFileSync(serverPath, filteredLines.join('\n'));
                }
            }
        }
        catch (error) {
            this.logger.error('Error fixing route registration', {
                error: error instanceof Error ? error.message : String(error)
            });
        }
        return fixes;
    }
    /**
     * Fix service instantiation issues
     */
    async fixServiceInstantiation(context) {
        const fixes = [];
        // This would be implemented to fix service instantiation patterns
        fixes.push('Applied service instantiation fixes');
        return fixes;
    }
    /**
     * Fix test quality issues
     */
    async fixTestQuality(context) {
        const fixes = [];
        // This would be implemented to improve test generation quality
        fixes.push('Applied test quality improvements');
        return fixes;
    }
    /**
     * Validate applied fixes
     */
    async validateFixes(context, fixesApplied) {
        // Basic validation - in a real implementation, this would run compilation checks
        return {
            passed: fixesApplied.length > 0,
            remainingIssues: []
        };
    }
    /**
     * Generate next actions based on fixing results
     */
    generateNextActions(context, fixesApplied, validationResult) {
        const actions = [];
        if (!validationResult.passed) {
            actions.push('Retry fixing with enhanced patterns');
        }
        if (validationResult.remainingIssues.length > 0) {
            actions.push('Apply targeted fixes for remaining issues');
        }
        if (fixesApplied.length > 0) {
            actions.push('Validate fixes with comprehensive testing');
        }
        return actions;
    }
    /**
     * Check if a fix is relevant to an issue
     */
    isFixRelevantToIssue(fix, issue) {
        const fixLower = fix.toLowerCase();
        const issueLower = issue.toLowerCase();
        // Simple relevance check - could be enhanced with ML
        return fixLower.includes(issueLower.split(' ')[0]) ||
            issueLower.includes(fixLower.split(' ')[0]);
    }
}
exports.CoordinatedFixingStrategy = CoordinatedFixingStrategy;
exports.default = CoordinatedFixingStrategy;
//# sourceMappingURL=CoordinatedFixingStrategy.js.map
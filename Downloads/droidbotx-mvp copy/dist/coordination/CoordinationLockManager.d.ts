export interface CoordinationLock {
    sessionId: string;
    phase: string;
    operation: string;
    timestamp: Date;
    filePath: string;
    lockData: any;
}
export interface ImportState {
    imports: string[];
    registrations: string[];
    lastModified: Date;
    checksum: string;
}
/**
 * Coordination Lock Manager to prevent timing issues between agents
 * Specifically designed to resolve duplicate import coordination problems
 */
export declare class CoordinationLockManager {
    private logger;
    private locks;
    private importStates;
    constructor();
    /**
     * Acquire a coordination lock for a specific operation
     */
    acquireLock(sessionId: string, phase: string, operation: string, filePath: string, lockData?: any): Promise<boolean>;
    /**
     * Release a coordination lock
     */
    releaseLock(sessionId: string, phase: string, operation: string, filePath: string): Promise<void>;
    /**
     * Check if a lock exists for a specific operation
     */
    hasLock(sessionId: string, phase: string, operation: string, filePath: string): boolean;
    /**
     * Capture the current import state of a file
     */
    captureImportState(filePath: string): Promise<ImportState | null>;
    /**
     * Validate that imports haven't been duplicated since last capture
     */
    validateImportState(filePath: string): Promise<{
        isValid: boolean;
        duplicates: string[];
        issues: string[];
    }>;
    /**
     * Extract import statements from file content
     */
    private extractImports;
    /**
     * Extract app.use registrations from file content
     */
    private extractRegistrations;
    /**
     * Normalize import statement for comparison
     */
    private normalizeImport;
    /**
     * Normalize registration statement for comparison
     */
    private normalizeRegistration;
    /**
     * Calculate simple checksum for content
     */
    private calculateChecksum;
    /**
     * Clean up old locks and states
     */
    cleanup(maxAge?: number): void;
}
export default CoordinationLockManager;
//# sourceMappingURL=CoordinationLockManager.d.ts.map
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoordinationLockManager = void 0;
const Logger_1 = require("../core/Logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Coordination Lock Manager to prevent timing issues between agents
 * Specifically designed to resolve duplicate import coordination problems
 */
class CoordinationLockManager {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.locks = new Map();
        this.importStates = new Map();
    }
    /**
     * Acquire a coordination lock for a specific operation
     */
    async acquireLock(sessionId, phase, operation, filePath, lockData) {
        const lockKey = `${sessionId}-${phase}-${operation}-${filePath}`;
        if (this.locks.has(lockKey)) {
            this.logger.warn('Lock already exists, operation blocked', {
                sessionId,
                phase,
                operation,
                filePath
            });
            return false;
        }
        const lock = {
            sessionId,
            phase,
            operation,
            timestamp: new Date(),
            filePath,
            lockData
        };
        this.locks.set(lockKey, lock);
        this.logger.info('Coordination lock acquired', {
            lockKey,
            sessionId,
            phase,
            operation
        });
        return true;
    }
    /**
     * Release a coordination lock
     */
    async releaseLock(sessionId, phase, operation, filePath) {
        const lockKey = `${sessionId}-${phase}-${operation}-${filePath}`;
        if (this.locks.has(lockKey)) {
            this.locks.delete(lockKey);
            this.logger.info('Coordination lock released', {
                lockKey,
                sessionId,
                phase,
                operation
            });
        }
    }
    /**
     * Check if a lock exists for a specific operation
     */
    hasLock(sessionId, phase, operation, filePath) {
        const lockKey = `${sessionId}-${phase}-${operation}-${filePath}`;
        return this.locks.has(lockKey);
    }
    /**
     * Capture the current import state of a file
     */
    async captureImportState(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                return null;
            }
            const content = fs.readFileSync(filePath, 'utf8');
            const imports = this.extractImports(content);
            const registrations = this.extractRegistrations(content);
            const checksum = this.calculateChecksum(content);
            const state = {
                imports,
                registrations,
                lastModified: new Date(),
                checksum
            };
            this.importStates.set(filePath, state);
            this.logger.info('Import state captured', {
                filePath: path.basename(filePath),
                imports: imports.length,
                registrations: registrations.length,
                checksum
            });
            return state;
        }
        catch (error) {
            this.logger.error('Failed to capture import state', {
                filePath,
                error: error instanceof Error ? error.message : String(error)
            });
            return null;
        }
    }
    /**
     * Validate that imports haven't been duplicated since last capture
     */
    async validateImportState(filePath) {
        const result = {
            isValid: true,
            duplicates: [],
            issues: []
        };
        try {
            if (!fs.existsSync(filePath)) {
                result.issues.push('File does not exist');
                result.isValid = false;
                return result;
            }
            const content = fs.readFileSync(filePath, 'utf8');
            const currentImports = this.extractImports(content);
            const currentRegistrations = this.extractRegistrations(content);
            // Check for duplicate imports
            const importCounts = new Map();
            for (const imp of currentImports) {
                const normalized = this.normalizeImport(imp);
                importCounts.set(normalized, (importCounts.get(normalized) || 0) + 1);
            }
            for (const [imp, count] of importCounts) {
                if (count > 1) {
                    result.duplicates.push(imp);
                    result.isValid = false;
                }
            }
            // Check for duplicate registrations
            const registrationCounts = new Map();
            for (const reg of currentRegistrations) {
                const normalized = this.normalizeRegistration(reg);
                registrationCounts.set(normalized, (registrationCounts.get(normalized) || 0) + 1);
            }
            for (const [reg, count] of registrationCounts) {
                if (count > 1) {
                    result.duplicates.push(reg);
                    result.isValid = false;
                }
            }
            if (!result.isValid) {
                this.logger.warn('Import state validation failed', {
                    filePath: path.basename(filePath),
                    duplicates: result.duplicates.length,
                    issues: result.issues.length
                });
            }
        }
        catch (error) {
            result.issues.push(`Validation error: ${error instanceof Error ? error.message : String(error)}`);
            result.isValid = false;
        }
        return result;
    }
    /**
     * Extract import statements from file content
     */
    extractImports(content) {
        const imports = [];
        const importRegex = /import\s+[^;]+from\s+['"][^'"]+['"];?/g;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            imports.push(match[0].trim());
        }
        return imports;
    }
    /**
     * Extract app.use registrations from file content
     */
    extractRegistrations(content) {
        const registrations = [];
        const registrationRegex = /app\.use\s*\([^)]+\);?/g;
        let match;
        while ((match = registrationRegex.exec(content)) !== null) {
            registrations.push(match[0].trim());
        }
        return registrations;
    }
    /**
     * Normalize import statement for comparison
     */
    normalizeImport(importStatement) {
        // Remove whitespace and normalize quotes
        return importStatement
            .replace(/\s+/g, ' ')
            .replace(/'/g, '"')
            .trim();
    }
    /**
     * Normalize registration statement for comparison
     */
    normalizeRegistration(registration) {
        // Remove whitespace and normalize quotes
        return registration
            .replace(/\s+/g, ' ')
            .replace(/'/g, '"')
            .trim();
    }
    /**
     * Calculate simple checksum for content
     */
    calculateChecksum(content) {
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString(16);
    }
    /**
     * Clean up old locks and states
     */
    cleanup(maxAge = 3600000) {
        const now = new Date();
        // Clean up old locks
        for (const [key, lock] of this.locks) {
            if (now.getTime() - lock.timestamp.getTime() > maxAge) {
                this.locks.delete(key);
                this.logger.info('Cleaned up expired lock', { key });
            }
        }
        // Clean up old import states
        for (const [filePath, state] of this.importStates) {
            if (now.getTime() - state.lastModified.getTime() > maxAge) {
                this.importStates.delete(filePath);
                this.logger.info('Cleaned up expired import state', { filePath });
            }
        }
    }
}
exports.CoordinationLockManager = CoordinationLockManager;
exports.default = CoordinationLockManager;
//# sourceMappingURL=CoordinationLockManager.js.map
{"version": 3, "file": "CoordinationLockManager.js", "sourceRoot": "", "sources": ["../../src/coordination/CoordinationLockManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAwC;AACxC,uCAAyB;AACzB,2CAA6B;AAkB7B;;;GAGG;AACH,MAAa,uBAAuB;IAKlC;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CACtB,SAAiB,EACjB,KAAa,EACb,SAAiB,EACjB,QAAgB,EAChB,QAAc;QAEd,MAAM,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;QAEjE,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE;gBACzD,SAAS;gBACT,KAAK;gBACL,SAAS;gBACT,QAAQ;aACT,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,IAAI,GAAqB;YAC7B,SAAS;YACT,KAAK;YACL,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ;YACR,QAAQ;SACT,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;YAC7C,OAAO;YACP,SAAS;YACT,KAAK;YACL,SAAS;SACV,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CACtB,SAAiB,EACjB,KAAa,EACb,SAAiB,EACjB,QAAgB;QAEhB,MAAM,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;QAEjE,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBAC7C,OAAO;gBACP,SAAS;gBACT,KAAK;gBACL,SAAS;aACV,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,OAAO,CACZ,SAAiB,EACjB,KAAa,EACb,SAAiB,EACjB,QAAgB;QAEhB,MAAM,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;QACjE,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEjD,MAAM,KAAK,GAAgB;gBACzB,OAAO;gBACP,aAAa;gBACb,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,QAAQ;aACT,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACxC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACjC,OAAO,EAAE,OAAO,CAAC,MAAM;gBACvB,aAAa,EAAE,aAAa,CAAC,MAAM;gBACnC,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,QAAQ;gBACR,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAK/C,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,EAAc;YAC1B,MAAM,EAAE,EAAc;SACvB,CAAC;QAEF,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC1C,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACvB,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAClD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEhE,8BAA8B;YAC9B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;YAC/C,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;gBACjC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC7C,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACxE,CAAC;YAED,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,YAAY,EAAE,CAAC;gBACxC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACd,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC5B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACzB,CAAC;YACH,CAAC;YAED,oCAAoC;YACpC,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAkB,CAAC;YACrD,KAAK,MAAM,GAAG,IAAI,oBAAoB,EAAE,CAAC;gBACvC,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;gBACnD,kBAAkB,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpF,CAAC;YAED,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,kBAAkB,EAAE,CAAC;gBAC9C,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACd,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC5B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACzB,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;oBACjD,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBACjC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,MAAM;oBACpC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM;iBAC7B,CAAC,CAAC;YACL,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAClG,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QACzB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAe;QACpC,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAG,wCAAwC,CAAC;QAC7D,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAe;QAC1C,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,MAAM,iBAAiB,GAAG,yBAAyB,CAAC;QACpD,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC1D,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,eAAuB;QAC7C,yCAAyC;QACzC,OAAO,eAAe;aACnB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;aAClB,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,YAAoB;QAChD,yCAAyC;QACzC,OAAO,YAAY;aAChB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;aAClB,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAe;QACvC,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACnC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,4BAA4B;QAClD,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,SAAiB,OAAO;QACrC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,qBAAqB;QACrB,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACrC,IAAI,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,MAAM,EAAE,CAAC;gBACtD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAClD,IAAI,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,MAAM,EAAE,CAAC;gBAC1D,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA7RD,0DA6RC;AAED,kBAAe,uBAAuB,CAAC"}
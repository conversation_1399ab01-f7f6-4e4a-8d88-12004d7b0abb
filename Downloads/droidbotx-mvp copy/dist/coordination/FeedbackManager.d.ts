import { WorkflowPhase } from '../orchestration/Orchestrator';
export interface AgentFeedback {
    sourcePhase: WorkflowPhase;
    targetPhase: WorkflowPhase;
    issues: string[];
    suggestedFixes: string[];
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: Date;
    sessionId: string;
    metadata?: Record<string, any>;
}
export interface FeedbackAnalysis {
    patterns: Map<string, number>;
    criticalIssues: AgentFeedback[];
    recommendedActions: string[];
    shouldRetry: boolean;
    retryPhase?: WorkflowPhase;
}
/**
 * Manages feedback loops between agents to enable learning from downstream failures
 * and coordinated fixing strategies
 */
export declare class FeedbackManager {
    private logger;
    private feedbackHistory;
    private patternAnalysis;
    private retryAttempts;
    private maxRetryAttempts;
    constructor();
    /**
     * Provide feedback from one agent to another
     */
    provideFeedback(feedback: AgentFeedback): Promise<void>;
    /**
     * Analyze feedback patterns and determine if retry is needed
     */
    analyzeFeedback(sessionId: string): FeedbackAnalysis;
    /**
     * Get feedback history for a session
     */
    getFeedbackHistory(sessionId: string): AgentFeedback[];
    /**
     * Clear feedback history for a session
     */
    clearFeedbackHistory(sessionId: string): void;
    /**
     * Get pattern analysis for learning
     */
    getPatternAnalysis(): Map<string, number>;
    /**
     * Check if retry is allowed for a session
     */
    private canRetry;
    /**
     * Increment retry attempts for a session
     */
    incrementRetryAttempts(sessionId: string): void;
    /**
     * Handle critical feedback immediately
     */
    private handleCriticalFeedback;
    /**
     * Update pattern analysis with new feedback
     */
    private updatePatternAnalysis;
    /**
     * Extract pattern from issue description
     */
    private extractPattern;
    /**
     * Generate feedback from test results
     */
    generateTestingFeedback(sessionId: string, testResults: any, compilationErrors: string[]): AgentFeedback;
}
export default FeedbackManager;
//# sourceMappingURL=FeedbackManager.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeedbackManager = void 0;
const Logger_1 = require("../core/Logger");
const Orchestrator_1 = require("../orchestration/Orchestrator");
/**
 * Manages feedback loops between agents to enable learning from downstream failures
 * and coordinated fixing strategies
 */
class FeedbackManager {
    constructor() {
        this.feedbackHistory = new Map();
        this.patternAnalysis = new Map();
        this.retryAttempts = new Map();
        this.maxRetryAttempts = 3;
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Provide feedback from one agent to another
     */
    async provideFeedback(feedback) {
        try {
            const sessionId = feedback.sessionId;
            // Store feedback in history
            if (!this.feedbackHistory.has(sessionId)) {
                this.feedbackHistory.set(sessionId, []);
            }
            this.feedbackHistory.get(sessionId).push(feedback);
            // Update pattern analysis
            this.updatePatternAnalysis(feedback);
            // Log feedback
            this.logger.info('Agent feedback received', {
                sourcePhase: feedback.sourcePhase,
                targetPhase: feedback.targetPhase,
                severity: feedback.severity,
                issueCount: feedback.issues.length,
                sessionId: feedback.sessionId
            });
            // Handle critical feedback immediately
            if (feedback.severity === 'critical') {
                await this.handleCriticalFeedback(feedback);
            }
        }
        catch (error) {
            this.logger.error('Error providing feedback', {
                error: error instanceof Error ? error.message : String(error),
                feedback: feedback.sourcePhase + ' -> ' + feedback.targetPhase
            });
        }
    }
    /**
     * Analyze feedback patterns and determine if retry is needed
     */
    analyzeFeedback(sessionId) {
        const sessionFeedback = this.feedbackHistory.get(sessionId) || [];
        const criticalIssues = sessionFeedback.filter(f => f.severity === 'critical');
        const patterns = new Map();
        const recommendedActions = [];
        // Analyze issue patterns
        sessionFeedback.forEach(feedback => {
            feedback.issues.forEach(issue => {
                const pattern = this.extractPattern(issue);
                patterns.set(pattern, (patterns.get(pattern) || 0) + 1);
            });
        });
        // Determine if retry is needed
        const shouldRetry = criticalIssues.length > 0 && this.canRetry(sessionId);
        let retryPhase;
        if (shouldRetry && criticalIssues.length > 0) {
            // Find the earliest phase that needs retry
            const phaseOrder = [
                Orchestrator_1.WorkflowPhase.PLAN,
                Orchestrator_1.WorkflowPhase.BUSINESS_LOGIC,
                Orchestrator_1.WorkflowPhase.GENERATE,
                Orchestrator_1.WorkflowPhase.UIUX,
                Orchestrator_1.WorkflowPhase.SECURITY,
                Orchestrator_1.WorkflowPhase.DATABASE,
                Orchestrator_1.WorkflowPhase.DEBUGGER,
                Orchestrator_1.WorkflowPhase.TESTING,
                Orchestrator_1.WorkflowPhase.DEVOPS,
                Orchestrator_1.WorkflowPhase.DEPLOY
            ];
            for (const phase of phaseOrder) {
                if (criticalIssues.some(issue => issue.targetPhase === phase)) {
                    retryPhase = phase;
                    break;
                }
            }
        }
        // Generate recommended actions
        if (patterns.has('duplicate_imports')) {
            recommendedActions.push('Enhance import/export management patterns');
        }
        if (patterns.has('missing_types')) {
            recommendedActions.push('Generate missing TypeScript type definitions');
        }
        if (patterns.has('empty_tests')) {
            recommendedActions.push('Improve test generation quality');
        }
        if (patterns.has('compilation_errors')) {
            recommendedActions.push('Apply comprehensive pre-compilation fixes');
        }
        return {
            patterns,
            criticalIssues,
            recommendedActions,
            shouldRetry,
            retryPhase
        };
    }
    /**
     * Get feedback history for a session
     */
    getFeedbackHistory(sessionId) {
        return this.feedbackHistory.get(sessionId) || [];
    }
    /**
     * Clear feedback history for a session
     */
    clearFeedbackHistory(sessionId) {
        this.feedbackHistory.delete(sessionId);
        this.retryAttempts.delete(sessionId);
    }
    /**
     * Get pattern analysis for learning
     */
    getPatternAnalysis() {
        return new Map(this.patternAnalysis);
    }
    /**
     * Check if retry is allowed for a session
     */
    canRetry(sessionId) {
        const attempts = this.retryAttempts.get(sessionId) || 0;
        return attempts < this.maxRetryAttempts;
    }
    /**
     * Increment retry attempts for a session
     */
    incrementRetryAttempts(sessionId) {
        const attempts = this.retryAttempts.get(sessionId) || 0;
        this.retryAttempts.set(sessionId, attempts + 1);
    }
    /**
     * Handle critical feedback immediately
     */
    async handleCriticalFeedback(feedback) {
        this.logger.warn('Critical feedback received - immediate action required', {
            sourcePhase: feedback.sourcePhase,
            targetPhase: feedback.targetPhase,
            issues: feedback.issues,
            suggestedFixes: feedback.suggestedFixes
        });
        // Store critical patterns for future prevention
        feedback.issues.forEach(issue => {
            const pattern = this.extractPattern(issue);
            this.patternAnalysis.set(pattern, (this.patternAnalysis.get(pattern) || 0) + 10); // Weight critical issues higher
        });
    }
    /**
     * Update pattern analysis with new feedback
     */
    updatePatternAnalysis(feedback) {
        feedback.issues.forEach(issue => {
            const pattern = this.extractPattern(issue);
            this.patternAnalysis.set(pattern, (this.patternAnalysis.get(pattern) || 0) + 1);
        });
    }
    /**
     * Extract pattern from issue description
     */
    extractPattern(issue) {
        const lowerIssue = issue.toLowerCase();
        if (lowerIssue.includes('duplicate') && lowerIssue.includes('import')) {
            return 'duplicate_imports';
        }
        if (lowerIssue.includes('missing') && (lowerIssue.includes('type') || lowerIssue.includes('interface'))) {
            return 'missing_types';
        }
        if (lowerIssue.includes('empty') && lowerIssue.includes('test')) {
            return 'empty_tests';
        }
        if (lowerIssue.includes('compilation') || lowerIssue.includes('typescript')) {
            return 'compilation_errors';
        }
        if (lowerIssue.includes('route') && lowerIssue.includes('registration')) {
            return 'route_registration';
        }
        if (lowerIssue.includes('database') && lowerIssue.includes('connection')) {
            return 'database_connection';
        }
        if (lowerIssue.includes('service') && lowerIssue.includes('instantiation')) {
            return 'service_instantiation';
        }
        return 'general_issue';
    }
    /**
     * Generate feedback from test results
     */
    generateTestingFeedback(sessionId, testResults, compilationErrors) {
        const issues = [];
        const suggestedFixes = [];
        let severity = 'low';
        // Analyze compilation errors
        if (compilationErrors.length > 0) {
            severity = 'critical';
            compilationErrors.forEach(error => {
                if (error.includes('Duplicate identifier')) {
                    issues.push('Duplicate import statements causing compilation failures');
                    suggestedFixes.push('Apply enhanced duplicate import detection and removal');
                }
                if (error.includes('has no exported member')) {
                    issues.push('Missing TypeScript type definitions in models');
                    suggestedFixes.push('Generate missing CreateData and UpdateData interfaces');
                }
                if (error.includes('Cannot find module')) {
                    issues.push('Missing file extensions in import statements');
                    suggestedFixes.push('Remove .js extensions from TypeScript imports');
                }
            });
        }
        // Analyze test failures
        if (testResults && testResults.failed > 0) {
            if (testResults.failed > testResults.passed) {
                severity = severity === 'critical' ? 'critical' : 'high';
            }
            issues.push(`${testResults.failed} test failures detected`);
            suggestedFixes.push('Improve test generation quality and error handling');
        }
        return {
            sourcePhase: Orchestrator_1.WorkflowPhase.TESTING,
            targetPhase: Orchestrator_1.WorkflowPhase.GENERATE,
            issues,
            suggestedFixes,
            severity,
            timestamp: new Date(),
            sessionId,
            metadata: {
                compilationErrors: compilationErrors.length,
                testResults
            }
        };
    }
}
exports.FeedbackManager = FeedbackManager;
exports.default = FeedbackManager;
//# sourceMappingURL=FeedbackManager.js.map
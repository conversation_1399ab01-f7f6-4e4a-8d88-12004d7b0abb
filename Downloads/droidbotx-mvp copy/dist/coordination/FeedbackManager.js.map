{"version": 3, "file": "FeedbackManager.js", "sourceRoot": "", "sources": ["../../src/coordination/FeedbackManager.ts"], "names": [], "mappings": ";;;AAAA,2CAAwC;AACxC,gEAA8D;AAqB9D;;;GAGG;AACH,MAAa,eAAe;IAO1B;QALQ,oBAAe,GAAiC,IAAI,GAAG,EAAE,CAAC;QAC1D,oBAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;QACjD,kBAAa,GAAwB,IAAI,GAAG,EAAE,CAAC;QAC/C,qBAAgB,GAAW,CAAC,CAAC;QAGnC,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAAC,QAAuB;QAClD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;YAErC,4BAA4B;YAC5B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBACzC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC1C,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEpD,0BAA0B;YAC1B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAErC,eAAe;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAC1C,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM;gBAClC,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC,CAAC;YAEH,uCAAuC;YACvC,IAAI,QAAQ,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;gBACrC,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC5C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,QAAQ,EAAE,QAAQ,CAAC,WAAW,GAAG,MAAM,GAAG,QAAQ,CAAC,WAAW;aAC/D,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,SAAiB;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAClE,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC;QAC9E,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC3C,MAAM,kBAAkB,GAAa,EAAE,CAAC;QAExC,yBAAyB;QACzB,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACjC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3C,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC1E,IAAI,UAAqC,CAAC;QAE1C,IAAI,WAAW,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7C,2CAA2C;YAC3C,MAAM,UAAU,GAAG;gBACjB,4BAAa,CAAC,IAAI;gBAClB,4BAAa,CAAC,cAAc;gBAC5B,4BAAa,CAAC,QAAQ;gBACtB,4BAAa,CAAC,IAAI;gBAClB,4BAAa,CAAC,QAAQ;gBACtB,4BAAa,CAAC,QAAQ;gBACtB,4BAAa,CAAC,QAAQ;gBACtB,4BAAa,CAAC,OAAO;gBACrB,4BAAa,CAAC,MAAM;gBACpB,4BAAa,CAAC,MAAM;aACrB,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE,CAAC;gBAC/B,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,EAAE,CAAC;oBAC9D,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACtC,kBAAkB,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;YAClC,kBAAkB,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC1E,CAAC;QACD,IAAI,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YAChC,kBAAkB,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,QAAQ,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACvC,kBAAkB,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACvE,CAAC;QAED,OAAO;YACL,QAAQ;YACR,cAAc;YACd,kBAAkB;YAClB,WAAW;YACX,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,SAAiB;QACzC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,SAAiB;QAC3C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,SAAiB;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACxD,OAAO,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,SAAiB;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,QAAuB;QAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wDAAwD,EAAE;YACzE,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,cAAc,EAAE,QAAQ,CAAC,cAAc;SACxC,CAAC,CAAC;QAEH,gDAAgD;QAChD,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,gCAAgC;QACpH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,QAAuB;QACnD,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAa;QAClC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAEvC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtE,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;YACxG,OAAO,eAAe,CAAC;QACzB,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAChE,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5E,OAAO,oBAAoB,CAAC;QAC9B,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACxE,OAAO,oBAAoB,CAAC;QAC9B,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACzE,OAAO,qBAAqB,CAAC;QAC/B,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3E,OAAO,uBAAuB,CAAC;QACjC,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,uBAAuB,CAC5B,SAAiB,EACjB,WAAgB,EAChB,iBAA2B;QAE3B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,IAAI,QAAQ,GAA2C,KAAK,CAAC;QAE7D,6BAA6B;QAC7B,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,QAAQ,GAAG,UAAU,CAAC;YACtB,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAChC,IAAI,KAAK,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE,CAAC;oBAC3C,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;oBACxE,cAAc,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBAC/E,CAAC;gBACD,IAAI,KAAK,CAAC,QAAQ,CAAC,wBAAwB,CAAC,EAAE,CAAC;oBAC7C,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;oBAC7D,cAAc,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBAC/E,CAAC;gBACD,IAAI,KAAK,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;oBACzC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;oBAC5D,cAAc,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,IAAI,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;gBAC5C,QAAQ,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;YAC3D,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,yBAAyB,CAAC,CAAC;YAC5D,cAAc,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO;YACL,WAAW,EAAE,4BAAa,CAAC,OAAO;YAClC,WAAW,EAAE,4BAAa,CAAC,QAAQ;YACnC,MAAM;YACN,cAAc;YACd,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS;YACT,QAAQ,EAAE;gBACR,iBAAiB,EAAE,iBAAiB,CAAC,MAAM;gBAC3C,WAAW;aACZ;SACF,CAAC;IACJ,CAAC;CACF;AA3QD,0CA2QC;AAED,kBAAe,eAAe,CAAC"}
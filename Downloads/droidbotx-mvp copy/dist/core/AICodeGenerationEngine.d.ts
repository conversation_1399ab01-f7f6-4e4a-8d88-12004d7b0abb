/**
 * True AI-Driven Code Generation Engine
 * Replaces template-based generation with intelligent, context-aware code generation
 * Uses advanced LLM capabilities to generate production-quality code
 */
export interface CodeGenerationContext {
    projectType: 'fullstack' | 'backend' | 'frontend' | 'mobile';
    businessDomain: string;
    technicalRequirements: string[];
    databaseSchema?: any;
    apiSpecification?: any;
    existingCode?: Record<string, string>;
    constraints?: {
        framework?: string;
        language?: string;
        patterns?: string[];
        security?: string[];
    };
}
export interface GeneratedCodeArtifact {
    filePath: string;
    content: string;
    type: 'component' | 'service' | 'model' | 'controller' | 'middleware' | 'config' | 'test';
    dependencies: string[];
    exports: string[];
    imports: string[];
    quality: {
        complexity: number;
        maintainability: number;
        testability: number;
        security: number;
    };
}
export interface CodeGenerationResult {
    artifacts: GeneratedCodeArtifact[];
    architecture: {
        patterns: string[];
        layers: string[];
        dependencies: Record<string, string[]>;
    };
    quality: {
        overallScore: number;
        codeComplexity: number;
        maintainability: number;
        testCoverage: number;
        securityScore: number;
    };
    recommendations: string[];
    nextSteps: string[];
}
export declare class AICodeGenerationEngine {
    private logger;
    private llmProvider;
    private generationPrompts;
    constructor();
    /**
     * Generate intelligent, context-aware code based on requirements
     */
    generateCode(context: CodeGenerationContext): Promise<CodeGenerationResult>;
    /**
     * Analyze context and determine optimal generation strategy
     */
    private analyzeGenerationStrategy;
    /**
     * Generate architecture blueprint based on context and strategy
     */
    private generateArchitectureBlueprint;
    /**
     * Generate individual code artifacts
     */
    private generateCodeArtifacts;
    /**
     * Generate artifacts for a specific layer
     */
    private generateLayerArtifacts;
    /**
     * Optimize generated code for quality and performance
     */
    private optimizeGeneratedCode;
    /**
     * Optimize a single code artifact
     */
    private optimizeArtifact;
    /**
     * Calculate quality metrics for generated code
     */
    private calculateQualityMetrics;
    /**
     * Generate recommendations for improvement
     */
    private generateRecommendations;
    /**
     * Generate next steps for development
     */
    private generateNextSteps;
    private parseStrategyResponse;
    private parseArchitectureResponse;
    private parseCodeResponse;
    private extractDependencies;
    private extractExports;
    private extractImports;
    private analyzeCodeQuality;
    private extractOptimizedCode;
    /**
     * Initialize generation prompts for different code types
     */
    private initializeGenerationPrompts;
}
//# sourceMappingURL=AICodeGenerationEngine.d.ts.map
{"version": 3, "file": "AICodeGenerationEngine.js", "sourceRoot": "", "sources": ["../../src/core/AICodeGenerationEngine.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,qCAAkC;AAClC,2DAAoE;AAkDpE,MAAa,sBAAsB;IAKjC;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,qCAAiB,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,OAA8B;QAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;YACrD,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,MAAM,EAAE,OAAO,CAAC,cAAc;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,oDAAoD;YACpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;YAE/D,kCAAkC;YAClC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEjF,0BAA0B;YAC1B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAE1E,sCAAsC;YACtC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEhF,4BAA4B;YAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YAEjE,2BAA2B;YAC3B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAExF,MAAM,MAAM,GAAyB;gBACnC,SAAS,EAAE,kBAAkB;gBAC7B,YAAY;gBACZ,OAAO;gBACP,eAAe;gBACf,SAAS,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,kBAAkB,CAAC;aACrE,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBACtD,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM;gBACtC,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY;aAC1C,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,+BAA+B,CAAC;YAC9F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CAAC,OAA8B;QACpE,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,mBAAmB,CAAE,CAAC;QAExE,MAAM,QAAQ,GAAiB;YAC7B;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,cAAc;aACxB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE;;gBAED,OAAO,CAAC,WAAW;mBAChB,OAAO,CAAC,cAAc;0BACf,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;eACnD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;oBAOvC;aACb;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YACjE,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,6BAA6B,CACzC,OAA8B,EAC9B,QAAa;QAEb,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,wBAAwB,CAAE,CAAC;QAE9E,MAAM,QAAQ,GAAiB;YAC7B;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,eAAe;aACzB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE;;mBAEE,OAAO,CAAC,cAAc;YAC7B,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;mBAC1B,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;qBACnD,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;;;;;;;8BAO9C;aACvB;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YACjE,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,OAA8B,EAC9B,YAAiB;QAEjB,MAAM,SAAS,GAA4B,EAAE,CAAC;QAE9C,8CAA8C;QAC9C,KAAK,MAAM,KAAK,IAAI,YAAY,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAC9C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;YACvF,SAAS,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,OAA8B,EAC9B,KAAU,EACV,YAAiB;QAEjB,MAAM,SAAS,GAA4B,EAAE,CAAC;QAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,aAAa,CAAE,CAAC;QAE5E,MAAM,QAAQ,GAAiB;YAC7B;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,WAAW;aACrB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,wCAAwC,KAAK,CAAC,IAAI;;cAErD,KAAK,CAAC,IAAI;oBACJ,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;gBACrC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;WAClC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;sBAOrB;aACf;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YACjE,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAE5E,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;YAClC,SAAS,CAAC,IAAI,CAAC;gBACb,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC;gBACpD,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;gBAC1C,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;gBAC1C,OAAO,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;aACrD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,SAAkC,EAClC,OAA8B;QAE9B,MAAM,kBAAkB,GAA4B,EAAE,CAAC;QAEvD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACjE,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,QAA+B,EAC/B,OAA8B;QAE9B,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,mBAAmB,CAAE,CAAC;QAE5E,MAAM,QAAQ,GAAiB;YAC7B;gBACE,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,kBAAkB;aAC5B;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE;;QAET,QAAQ,CAAC,QAAQ;QACjB,QAAQ,CAAC,IAAI;yBACI,QAAQ,CAAC,OAAO,CAAC,UAAU;;;;EAIlD,QAAQ,CAAC,OAAO;;;;;;;;6BAQW;aACtB;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YACjE,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAErE,OAAO;YACL,GAAG,QAAQ;YACX,OAAO,EAAE,gBAAgB;YACzB,OAAO,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC;SACzD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,SAAkC;QAChE,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC;QAExC,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC;QACnG,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC;QAC7G,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC;QACrG,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC;QAE/F,MAAM,YAAY,GAAG,CAAC,kBAAkB,GAAG,cAAc,GAAG,WAAW,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;QAEpG,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,EAAE,CAAC,GAAG,EAAE;YAChD,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC,GAAG,EAAE;YACnD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,EAAE,CAAC,GAAG,EAAE;YACzD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,CAAC,GAAG,EAAE;YAClD,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,EAAE;SACjD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CACnC,OAA8B,EAC9B,SAAkC;QAElC,0DAA0D;QAC1D,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,gCAAgC;QAChC,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QACjF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,eAAe,CAAC,IAAI,CAAC,YAAY,mBAAmB,CAAC,MAAM,wCAAwC,CAAC,CAAC;QACvG,CAAC;QAED,2BAA2B;QAC3B,MAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAC3E,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,eAAe,CAAC,IAAI,CAAC,qCAAqC,oBAAoB,CAAC,MAAM,QAAQ,CAAC,CAAC;QACjG,CAAC;QAED,yDAAyD;QACzD,eAAe,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;QAClF,eAAe,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAChE,eAAe,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAE/D,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,OAA8B,EAC9B,SAAkC;QAElC,OAAO;YACL,8BAA8B;YAC9B,+BAA+B;YAC/B,wBAAwB;YACxB,2BAA2B;YAC3B,+BAA+B;YAC/B,+BAA+B;SAChC,CAAC;IACJ,CAAC;IAED,0CAA0C;IAClC,qBAAqB,CAAC,QAAgB;QAC5C,qDAAqD;QACrD,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IAC3D,CAAC;IAEO,yBAAyB,CAAC,QAAgB;QAChD,iDAAiD;QACjD,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;IACxD,CAAC;IAEO,iBAAiB,CAAC,QAAgB,EAAE,SAAiB;QAC3D,gDAAgD;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACtC,iCAAiC;QACjC,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,cAAc,CAAC,IAAY;QACjC,4BAA4B;QAC5B,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,cAAc,CAAC,IAAY;QACjC,4BAA4B;QAC5B,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAC3C,+BAA+B;QAC/B,OAAO;YACL,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,CAAC;SACZ,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,QAAgB;QAC3C,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,2BAA2B;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,CAAC;YAC/B,CAAC,mBAAmB,EAAE,gMAAgM,CAAC;YAEvN,CAAC,wBAAwB,EAAE,sLAAsL,CAAC;YAElN,CAAC,uBAAuB,EAAE,iJAAiJ,CAAC;YAE5K,CAAC,oBAAoB,EAAE,gJAAgJ,CAAC;YAExK,CAAC,kBAAkB,EAAE,6GAA6G,CAAC;YAEnI,CAAC,sBAAsB,EAAE,4IAA4I,CAAC;YAEtK,CAAC,mBAAmB,EAAE,uIAAuI,CAAC;SAC/J,CAAC,CAAC;IACL,CAAC;CACF;AA/ZD,wDA+ZC"}
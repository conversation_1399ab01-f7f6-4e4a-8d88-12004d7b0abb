{"version": 3, "file": "AICodeGenerator.d.ts", "sourceRoot": "", "sources": ["../../src/core/AICodeGenerator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,sBAAsB,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AAE5E,MAAM,WAAW,qBAAqB;IACpC,IAAI,EAAE,oBAAoB,GAAG,eAAe,GAAG,iBAAiB,GAAG,eAAe,GAAG,aAAa,GAAG,eAAe,CAAC;IACrH,MAAM,CAAC,EAAE,cAAc,CAAC;IACxB,OAAO,EAAE,qBAAqB,CAAC;IAC/B,cAAc,EAAE,kBAAkB,CAAC;CACpC;AAED,MAAM,WAAW,qBAAqB;IACpC,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,sBAAsB,CAAC;IACvC,eAAe,EAAE,MAAM,CAAC;IACxB,qBAAqB,EAAE,MAAM,EAAE,CAAC;IAChC,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B,eAAe,EAAE,cAAc,EAAE,CAAC;CACnC;AAED,MAAM,WAAW,kBAAkB;IACjC,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,aAAa,EAAE,MAAM,EAAE,CAAC;IACxB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,oBAAoB,EAAE,MAAM,EAAE,CAAC;CAChC;AAED,MAAM,WAAW,aAAa;IAC5B,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,qBAAa,eAAgB,SAAQ,SAAS;;IA0BrC,SAAS,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO;IAIvB,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAI7C;;OAEG;IACU,sBAAsB,CAAC,OAAO,EAAE,qBAAqB,GAAG,OAAO,CAAC,aAAa,CAAC;IA0B3F;;OAEG;IACU,oBAAoB,CAAC,OAAO,EAAE,qBAAqB,GAAG,OAAO,CAAC,aAAa,CAAC;IA0BzF;;OAEG;IACU,sBAAsB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE,OAAO,EAAE,qBAAqB,GAAG,OAAO,CAAC,aAAa,CAAC;IA0BvH;;OAEG;IACU,oBAAoB,CAAC,OAAO,EAAE,qBAAqB,GAAG,OAAO,CAAC,aAAa,CAAC;IA0BzF,OAAO,CAAC,yBAAyB;IA4DjC,OAAO,CAAC,uBAAuB;IAkE/B,OAAO,CAAC,yBAAyB;IAoCjC,OAAO,CAAC,uBAAuB;IA0D/B,OAAO,CAAC,iBAAiB;IA4CzB,OAAO,CAAC,cAAc;IAYtB,OAAO,CAAC,cAAc;IAYtB,OAAO,CAAC,mBAAmB;IAiB3B;;OAEG;IACH,OAAO,CAAC,kBAAkB;CAiD3B"}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AICodeGenerator = void 0;
const BaseAgent_1 = require("./BaseAgent");
class AICodeGenerator extends BaseAgent_1.BaseAgent {
    constructor() {
        super('AICodeGenerator', 'Generates domain-specific code using AI based on semantic analysis', `You are an expert software developer specializing in generating high-quality, domain-specific code.

Your role is to generate production-ready code that:
1. Accurately reflects the business domain and requirements
2. Follows best practices and architectural patterns
3. Includes proper error handling and validation
4. Implements security requirements
5. Maintains consistency with existing codebase

You generate code for various components:
- React components with TypeScript for frontend
- Express.js routes and controllers for backend
- PostgreSQL database schemas and migrations
- Service classes with business logic
- Data models with validation
- Configuration files

Always generate complete, functional code that can be used immediately without modification.`);
    }
    canHandle(task) {
        return task.type === 'ai_code_generation';
    }
    async execute(task) {
        return { success: true, data: {} };
    }
    /**
     * Generate a React component based on business entity
     */
    async generateReactComponent(request) {
        const prompt = this.buildReactComponentPrompt(request);
        try {
            const codeResponse = await this.generateSingleLLMResponse(prompt, {
                temperature: 0.2,
                maxTokens: 4000
            });
            const generatedCode = this.parseCodeResponse(codeResponse, request.specifications.fileName);
            this.logInfo('React component generated', {
                fileName: generatedCode.fileName,
                linesOfCode: generatedCode.content.split('\n').length
            });
            return generatedCode;
        }
        catch (error) {
            this.logError('Failed to generate React component', {
                error: error instanceof Error ? error.message : String(error),
                fileName: request.specifications.fileName
            });
            throw new Error(`React component generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Generate Express.js route based on business entity
     */
    async generateExpressRoute(request) {
        const prompt = this.buildExpressRoutePrompt(request);
        try {
            const codeResponse = await this.generateSingleLLMResponse(prompt, {
                temperature: 0.2,
                maxTokens: 4000
            });
            const generatedCode = this.parseCodeResponse(codeResponse, request.specifications.fileName);
            this.logInfo('Express route generated', {
                fileName: generatedCode.fileName,
                linesOfCode: generatedCode.content.split('\n').length
            });
            return generatedCode;
        }
        catch (error) {
            this.logError('Failed to generate Express route', {
                error: error instanceof Error ? error.message : String(error),
                fileName: request.specifications.fileName
            });
            throw new Error(`Express route generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Generate database schema based on business entities
     */
    async generateDatabaseSchema(entities, context) {
        const prompt = this.buildDatabaseSchemaPrompt(entities, context);
        try {
            const codeResponse = await this.generateSingleLLMResponse(prompt, {
                temperature: 0.1,
                maxTokens: 6000
            });
            const generatedCode = this.parseCodeResponse(codeResponse, 'init.sql');
            this.logInfo('Database schema generated', {
                entitiesCount: entities.length,
                linesOfCode: generatedCode.content.split('\n').length
            });
            return generatedCode;
        }
        catch (error) {
            this.logError('Failed to generate database schema', {
                error: error instanceof Error ? error.message : String(error),
                entitiesCount: entities.length
            });
            throw new Error(`Database schema generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Generate service class based on business entity
     */
    async generateServiceClass(request) {
        const prompt = this.buildServiceClassPrompt(request);
        try {
            const codeResponse = await this.generateSingleLLMResponse(prompt, {
                temperature: 0.2,
                maxTokens: 4000
            });
            const generatedCode = this.parseCodeResponse(codeResponse, request.specifications.fileName);
            this.logInfo('Service class generated', {
                fileName: generatedCode.fileName,
                linesOfCode: generatedCode.content.split('\n').length
            });
            return generatedCode;
        }
        catch (error) {
            this.logError('Failed to generate service class', {
                error: error instanceof Error ? error.message : String(error),
                fileName: request.specifications.fileName
            });
            throw new Error(`Service class generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    buildReactComponentPrompt(request) {
        const { entity, context, specifications } = request;
        return `Generate a React TypeScript component for the ${context.businessDomain.domain} domain.

BUSINESS CONTEXT:
- Domain: ${context.businessDomain.domain}
- Project: ${context.projectName}
- Entity: ${entity?.name || 'N/A'}

COMPONENT SPECIFICATIONS:
- File: ${specifications.fileName}
- Purpose: ${specifications.purpose}
- Requirements: ${specifications.requirements.join(', ')}

ENTITY DETAILS:
${entity ? `
- Name: ${entity.name}
- Description: ${entity.description}
- Fields: ${(entity.fields || []).map(f => `${f.name}: ${f.type}${f.required ? ' (required)' : ''}`).join(', ')}
- Operations: ${(entity.operations || []).map(op => {
            if (typeof op === 'string')
                return op;
            if (op && typeof op === 'object' && op.name)
                return op.name;
            return 'operation';
        }).join(', ')}
` : 'No specific entity'}

TECHNICAL REQUIREMENTS:
- Framework: React with TypeScript
- Styling: Modern CSS-in-JS or Tailwind CSS
- State Management: React hooks
- Form Handling: Controlled components with validation
- Error Handling: Comprehensive error boundaries
- Accessibility: WCAG 2.1 AA compliance

BUSINESS RULES:
${specifications.businessRules.map(rule => `- ${rule}`).join('\n')}

VALIDATION RULES:
${specifications.validationRules.map(rule => `- ${rule}`).join('\n')}

Generate a complete, production-ready React component that:
1. Implements all specified functionality
2. Includes proper TypeScript interfaces
3. Handles loading, error, and success states
4. Implements form validation if applicable
5. Follows React best practices
6. Includes proper accessibility attributes
7. Is specific to the ${context.businessDomain.domain} domain

CRITICAL SYNTAX REQUIREMENTS:
- Use ONLY valid TypeScript syntax: (variable as any) NOT (variable as any: any)
- Use proper function parameters: function(param) NOT function(param: any)
- Use proper property access: object.property NOT (object as any).(property as any)
- Use proper string literals: 'string' NOT 'string': any
- Use proper function calls: func(param) NOT func(param: any)

Return ONLY the TypeScript code, no additional text or explanations.`;
    }
    buildExpressRoutePrompt(request) {
        const { entity, context, specifications } = request;
        return `Generate an Express.js TypeScript route for the ${context.businessDomain.domain} domain.

BUSINESS CONTEXT:
- Domain: ${context.businessDomain.domain}
- Project: ${context.projectName}
- Entity: ${entity?.name || 'N/A'}

ROUTE SPECIFICATIONS:
- File: ${specifications.fileName}
- Purpose: ${specifications.purpose}
- Requirements: ${specifications.requirements.join(', ')}

ENTITY DETAILS:
${entity ? `
- Name: ${entity.name}
- Description: ${entity.description}
- Fields: ${(entity.fields || []).map(f => `${f.name}: ${f.type}${f.required ? ' (required)' : ''}`).join(', ')}
- Operations: ${(entity.operations || []).map(op => {
            if (typeof op === 'string')
                return op;
            if (op && typeof op === 'object' && op.name && op.type)
                return `${op.name} (${op.type})`;
            return 'operation';
        }).join(', ')}
- Business Rules: ${(entity.operations || []).flatMap(op => {
            if (typeof op === 'string')
                return [];
            if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                return op.businessRules;
            return [];
        }).join(', ')}
` : 'No specific entity'}

TECHNICAL REQUIREMENTS:
- Framework: Express.js with TypeScript
- Database: PostgreSQL with connection pooling
- Authentication: JWT-based authentication
- Validation: Input validation with proper error messages
- Error Handling: Comprehensive error handling with logging
- Security: SQL injection prevention, rate limiting

BUSINESS RULES:
${specifications.businessRules.map(rule => `- ${rule}`).join('\n')}

SECURITY REQUIREMENTS:
${specifications.securityRequirements.map(req => `- ${req}`).join('\n')}

Generate a complete Express.js route file that:
1. Implements all CRUD operations for the entity
2. Includes proper TypeScript interfaces
3. Implements authentication and authorization
4. Includes comprehensive input validation
5. Handles all error cases with appropriate HTTP status codes
6. Includes proper logging for debugging
7. Follows Express.js best practices
8. Is specific to the ${context.businessDomain.domain} domain

CRITICAL SYNTAX REQUIREMENTS:
- Use ONLY valid TypeScript syntax: (variable as any) NOT (variable as any: any)
- Use proper function parameters: function(param) NOT function(param: any)
- Use proper property access: object.property NOT (object as any).(property as any)
- Use proper string literals: 'string' NOT 'string': any
- Use proper function calls: func(param) NOT func(param: any)

Return ONLY the TypeScript code, no additional text or explanations.`;
    }
    buildDatabaseSchemaPrompt(entities, context) {
        const domain = context.businessDomain?.domain || 'application';
        const projectName = context.projectName || 'project';
        return `Generate a PostgreSQL database schema for the ${domain} domain.

BUSINESS CONTEXT:
- Domain: ${domain}
- Project: ${projectName}

ENTITIES:
${entities.map(entity => `
- ${entity.name}: ${entity.description || 'No description'}
  Fields: ${(entity.fields || []).map(f => `${f.name} (${f.type}${f.required ? ', required' : ''})`).join(', ') || 'No fields defined'}
  Relationships: ${(entity.relationships || []).map(r => `${r.type} with ${r.target}`).join(', ') || 'No relationships defined'}
`).join('\n')}

TECHNICAL REQUIREMENTS:
- Database: PostgreSQL 14+
- Extensions: uuid-ossp, pgcrypto
- Indexing: Proper indexes for performance
- Constraints: Foreign keys, unique constraints, check constraints
- Security: Row-level security where applicable

Generate a complete SQL schema that:
1. Creates all necessary tables with proper data types
2. Implements all relationships with foreign keys
3. Includes proper indexes for performance
4. Adds check constraints for data validation
5. Includes sample data for testing
6. Follows PostgreSQL best practices
7. Is optimized for the ${context.businessDomain.domain} domain

Return ONLY the SQL code, no additional text or explanations.`;
    }
    buildServiceClassPrompt(request) {
        const { entity, context, specifications } = request;
        return `Generate a TypeScript service class for the ${context.businessDomain.domain} domain.

BUSINESS CONTEXT:
- Domain: ${context.businessDomain.domain}
- Project: ${context.projectName}
- Entity: ${entity?.name || 'N/A'}

SERVICE SPECIFICATIONS:
- File: ${specifications.fileName}
- Purpose: ${specifications.purpose}
- Requirements: ${specifications.requirements.join(', ')}

ENTITY DETAILS:
${entity ? `
- Name: ${entity.name}
- Description: ${entity.description}
- Operations: ${(entity.operations || []).map(op => {
            if (typeof op === 'string')
                return op;
            if (op && typeof op === 'object' && op.name && op.type)
                return `${op.name} (${op.type}): ${op.description || 'No description'}`;
            return 'operation';
        }).join('\n  ')}
- Business Rules: ${(entity.operations || []).flatMap(op => {
            if (typeof op === 'string')
                return [];
            if (op && typeof op === 'object' && Array.isArray(op.businessRules))
                return op.businessRules;
            return [];
        }).join(', ')}
` : 'No specific entity'}

TECHNICAL REQUIREMENTS:
- Language: TypeScript
- Database: PostgreSQL with connection pooling
- Patterns: Repository pattern, dependency injection
- Error Handling: Custom business exceptions
- Logging: Comprehensive logging for debugging
- Validation: Business rule validation

Generate a complete service class that:
1. Implements all business operations for the entity
2. Includes proper TypeScript interfaces and types
3. Implements business rule validation
4. Handles database operations with proper error handling
5. Includes comprehensive logging
6. Follows SOLID principles
7. Is specific to the ${context.businessDomain.domain} domain

CRITICAL SYNTAX REQUIREMENTS:
- Use ONLY valid TypeScript syntax: (variable as any) NOT (variable as any: any)
- Use proper function parameters: function(param) NOT function(param: any)
- Use proper property access: object.property NOT (object as any).(property as any)
- Use proper string literals: 'string' NOT 'string': any
- Use proper function calls: func(param) NOT func(param: any)

Return ONLY the TypeScript code, no additional text or explanations.`;
    }
    parseCodeResponse(response, fileName) {
        try {
            // Extract code from response (remove any markdown formatting)
            let code = response.trim();
            // Remove markdown code blocks if present (multiple patterns)
            const codeBlockPatterns = [
                /```(?:typescript|ts|javascript|js|sql|tsx|jsx)?\n?([\s\S]*?)\n?```/g,
                /```\n?([\s\S]*?)\n?```/g,
                /`{3,}\w*\n?([\s\S]*?)\n?`{3,}/g
            ];
            for (const pattern of codeBlockPatterns) {
                const match = code.match(pattern);
                if (match) {
                    code = match[1] || match[0];
                    break;
                }
            }
            // Clean up common AI response artifacts
            code = this.cleanCodeArtifacts(code);
            // Extract imports and exports
            const imports = this.extractImports(code);
            const exports = this.extractExports(code);
            const dependencies = this.extractDependencies(code);
            return {
                fileName,
                content: code,
                dependencies,
                imports,
                exports
            };
        }
        catch (error) {
            this.logError('Failed to parse code response', {
                error: error instanceof Error ? error.message : String(error),
                fileName
            });
            throw new Error(`Failed to parse generated code: ${error instanceof Error ? error.message : 'Invalid response'}`);
        }
    }
    extractImports(code) {
        const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
        const imports = [];
        let match;
        while ((match = importRegex.exec(code)) !== null) {
            imports.push(match[1]);
        }
        return imports;
    }
    extractExports(code) {
        const exportRegex = /export\s+(?:default\s+)?(?:class|function|const|let|var|interface|type)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
        const exports = [];
        let match;
        while ((match = exportRegex.exec(code)) !== null) {
            exports.push(match[1]);
        }
        return exports;
    }
    extractDependencies(code) {
        const dependencies = [];
        // Extract npm package dependencies from imports
        const importRegex = /import\s+.*?\s+from\s+['"]([^'"./][^'"]*)['"]/g;
        let match;
        while ((match = importRegex.exec(code)) !== null) {
            const packageName = match[1].split('/')[0];
            if (!dependencies.includes(packageName)) {
                dependencies.push(packageName);
            }
        }
        return dependencies;
    }
    /**
     * Clean common AI response artifacts from generated code
     */
    cleanCodeArtifacts(code) {
        let cleaned = code;
        // Remove multi-line markdown code blocks (most common issue)
        // Pattern 1: ```typescript at start and ``` at end
        cleaned = cleaned.replace(/^```typescript\s*\n/gm, '');
        cleaned = cleaned.replace(/^```tsx\s*\n/gm, '');
        cleaned = cleaned.replace(/^```javascript\s*\n/gm, '');
        cleaned = cleaned.replace(/^```jsx\s*\n/gm, '');
        cleaned = cleaned.replace(/^```\s*\n/gm, '');
        // Remove closing markdown blocks
        cleaned = cleaned.replace(/\n```\s*$/gm, '');
        cleaned = cleaned.replace(/^```\s*$/gm, '');
        // Remove common AI response prefixes/suffixes
        const artifactPatterns = [
            /^Here's the.*?:\s*/i,
            /^Here is the.*?:\s*/i,
            /^This is the.*?:\s*/i,
            /^The following.*?:\s*/i,
            /^Below is the.*?:\s*/i,
            /^\s*```\w*\s*/,
            /\s*```\s*$/,
            /^typescript\s*/i,
            /^javascript\s*/i,
            /^react\s*/i,
            /^tsx\s*/i,
            /^jsx\s*/i
        ];
        for (const pattern of artifactPatterns) {
            cleaned = cleaned.replace(pattern, '');
        }
        // Remove standalone 'x' characters that appear as artifacts
        cleaned = cleaned.replace(/^\s*x\s*$/gm, '');
        // Remove excessive empty lines (more than 2 consecutive)
        cleaned = cleaned.replace(/\n{3,}/g, '\n\n');
        // Remove trailing whitespace from each line
        cleaned = cleaned.split('\n').map(line => line.trimEnd()).join('\n');
        // Ensure proper line endings
        cleaned = cleaned.trim();
        return cleaned;
    }
}
exports.AICodeGenerator = AICodeGenerator;
//# sourceMappingURL=AICodeGenerator.js.map
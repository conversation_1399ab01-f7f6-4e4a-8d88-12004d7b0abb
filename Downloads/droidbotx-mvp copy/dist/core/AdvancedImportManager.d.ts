export interface ImportDeclaration {
    source: string;
    imports: string[];
    isDefault?: boolean;
    isNamespace?: boolean;
    alias?: string;
}
export interface ExportDeclaration {
    exports: string[];
    isDefault?: boolean;
    source?: string;
}
export interface DependencyGraph {
    [filePath: string]: {
        imports: ImportDeclaration[];
        exports: ExportDeclaration[];
        dependencies: string[];
        dependents: string[];
    };
}
export interface ImportConflict {
    filePath: string;
    conflictType: 'duplicate' | 'circular' | 'missing' | 'version';
    details: string;
    suggestions: string[];
}
/**
 * Advanced Import/Export Management System for Phase 3
 * Eliminates duplicate imports and ensures clean TypeScript compilation
 */
export declare class AdvancedImportManager {
    private logger;
    private dependencyGraph;
    private globalExports;
    private importConflicts;
    private resolvedImports;
    constructor();
    private logInfo;
    private logWarn;
    private logError;
    /**
     * Register a file's imports and exports in the dependency graph
     */
    registerFile(filePath: string, content: string): void;
    /**
     * Analyze and resolve all import conflicts
     */
    analyzeConflicts(): ImportConflict[];
    /**
     * Generate optimized imports for a file
     */
    generateOptimizedImports(filePath: string): string[];
    /**
     * Apply import optimizations to file content
     */
    optimizeFileImports(filePath: string, content: string): string;
    /**
     * Get dependency resolution report
     */
    getDependencyReport(): {
        totalFiles: number;
        totalDependencies: number;
        circularDependencies: string[][];
        conflictsResolved: number;
        optimizationSavings: number;
    };
    /**
     * Extract import declarations from file content
     */
    private extractImports;
    /**
     * Extract export declarations from file content
     */
    private extractExports;
    /**
     * Resolve module path relative to importing file
     */
    private resolveModulePath;
    /**
     * Detect duplicate imports in a file
     */
    private detectDuplicateImports;
    /**
     * Detect circular dependencies
     */
    private detectCircularDependencies;
    /**
     * Detect missing imports
     */
    private detectMissingImports;
    /**
     * Find all circular dependencies in the graph
     */
    private findCircularDependencies;
    /**
     * DFS to find cycles from a specific node
     */
    private findCyclesFromNode;
    /**
     * Format import statement
     */
    private formatImportStatement;
    /**
     * Clear all data
     */
    clear(): void;
}
//# sourceMappingURL=AdvancedImportManager.d.ts.map
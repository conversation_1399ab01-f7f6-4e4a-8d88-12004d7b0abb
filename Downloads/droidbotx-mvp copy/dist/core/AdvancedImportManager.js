"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedImportManager = void 0;
const Logger_1 = require("./Logger");
/**
 * Advanced Import/Export Management System for Phase 3
 * Eliminates duplicate imports and ensures clean TypeScript compilation
 */
class AdvancedImportManager {
    constructor() {
        this.dependencyGraph = {};
        this.globalExports = new Map(); // module -> exports
        this.importConflicts = [];
        this.resolvedImports = new Map();
        this.logger = Logger_1.Logger.getInstance();
    }
    logInfo(message, context) {
        this.logger.info(`[AdvancedImportManager] ${message}`, context);
    }
    logWarn(message, context) {
        this.logger.warn(`[AdvancedImportManager] ${message}`, context);
    }
    logError(message, context) {
        this.logger.error(`[AdvancedImportManager] ${message}`, context);
    }
    /**
     * Register a file's imports and exports in the dependency graph
     */
    registerFile(filePath, content) {
        this.logInfo('Registering file in dependency graph', { filePath });
        const imports = this.extractImports(content);
        const exports = this.extractExports(content);
        const dependencies = imports.map(imp => this.resolveModulePath(imp.source, filePath));
        this.dependencyGraph[filePath] = {
            imports,
            exports,
            dependencies,
            dependents: []
        };
        // Update global exports registry
        this.globalExports.set(filePath, exports.flatMap(exp => exp.exports));
        // Update dependents
        dependencies.forEach(dep => {
            if (this.dependencyGraph[dep]) {
                this.dependencyGraph[dep].dependents.push(filePath);
            }
        });
        this.logInfo('File registered successfully', {
            filePath,
            importsCount: imports.length,
            exportsCount: exports.length,
            dependenciesCount: dependencies.length
        });
    }
    /**
     * Analyze and resolve all import conflicts
     */
    analyzeConflicts() {
        this.importConflicts = [];
        Object.keys(this.dependencyGraph).forEach(filePath => {
            this.detectDuplicateImports(filePath);
            this.detectCircularDependencies(filePath);
            this.detectMissingImports(filePath);
        });
        this.logInfo('Import conflict analysis completed', {
            totalConflicts: this.importConflicts.length,
            duplicateConflicts: this.importConflicts.filter(c => c.conflictType === 'duplicate').length,
            circularConflicts: this.importConflicts.filter(c => c.conflictType === 'circular').length,
            missingConflicts: this.importConflicts.filter(c => c.conflictType === 'missing').length
        });
        return this.importConflicts;
    }
    /**
     * Generate optimized imports for a file
     */
    generateOptimizedImports(filePath) {
        const fileData = this.dependencyGraph[filePath];
        if (!fileData) {
            this.logWarn('File not found in dependency graph', { filePath });
            return [];
        }
        // Group imports by source
        const importGroups = new Map();
        fileData.imports.forEach(imp => {
            const existing = importGroups.get(imp.source);
            if (existing) {
                // Merge imports from same source
                existing.imports = [...new Set([...existing.imports, ...imp.imports])];
            }
            else {
                importGroups.set(imp.source, { ...imp });
            }
        });
        // Generate clean import statements
        const optimizedImports = [];
        // Sort imports: external modules first, then relative imports
        const sortedSources = Array.from(importGroups.keys()).sort((a, b) => {
            const aIsExternal = !a.startsWith('./') && !a.startsWith('../');
            const bIsExternal = !b.startsWith('./') && !b.startsWith('../');
            if (aIsExternal && !bIsExternal)
                return -1;
            if (!aIsExternal && bIsExternal)
                return 1;
            return a.localeCompare(b);
        });
        sortedSources.forEach(source => {
            const imp = importGroups.get(source);
            optimizedImports.push(this.formatImportStatement(imp));
        });
        this.resolvedImports.set(filePath, Array.from(importGroups.values()));
        this.logInfo('Generated optimized imports', {
            filePath,
            originalImports: fileData.imports.length,
            optimizedImports: optimizedImports.length,
            duplicatesRemoved: fileData.imports.length - optimizedImports.length
        });
        return optimizedImports;
    }
    /**
     * Apply import optimizations to file content
     */
    optimizeFileImports(filePath, content) {
        const optimizedImports = this.generateOptimizedImports(filePath);
        // Remove existing imports
        const contentWithoutImports = content.replace(/^import\s+.*?;?\s*$/gm, '');
        // Add optimized imports at the top
        const optimizedContent = [
            ...optimizedImports,
            '',
            contentWithoutImports.trim()
        ].join('\n');
        this.logInfo('File imports optimized', {
            filePath,
            originalLines: content.split('\n').length,
            optimizedLines: optimizedContent.split('\n').length
        });
        return optimizedContent;
    }
    /**
     * Get dependency resolution report
     */
    getDependencyReport() {
        const circularDeps = this.findCircularDependencies();
        const totalDeps = Object.values(this.dependencyGraph)
            .reduce((sum, file) => sum + file.dependencies.length, 0);
        const optimizationSavings = Object.keys(this.dependencyGraph)
            .reduce((savings, filePath) => {
            const original = this.dependencyGraph[filePath].imports.length;
            const optimized = this.resolvedImports.get(filePath)?.length || original;
            return savings + (original - optimized);
        }, 0);
        return {
            totalFiles: Object.keys(this.dependencyGraph).length,
            totalDependencies: totalDeps,
            circularDependencies: circularDeps,
            conflictsResolved: this.importConflicts.length,
            optimizationSavings
        };
    }
    /**
     * Extract import declarations from file content
     */
    extractImports(content) {
        const imports = [];
        const importRegex = /import\s+(?:(\w+)|{([^}]+)}|\*\s+as\s+(\w+))\s+from\s+['"]([^'"]+)['"];?/g;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            const [, defaultImport, namedImports, namespaceImport, source] = match;
            if (defaultImport) {
                imports.push({
                    source,
                    imports: [defaultImport],
                    isDefault: true
                });
            }
            else if (namedImports) {
                imports.push({
                    source,
                    imports: namedImports.split(',').map(imp => imp.trim())
                });
            }
            else if (namespaceImport) {
                imports.push({
                    source,
                    imports: [namespaceImport],
                    isNamespace: true
                });
            }
        }
        return imports;
    }
    /**
     * Extract export declarations from file content
     */
    extractExports(content) {
        const exports = [];
        // Named exports
        const namedExportRegex = /export\s+(?:const|let|var|function|class|interface|type)\s+(\w+)/g;
        let match;
        while ((match = namedExportRegex.exec(content)) !== null) {
            exports.push({ exports: [match[1]] });
        }
        // Export statements
        const exportStatementRegex = /export\s+{([^}]+)}/g;
        while ((match = exportStatementRegex.exec(content)) !== null) {
            const exportNames = match[1].split(',').map(exp => exp.trim());
            exports.push({ exports: exportNames });
        }
        // Default exports
        if (content.includes('export default')) {
            exports.push({ exports: ['default'], isDefault: true });
        }
        return exports;
    }
    /**
     * Resolve module path relative to importing file
     */
    resolveModulePath(source, fromFile) {
        if (source.startsWith('./') || source.startsWith('../')) {
            // Relative import - resolve relative to fromFile
            const path = require('path');
            return path.resolve(path.dirname(fromFile), source);
        }
        return source; // External module
    }
    /**
     * Detect duplicate imports in a file
     */
    detectDuplicateImports(filePath) {
        const fileData = this.dependencyGraph[filePath];
        const sourceCount = new Map();
        fileData.imports.forEach(imp => {
            const count = sourceCount.get(imp.source) || 0;
            sourceCount.set(imp.source, count + 1);
        });
        sourceCount.forEach((count, source) => {
            if (count > 1) {
                this.importConflicts.push({
                    filePath,
                    conflictType: 'duplicate',
                    details: `Duplicate imports from '${source}' (${count} times)`,
                    suggestions: [`Merge imports from '${source}' into single statement`]
                });
            }
        });
    }
    /**
     * Detect circular dependencies
     */
    detectCircularDependencies(filePath) {
        const visited = new Set();
        const recursionStack = new Set();
        const hasCycle = (current, path) => {
            if (recursionStack.has(current)) {
                const cycleStart = path.indexOf(current);
                const cycle = path.slice(cycleStart);
                this.importConflicts.push({
                    filePath,
                    conflictType: 'circular',
                    details: `Circular dependency detected: ${cycle.join(' -> ')} -> ${current}`,
                    suggestions: ['Refactor to remove circular dependency', 'Use dependency injection']
                });
                return true;
            }
            if (visited.has(current))
                return false;
            visited.add(current);
            recursionStack.add(current);
            const deps = this.dependencyGraph[current]?.dependencies || [];
            for (const dep of deps) {
                if (hasCycle(dep, [...path, current]))
                    return true;
            }
            recursionStack.delete(current);
            return false;
        };
        hasCycle(filePath, []);
    }
    /**
     * Detect missing imports
     */
    detectMissingImports(filePath) {
        const fileData = this.dependencyGraph[filePath];
        fileData.dependencies.forEach(dep => {
            if (!this.globalExports.has(dep) && dep.startsWith('./')) {
                this.importConflicts.push({
                    filePath,
                    conflictType: 'missing',
                    details: `Missing dependency: '${dep}'`,
                    suggestions: [`Create missing file '${dep}'`, 'Update import path']
                });
            }
        });
    }
    /**
     * Find all circular dependencies in the graph
     */
    findCircularDependencies() {
        const cycles = [];
        const visited = new Set();
        Object.keys(this.dependencyGraph).forEach(startNode => {
            if (!visited.has(startNode)) {
                const path = [];
                const recursionStack = new Set();
                this.findCyclesFromNode(startNode, path, recursionStack, visited, cycles);
            }
        });
        return cycles;
    }
    /**
     * DFS to find cycles from a specific node
     */
    findCyclesFromNode(node, path, recursionStack, visited, cycles) {
        visited.add(node);
        recursionStack.add(node);
        path.push(node);
        const deps = this.dependencyGraph[node]?.dependencies || [];
        for (const dep of deps) {
            if (recursionStack.has(dep)) {
                const cycleStart = path.indexOf(dep);
                cycles.push([...path.slice(cycleStart), dep]);
            }
            else if (!visited.has(dep)) {
                this.findCyclesFromNode(dep, path, recursionStack, visited, cycles);
            }
        }
        recursionStack.delete(node);
        path.pop();
    }
    /**
     * Format import statement
     */
    formatImportStatement(imp) {
        if (imp.isDefault) {
            return `import ${imp.imports[0]} from '${imp.source}';`;
        }
        else if (imp.isNamespace) {
            return `import * as ${imp.imports[0]} from '${imp.source}';`;
        }
        else {
            const imports = imp.imports.sort().join(', ');
            return `import { ${imports} } from '${imp.source}';`;
        }
    }
    /**
     * Clear all data
     */
    clear() {
        this.dependencyGraph = {};
        this.globalExports.clear();
        this.importConflicts = [];
        this.resolvedImports.clear();
        this.logInfo('Import manager cleared');
    }
}
exports.AdvancedImportManager = AdvancedImportManager;
//# sourceMappingURL=AdvancedImportManager.js.map
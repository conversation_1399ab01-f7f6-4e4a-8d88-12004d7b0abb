import { LLMProviderSystem, LLMMessage, LLMRequestOptions } from './LLMProviderSystem';
import { ToolManager } from './ToolManager';
import { Logger } from './Logger';
export interface AgentContext {
    sessionId: string;
    userId?: string;
    metadata?: Record<string, any>;
}
export interface AgentTask {
    id: string;
    type: string;
    description: string;
    parameters: Record<string, any>;
    context: AgentContext;
}
export interface AgentResult {
    success: boolean;
    data?: any;
    error?: string;
    metadata?: Record<string, any>;
}
export declare abstract class BaseAgent {
    protected name: string;
    protected description: string;
    protected llmProvider: LLMProviderSystem;
    protected toolManager: ToolManager;
    protected logger: Logger;
    protected systemPrompt: string;
    constructor(name: string, description: string, systemPrompt: string);
    getName(): string;
    getDescription(): string;
    abstract canHandle(task: AgentTask): boolean;
    abstract execute(task: AgentTask): Promise<AgentResult>;
    protected generateLLMResponse(messages: LLMMessage[], options?: LLMRequestOptions): Promise<string>;
    protected generateSingleLLMResponse(prompt: string, options?: LLMRequestOptions): Promise<string>;
    protected useTool(toolName: string, params?: Record<string, any>): Promise<any>;
    protected logInfo(message: string, context?: Record<string, any>): void;
    protected logError(message: string, context?: Record<string, any>): void;
    protected logDebug(message: string, context?: Record<string, any>): void;
    protected logWarn(message: string, context?: Record<string, any>): void;
    protected addImportSafely(content: string, importStatement: string): string;
    protected addRegistrationSafely(content: string, registrationStatement: string): string;
    protected fileExistsSafely(filePath: string): boolean;
    protected readFileSafely(filePath: string): string | null;
    protected writeFileSafely(filePath: string, content: string): boolean;
    protected enhanceFileIncrementally(filePath: string, enhancementFn: (content: string) => string): boolean;
}
//# sourceMappingURL=BaseAgent.d.ts.map
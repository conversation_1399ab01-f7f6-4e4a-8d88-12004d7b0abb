"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAgent = void 0;
const LLMProviderSystem_1 = require("./LLMProviderSystem");
const ToolManager_1 = require("./ToolManager");
const Logger_1 = require("./Logger");
class BaseAgent {
    constructor(name, description, systemPrompt) {
        this.name = name;
        this.description = description;
        this.systemPrompt = systemPrompt;
        this.llmProvider = LLMProviderSystem_1.LLMProviderSystem.getInstance();
        this.toolManager = ToolManager_1.ToolManager.getInstance();
        this.logger = Logger_1.Logger.getInstance();
    }
    getName() {
        return this.name;
    }
    getDescription() {
        return this.description;
    }
    async generateLLMResponse(messages, options = {}) {
        const response = await this.llmProvider.generateResponse(messages, {
            systemPrompt: this.systemPrompt,
            ...options,
        });
        return response.content;
    }
    async generateSingleLLMResponse(prompt, options = {}) {
        return await this.llmProvider.generateSingleResponse(prompt, {
            systemPrompt: this.systemPrompt,
            ...options,
        });
    }
    async useTool(toolName, params = {}) {
        const result = await this.toolManager.executeTool(toolName, params);
        if (!result.success) {
            throw new Error(`Tool execution failed: ${result.error}`);
        }
        return result.data;
    }
    logInfo(message, context) {
        this.logger.info(`[${this.name}] ${message}`, context);
    }
    logError(message, context) {
        this.logger.error(`[${this.name}] ${message}`, context);
    }
    logDebug(message, context) {
        this.logger.debug(`[${this.name}] ${message}`, context);
    }
    logWarn(message, context) {
        this.logger.warn(`[${this.name}] ${message}`, context);
    }
    // SecurityAgent-inspired defensive programming methods
    addImportSafely(content, importStatement) {
        const trimmedImport = importStatement.trim();
        // Check if import already exists (exact match or similar)
        if (content.includes(trimmedImport) ||
            content.includes(importStatement.replace(/'/g, '"')) ||
            content.includes(importStatement.replace(/"/g, "'"))) {
            return content; // Already exists
        }
        // Find last import and add after it
        const lastImportIndex = content.lastIndexOf('import ');
        if (lastImportIndex > -1) {
            const nextLineIndex = content.indexOf('\n', lastImportIndex);
            return content.slice(0, nextLineIndex + 1) +
                importStatement + '\n' +
                content.slice(nextLineIndex + 1);
        }
        // If no imports found, add at the beginning
        return importStatement + '\n' + content;
    }
    addRegistrationSafely(content, registrationStatement) {
        const trimmedRegistration = registrationStatement.trim();
        // Check if registration already exists
        if (content.includes(trimmedRegistration) ||
            content.includes(registrationStatement.replace(/'/g, '"')) ||
            content.includes(registrationStatement.replace(/"/g, "'"))) {
            return content; // Already exists
        }
        // Find appropriate location to add registration
        const errorHandlerIndex = content.indexOf('app.use(errorHandler)');
        if (errorHandlerIndex > -1) {
            return content.slice(0, errorHandlerIndex) +
                registrationStatement + '\n' +
                content.slice(errorHandlerIndex);
        }
        // Add before the last app.use statement
        const lastAppUseIndex = content.lastIndexOf('app.use(');
        if (lastAppUseIndex > -1) {
            const nextLineIndex = content.indexOf('\n', lastAppUseIndex);
            return content.slice(0, nextLineIndex + 1) +
                registrationStatement + '\n' +
                content.slice(nextLineIndex + 1);
        }
        return content + '\n' + registrationStatement;
    }
    fileExistsSafely(filePath) {
        try {
            return require('fs').existsSync(filePath);
        }
        catch {
            return false;
        }
    }
    readFileSafely(filePath) {
        try {
            return require('fs').readFileSync(filePath, 'utf8');
        }
        catch (error) {
            this.logError(`Failed to read file: ${filePath}`, { error: error instanceof Error ? error.message : String(error) });
            return null;
        }
    }
    writeFileSafely(filePath, content) {
        try {
            const fs = require('fs');
            const path = require('path');
            // Ensure directory exists
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            fs.writeFileSync(filePath, content);
            return true;
        }
        catch (error) {
            this.logError(`Failed to write file: ${filePath}`, { error: error instanceof Error ? error.message : String(error) });
            return false;
        }
    }
    enhanceFileIncrementally(filePath, enhancementFn) {
        if (!this.fileExistsSafely(filePath)) {
            this.logWarn(`File not found for enhancement: ${filePath}`);
            return false;
        }
        const originalContent = this.readFileSafely(filePath);
        if (!originalContent) {
            return false;
        }
        const enhancedContent = enhancementFn(originalContent);
        // Only write if content actually changed
        if (enhancedContent !== originalContent) {
            return this.writeFileSafely(filePath, enhancedContent);
        }
        return true; // No changes needed
    }
}
exports.BaseAgent = BaseAgent;
//# sourceMappingURL=BaseAgent.js.map
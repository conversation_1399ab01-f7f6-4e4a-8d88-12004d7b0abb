/**
 * Comprehensive Testing Framework
 * Implements automated test generation for unit tests, integration tests, and E2E tests
 * Covers all generated code layers (database, business logic, API, frontend)
 */
import { DatabaseSchema } from './DatabaseCodeSynchronizer';
import { OpenAPISpec } from './ContractFirstAPIGenerator';
export interface TestConfiguration {
    projectPath: string;
    testFrameworks: {
        unit: 'jest' | 'vitest' | 'mocha';
        integration: 'jest' | 'supertest' | 'newman';
        e2e: 'playwright' | 'cypress' | 'selenium';
    };
    coverage: {
        threshold: number;
        includeIntegration: boolean;
        includeE2E: boolean;
    };
    mockStrategy: 'full' | 'partial' | 'minimal';
}
export interface TestSuite {
    type: 'unit' | 'integration' | 'e2e';
    layer: 'database' | 'business' | 'api' | 'frontend';
    files: Array<{
        filePath: string;
        content: string;
        dependencies: string[];
    }>;
    configuration: {
        setup: string;
        teardown: string;
        mocks: string[];
        fixtures: string[];
    };
}
export interface TestGenerationResult {
    testSuites: TestSuite[];
    configuration: {
        jestConfig: string;
        playwrightConfig: string;
        setupFiles: string[];
        mockFiles: string[];
    };
    coverage: {
        expectedCoverage: number;
        criticalPaths: string[];
        testCount: number;
    };
    recommendations: string[];
}
export declare class ComprehensiveTestingFramework {
    private logger;
    private llmProvider;
    private testTemplates;
    constructor();
    /**
     * Generate comprehensive test suite for the entire application
     */
    generateComprehensiveTests(config: TestConfiguration, context: {
        databaseSchema?: DatabaseSchema;
        openAPISpec?: OpenAPISpec;
        generatedCode?: Record<string, string>;
        businessLogic?: any;
    }): Promise<TestGenerationResult>;
    /**
     * Generate database layer tests
     */
    private generateDatabaseTests;
    /**
     * Generate database unit tests
     */
    private generateDatabaseUnitTests;
    /**
     * Generate model unit test
     */
    private generateModelUnitTest;
    /**
     * Generate business logic tests
     */
    private generateBusinessLogicTests;
    /**
     * Generate API tests
     */
    private generateAPITests;
    /**
     * Generate frontend tests
     */
    private generateFrontendTests;
    /**
     * Generate integration tests
     */
    private generateIntegrationTests;
    /**
     * Generate E2E tests
     */
    private generateE2ETests;
    /**
     * Generate test configuration files
     */
    private generateTestConfiguration;
    /**
     * Calculate expected test coverage
     */
    private calculateExpectedCoverage;
    /**
     * Generate testing recommendations
     */
    private generateTestingRecommendations;
    /**
     * Write test files to disk
     */
    private writeTestFiles;
    private generateServiceUnitTests;
    private generateBusinessRuleTests;
    private generateAPIEndpointTests;
    private generateAPIContractTests;
    private generateComponentUnitTests;
    private generateHookTests;
    private generateDatabaseIntegrationTests;
    private generateDatabaseServiceIntegrationTest;
    private generateAPIDatabaseIntegrationTest;
    private generateUserJourneyTests;
    private generateCriticalPathTests;
    private generateDatabaseTestSetup;
    private generateDatabaseTestTeardown;
    private generateIntegrationTestSetup;
    private generateIntegrationTestTeardown;
    private generateE2ETestSetup;
    private generateE2ETestTeardown;
    private generateJestConfig;
    private generatePlaywrightConfig;
    private initializeTestTemplates;
    private toPascalCase;
}
//# sourceMappingURL=ComprehensiveTestingFramework.d.ts.map
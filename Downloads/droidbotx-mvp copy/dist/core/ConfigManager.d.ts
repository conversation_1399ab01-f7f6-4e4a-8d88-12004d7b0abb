export interface DroidBotXConfig {
    openRouter: {
        apiKey: string;
        baseUrl: string;
        defaultModel: string;
    };
    logging: {
        level: string;
        file: string;
    };
    application: {
        nodeEnv: string;
        port: number;
    };
}
export declare class ConfigManager {
    private static instance;
    private config;
    private constructor();
    static getInstance(): ConfigManager;
    private loadConfig;
    getConfig(): DroidBotXConfig;
    get openRouter(): DroidBotXConfig['openRouter'];
    get logging(): DroidBotXConfig['logging'];
    get application(): DroidBotXConfig['application'];
}
//# sourceMappingURL=ConfigManager.d.ts.map
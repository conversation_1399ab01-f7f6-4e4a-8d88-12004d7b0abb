/**
 * Contract-First API Development System
 * Generates OpenAPI specifications from business logic and ensures type safety across all layers
 */
interface BusinessLogicData {
    projectName?: string;
    description?: string;
    domainAPIs?: Record<string, any>;
    databaseSchema?: {
        tables?: Record<string, any>;
        [key: string]: any;
    };
}
export interface OpenAPISpec {
    openapi: string;
    info: {
        title: string;
        version: string;
        description: string;
    };
    servers: Array<{
        url: string;
        description: string;
    }>;
    paths: Record<string, any>;
    components: {
        schemas: Record<string, any>;
        securitySchemes?: Record<string, any>;
    };
}
export interface TypeScriptInterface {
    name: string;
    properties: Record<string, {
        type: string;
        required: boolean;
        description?: string;
    }>;
    imports?: string[];
}
export interface APIClientCode {
    typescript: string;
    react: string;
    hooks: string;
}
export declare class ContractFirstAPIGenerator {
    private logger;
    constructor();
    /**
     * Generate OpenAPI specification from business logic
     */
    generateOpenAPISpec(businessLogic: BusinessLogicData): Promise<OpenAPISpec>;
    /**
     * Generate TypeScript interfaces from OpenAPI spec
     */
    generateTypeScriptInterfaces(spec: OpenAPISpec): TypeScriptInterface[];
    /**
     * Generate typed API client code
     */
    generateAPIClient(spec: OpenAPISpec): APIClientCode;
    private generateSchemaFromTable;
    private generateCreateRequestSchema;
    private generateUpdateRequestSchema;
    private generateCRUDPaths;
    private convertSchemaToInterface;
    private generateTypeScriptClient;
    private generateReactClient;
    private generateReactHooks;
    private mapDatabaseTypeToOpenAPI;
    private mapOpenAPITypeToTypeScript;
    private toPascalCase;
}
export {};
//# sourceMappingURL=ContractFirstAPIGenerator.d.ts.map
/**
 * Enhanced Cross-Layer Integration Framework
 * Improves coordination between DatabaseAgent, BusinessLogicAgent, and CodingAgent
 * Ensures seamless data flow and type consistency across all layers
 */
export interface LayerContext {
    layer: 'database' | 'business' | 'api' | 'frontend';
    data: any;
    metadata: {
        timestamp: Date;
        version: string;
        dependencies: string[];
        contracts: Record<string, any>;
    };
}
export interface IntegrationContract {
    id: string;
    from: string;
    to: string;
    dataSchema: any;
    validationRules: string[];
    transformationRules?: string[];
    versionCompatibility: string[];
}
export interface CrossLayerValidation {
    isValid: boolean;
    errors: Array<{
        layer: string;
        type: 'type_mismatch' | 'missing_dependency' | 'contract_violation' | 'data_inconsistency';
        message: string;
        severity: 'error' | 'warning' | 'info';
        suggestion?: string;
    }>;
    warnings: string[];
    recommendations: string[];
}
export interface IntegrationResult {
    success: boolean;
    layersProcessed: string[];
    contractsValidated: number;
    dataTransformations: Array<{
        from: string;
        to: string;
        transformationType: string;
        success: boolean;
    }>;
    validation: CrossLayerValidation;
    synchronizationResults: Record<string, any>;
}
export declare class CrossLayerIntegrationFramework {
    private logger;
    private layerContexts;
    private integrationContracts;
    private validationRules;
    constructor();
    /**
     * Register a layer context for integration
     */
    registerLayerContext(context: LayerContext): void;
    /**
     * Create integration contract between layers
     */
    createIntegrationContract(contract: IntegrationContract): void;
    /**
     * Perform comprehensive cross-layer integration
     */
    performIntegration(): Promise<IntegrationResult>;
    /**
     * Validate all registered layer contexts
     */
    private validateLayerContexts;
    /**
     * Validate integration contracts
     */
    private validateIntegrationContracts;
    /**
     * Perform data transformations between layers
     */
    private performDataTransformations;
    /**
     * Synchronize schemas across all layers
     */
    private synchronizeSchemas;
    /**
     * Generate integration recommendations
     */
    private generateIntegrationRecommendations;
    /**
     * Validate schema compatibility between layers
     */
    private validateSchemaCompatibility;
    /**
     * Transform database context to business logic context
     */
    private transformDatabaseToBusiness;
    /**
     * Transform business logic context to API context
     */
    private transformBusinessToAPI;
    /**
     * Transform API context to frontend context
     */
    private transformAPIToFrontend;
    /**
     * Initialize validation rules for different layers
     */
    private initializeValidationRules;
}
//# sourceMappingURL=CrossLayerIntegrationFramework.d.ts.map
{"version": 3, "file": "CrossLayerIntegrationFramework.d.ts", "sourceRoot": "", "sources": ["../../src/core/CrossLayerIntegrationFramework.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAMH,MAAM,WAAW,YAAY;IAC3B,KAAK,EAAE,UAAU,GAAG,UAAU,GAAG,KAAK,GAAG,UAAU,CAAC;IACpD,IAAI,EAAE,GAAG,CAAC;IACV,QAAQ,EAAE;QACR,SAAS,EAAE,IAAI,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;QAChB,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAChC,CAAC;CACH;AAED,MAAM,WAAW,mBAAmB;IAClC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,MAAM,CAAC;IACX,UAAU,EAAE,GAAG,CAAC;IAChB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC/B,oBAAoB,EAAE,MAAM,EAAE,CAAC;CAChC;AAED,MAAM,WAAW,oBAAoB;IACnC,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,EAAE,KAAK,CAAC;QACZ,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,eAAe,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,CAAC;QAC3F,OAAO,EAAE,MAAM,CAAC;QAChB,QAAQ,EAAE,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC;QACvC,UAAU,CAAC,EAAE,MAAM,CAAC;KACrB,CAAC,CAAC;IACH,QAAQ,EAAE,MAAM,EAAE,CAAC;IACnB,eAAe,EAAE,MAAM,EAAE,CAAC;CAC3B;AAED,MAAM,WAAW,iBAAiB;IAChC,OAAO,EAAE,OAAO,CAAC;IACjB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,kBAAkB,EAAE,MAAM,CAAC;IAC3B,mBAAmB,EAAE,KAAK,CAAC;QACzB,IAAI,EAAE,MAAM,CAAC;QACb,EAAE,EAAE,MAAM,CAAC;QACX,kBAAkB,EAAE,MAAM,CAAC;QAC3B,OAAO,EAAE,OAAO,CAAC;KAClB,CAAC,CAAC;IACH,UAAU,EAAE,oBAAoB,CAAC;IACjC,sBAAsB,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC7C;AAED,qBAAa,8BAA8B;IACzC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,aAAa,CAA4B;IACjD,OAAO,CAAC,oBAAoB,CAAmC;IAC/D,OAAO,CAAC,eAAe,CAAwB;;IAU/C;;OAEG;IACH,oBAAoB,CAAC,OAAO,EAAE,YAAY,GAAG,IAAI;IASjD;;OAEG;IACH,yBAAyB,CAAC,QAAQ,EAAE,mBAAmB,GAAG,IAAI;IAQ9D;;OAEG;IACG,kBAAkB,IAAI,OAAO,CAAC,iBAAiB,CAAC;IA8DtD;;OAEG;YACW,qBAAqB;IAiCnC;;OAEG;YACW,4BAA4B;IAoD1C;;OAEG;YACW,0BAA0B;IAsCxC;;OAEG;YACW,kBAAkB;IAoChC;;OAEG;YACW,kCAAkC;IAwBhD;;OAEG;IACH,OAAO,CAAC,2BAA2B;IAgBnC;;OAEG;YACW,2BAA2B;IAYzC;;OAEG;YACW,sBAAsB;IAYpC;;OAEG;YACW,sBAAsB;IAYpC;;OAEG;IACH,OAAO,CAAC,yBAAyB;CAyDlC"}
{"version": 3, "file": "CrossLayerIntegrationFramework.js", "sourceRoot": "", "sources": ["../../src/core/CrossLayerIntegrationFramework.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,qCAAkC;AAoDlC,MAAa,8BAA8B;IAMzC;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;QACtC,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,OAAqB;QACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,OAAO,CAAC,KAAK,EAAE,EAAE;YAC9D,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM;YAClD,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM;SAC1D,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,QAA6B;QACrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,QAAQ,CAAC,IAAI,OAAO,QAAQ,CAAC,EAAE,EAAE,EAAE;YACpF,UAAU,EAAE,QAAQ,CAAC,EAAE;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;YACnD,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACnC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,MAAM,GAAsB;gBAChC,OAAO,EAAE,IAAI;gBACb,eAAe,EAAE,EAAE;gBACnB,kBAAkB,EAAE,CAAC;gBACrB,mBAAmB,EAAE,EAAE;gBACvB,UAAU,EAAE;oBACV,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,EAAE;oBACZ,eAAe,EAAE,EAAE;iBACpB;gBACD,sBAAsB,EAAE,EAAE;aAC3B,CAAC;YAEF,sCAAsC;YACtC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7D,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC3D,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAE/D,yCAAyC;YACzC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACrE,MAAM,CAAC,kBAAkB,GAAG,kBAAkB,CAAC,cAAc,CAAC;YAC9D,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE5D,uCAAuC;YACvC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAChE,MAAM,CAAC,mBAAmB,GAAG,eAAe,CAAC;YAE7C,4CAA4C;YAC5C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxD,MAAM,CAAC,sBAAsB,GAAG,eAAe,CAAC;YAEhD,+CAA+C;YAC/C,MAAM,CAAC,UAAU,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAEpF,4BAA4B;YAC5B,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;YAC3F,MAAM,CAAC,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAC3C,MAAM,CAAC,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBACpD,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,WAAW,EAAE,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM;gBAC5C,aAAa,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM;aACjD,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B,CAAC;YAC1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QAIjC,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACtD,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC5C,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;wBACxB,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;4BAChD,GAAG,CAAC;4BACJ,KAAK,EAAE,SAAS;yBACjB,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC;oBACD,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;gBAC9C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC;wBACV,KAAK,EAAE,SAAS;wBAChB,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE,6BAA6B,KAAK,EAAE;wBAC7C,QAAQ,EAAE,OAAO;qBAClB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,4BAA4B;QAIxC,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,KAAK,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC/D,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAEtD,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC/B,MAAM,CAAC,IAAI,CAAC;wBACV,KAAK,EAAE,aAAa;wBACpB,IAAI,EAAE,oBAAoB;wBAC1B,OAAO,EAAE,YAAY,UAAU,oCAAoC;wBACnE,QAAQ,EAAE,OAAO;qBAClB,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBAED,qCAAqC;gBACrC,MAAM,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CACvD,WAAW,CAAC,IAAI,EAChB,SAAS,CAAC,IAAI,EACd,QAAQ,CAAC,UAAU,CACpB,CAAC;gBAEF,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;oBAC9B,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC/C,GAAG,CAAC;wBACJ,KAAK,EAAE,aAAa;wBACpB,UAAU;qBACX,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC;gBAED,cAAc,EAAE,CAAC;YAEnB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC;oBACV,KAAK,EAAE,aAAa;oBACpB,IAAI,EAAE,oBAAoB;oBAC1B,OAAO,EAAE,kCAAkC,UAAU,KAAK,KAAK,EAAE;oBACjE,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B;QAMtC,MAAM,eAAe,GAAU,EAAE,CAAC;QAElC,4CAA4C;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACrD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE3D,IAAI,SAAS,IAAI,eAAe,EAAE,CAAC;YACjC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YAC1F,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvC,CAAC;QAED,uCAAuC;QACvC,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACjD,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;gBACtF,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE3D,IAAI,UAAU,IAAI,eAAe,EAAE,CAAC;YAClC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;YACtF,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,MAAM,sBAAsB,GAAwB,EAAE,CAAC;QAEvD,yDAAyD;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACvC,sBAAsB,CAAC,QAAQ,GAAG;gBAChC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,MAAM;gBACvE,mBAAmB,EAAE,IAAI;gBACzB,UAAU,EAAE,UAAU;aACvB,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC9C,sBAAsB,CAAC,GAAG,GAAG;gBAC3B,kBAAkB,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM;gBAC/E,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,QAAQ;aAC7B,CAAC;QACJ,CAAC;QAED,6BAA6B;QAC7B,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,eAAe,EAAE,CAAC;YACpB,sBAAsB,CAAC,QAAQ,GAAG;gBAChC,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM;gBAC9E,eAAe,EAAE,UAAU;gBAC3B,kBAAkB,EAAE,IAAI;aACzB,CAAC;QACJ,CAAC;QAED,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kCAAkC;QAC9C,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,mCAAmC;QACnC,MAAM,cAAc,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;QACnE,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAErF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC,yCAAyC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5F,CAAC;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACvC,eAAe,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;QAC9F,CAAC;QAED,8BAA8B;QAC9B,eAAe,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACzE,eAAe,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QACpF,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAE9E,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,2BAA2B,CACjC,QAAa,EACb,MAAW,EACX,cAAmB;QAEnB,MAAM,MAAM,GAAU,EAAE,CAAC;QAEzB,gCAAgC;QAChC,4DAA4D;QAE5D,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B,CACvC,SAAuB,EACvB,eAA6B;QAE7B,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,EAAE,EAAE,UAAU;YACd,kBAAkB,EAAE,kBAAkB;YACtC,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,eAA6B,EAC7B,UAAwB;QAExB,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,EAAE,EAAE,KAAK;YACT,kBAAkB,EAAE,qBAAqB;YACzC,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,UAAwB,EACxB,eAA6B;QAE7B,OAAO;YACL,IAAI,EAAE,KAAK;YACX,EAAE,EAAE,UAAU;YACd,kBAAkB,EAAE,qBAAqB;YACzC,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,OAAqB,EAAE,EAAE;YACnE,MAAM,MAAM,GAAU,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,qCAAqC;YACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,oBAAoB;oBAC1B,OAAO,EAAE,uCAAuC;oBAChD,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,OAAqB,EAAE,EAAE;YACnE,MAAM,MAAM,GAAU,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,oCAAoC;YACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC7B,QAAQ,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YACnE,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAqB,EAAE,EAAE;YAC9D,MAAM,MAAM,GAAU,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,6BAA6B;YAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC9B,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,oBAAoB;oBAC1B,OAAO,EAAE,kCAAkC;oBAC3C,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,OAAqB,EAAE,EAAE;YACnE,MAAM,MAAM,GAAU,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,8BAA8B;YAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC7B,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAxaD,wEAwaC"}
/**
 * Database-Code Synchronization System
 * Ensures perfect alignment between database schemas and TypeScript interfaces
 * Provides real-time synchronization and automatic code generation
 */
export interface DatabaseColumn {
    name: string;
    type: string;
    nullable: boolean;
    primaryKey?: boolean;
    autoIncrement?: boolean;
    foreignKey?: {
        table: string;
        column: string;
    };
    defaultValue?: any;
    comment?: string;
}
export interface DatabaseTable {
    name: string;
    columns: Record<string, DatabaseColumn>;
    indexes?: string[];
    constraints?: string[];
}
export interface DatabaseSchema {
    tables: Record<string, DatabaseTable>;
    relationships: Array<{
        from: {
            table: string;
            column: string;
        };
        to: {
            table: string;
            column: string;
        };
        type: 'oneToOne' | 'oneToMany' | 'manyToOne' | 'manyToMany';
    }>;
}
export interface TypeScriptInterface {
    name: string;
    properties: Record<string, {
        type: string;
        optional: boolean;
        description?: string;
        validation?: string[];
    }>;
    imports: string[];
    relationships?: Array<{
        property: string;
        type: 'oneToOne' | 'oneToMany' | 'manyToOne' | 'manyToMany';
        target: string;
    }>;
}
export interface SynchronizationResult {
    interfaces: TypeScriptInterface[];
    apiTypes: string[];
    validationSchemas: string[];
    migrationScripts: string[];
    changesDetected: boolean;
    summary: {
        tablesProcessed: number;
        interfacesGenerated: number;
        relationshipsResolved: number;
    };
}
export declare class DatabaseCodeSynchronizer {
    private logger;
    private typeMapping;
    private validationMapping;
    constructor();
    /**
     * Synchronize database schema with TypeScript interfaces
     */
    synchronizeSchemaToCode(schema: DatabaseSchema, outputPath: string, options?: {
        generateValidation?: boolean;
        generateAPITypes?: boolean;
        generateMigrations?: boolean;
        strict?: boolean;
    }): Promise<SynchronizationResult>;
    /**
     * Generate TypeScript interface from database table
     */
    private generateTypeScriptInterface;
    /**
     * Generate API types (Create, Update, Response) for a table
     */
    private generateAPITypes;
    /**
     * Generate validation schema for runtime validation
     */
    private generateValidationSchema;
    /**
     * Generate validation rules for a database column
     */
    private generateValidationRules;
    /**
     * Resolve relationships between interfaces
     */
    private resolveRelationships;
    /**
     * Find relationships for a specific table
     */
    private findTableRelationships;
    /**
     * Write generated code to files
     */
    private writeGeneratedCode;
    /**
     * Generate complete interfaces file
     */
    private generateInterfacesFile;
    /**
     * Detect if changes were made compared to existing files
     */
    private detectChanges;
    /**
     * Generate migration scripts for schema changes
     */
    private generateMigrationScripts;
    /**
     * Initialize type mappings
     */
    private initializeTypeMappings;
    /**
     * Map database type to TypeScript type
     */
    private mapDatabaseTypeToTypeScript;
    /**
     * Convert string to PascalCase
     */
    private toPascalCase;
    /**
     * Convert string to camelCase
     */
    private toCamelCase;
}
//# sourceMappingURL=DatabaseCodeSynchronizer.d.ts.map
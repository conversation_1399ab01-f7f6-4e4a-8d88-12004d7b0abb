"use strict";
/**
 * Database-Code Synchronization System
 * Ensures perfect alignment between database schemas and TypeScript interfaces
 * Provides real-time synchronization and automatic code generation
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseCodeSynchronizer = void 0;
const Logger_1 = require("./Logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class DatabaseCodeSynchronizer {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.typeMapping = {};
        this.validationMapping = {};
        this.initializeTypeMappings();
    }
    /**
     * Synchronize database schema with TypeScript interfaces
     */
    async synchronizeSchemaToCode(schema, outputPath, options = {}) {
        this.logger.info('Starting database-code synchronization', {
            tableCount: Object.keys(schema.tables).length,
            outputPath
        });
        const result = {
            interfaces: [],
            apiTypes: [],
            validationSchemas: [],
            migrationScripts: [],
            changesDetected: false,
            summary: {
                tablesProcessed: 0,
                interfacesGenerated: 0,
                relationshipsResolved: 0
            }
        };
        try {
            // Generate TypeScript interfaces for each table
            for (const [tableName, table] of Object.entries(schema.tables)) {
                const tsInterface = this.generateTypeScriptInterface(table, schema);
                result.interfaces.push(tsInterface);
                result.summary.tablesProcessed++;
                result.summary.interfacesGenerated++;
                // Generate API types if requested
                if (options.generateAPITypes) {
                    const apiTypes = this.generateAPITypes(table, tsInterface);
                    result.apiTypes.push(...apiTypes);
                }
                // Generate validation schemas if requested
                if (options.generateValidation) {
                    const validationSchema = this.generateValidationSchema(table, tsInterface);
                    result.validationSchemas.push(validationSchema);
                }
            }
            // Resolve relationships
            result.summary.relationshipsResolved = this.resolveRelationships(result.interfaces, schema.relationships);
            // Generate migration scripts if requested
            if (options.generateMigrations) {
                result.migrationScripts = await this.generateMigrationScripts(schema, outputPath);
            }
            // Write generated code to files
            await this.writeGeneratedCode(result, outputPath);
            // Check for changes
            result.changesDetected = await this.detectChanges(result, outputPath);
            this.logger.info('Database-code synchronization completed', {
                ...result.summary,
                changesDetected: result.changesDetected
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown synchronization error';
            this.logger.error('Database-code synchronization failed', { error: errorMessage });
            throw error;
        }
    }
    /**
     * Generate TypeScript interface from database table
     */
    generateTypeScriptInterface(table, schema) {
        const interfaceName = this.toPascalCase(table.name);
        const properties = {};
        const imports = [];
        // Process each column
        for (const [columnName, column] of Object.entries(table.columns)) {
            const tsType = this.mapDatabaseTypeToTypeScript(column.type);
            const isOptional = column.nullable || column.autoIncrement;
            properties[columnName] = {
                type: tsType,
                optional: isOptional,
                description: column.comment || `${columnName} field`,
                validation: this.generateValidationRules(column)
            };
            // Add imports for complex types
            if (tsType.includes('Date')) {
                if (!imports.includes('Date'))
                    imports.push('Date');
            }
        }
        // Resolve relationships
        const relationships = this.findTableRelationships(table.name, schema.relationships);
        return {
            name: interfaceName,
            properties,
            imports,
            relationships
        };
    }
    /**
     * Generate API types (Create, Update, Response) for a table
     */
    generateAPITypes(table, tsInterface) {
        const baseName = tsInterface.name;
        const apiTypes = [];
        // Create Request Type (excludes auto-generated fields)
        const createProperties = Object.entries(tsInterface.properties)
            .filter(([key, prop]) => {
            const column = table.columns[key];
            return !(column?.autoIncrement || key === 'created_at' || key === 'updated_at');
        })
            .map(([key, prop]) => `  ${key}${prop.optional ? '?' : ''}: ${prop.type};`)
            .join('\n');
        apiTypes.push(`export interface Create${baseName}Request {
${createProperties}
}`);
        // Update Request Type (all fields optional except ID)
        const updateProperties = Object.entries(tsInterface.properties)
            .filter(([key]) => {
            const column = table.columns[key];
            return !(column?.autoIncrement || key === 'created_at' || key === 'updated_at');
        })
            .map(([key, prop]) => `  ${key}?: ${prop.type};`)
            .join('\n');
        apiTypes.push(`export interface Update${baseName}Request {
${updateProperties}
}`);
        // Response Type (includes metadata)
        apiTypes.push(`export interface ${baseName}Response extends ${baseName} {
  _metadata?: {
    created_at: Date;
    updated_at: Date;
    version?: number;
  };
}`);
        return apiTypes;
    }
    /**
     * Generate validation schema for runtime validation
     */
    generateValidationSchema(table, tsInterface) {
        const schemaName = `${tsInterface.name}Schema`;
        const validations = [];
        for (const [columnName, column] of Object.entries(table.columns)) {
            const rules = this.generateValidationRules(column);
            if (rules.length > 0) {
                validations.push(`  ${columnName}: ${rules.join(' && ')}`);
            }
        }
        return `export const ${schemaName} = {
${validations.join(',\n')}
};`;
    }
    /**
     * Generate validation rules for a database column
     */
    generateValidationRules(column) {
        const rules = [];
        if (!column.nullable) {
            rules.push('required()');
        }
        // Type-specific validations
        switch (column.type.toLowerCase()) {
            case 'varchar':
            case 'text':
                rules.push('string()');
                break;
            case 'integer':
            case 'int':
            case 'bigint':
                rules.push('number().integer()');
                break;
            case 'decimal':
            case 'float':
                rules.push('number()');
                break;
            case 'boolean':
                rules.push('boolean()');
                break;
            case 'timestamp':
            case 'date':
                rules.push('date()');
                break;
            case 'json':
                rules.push('object()');
                break;
        }
        return rules;
    }
    /**
     * Resolve relationships between interfaces
     */
    resolveRelationships(interfaces, relationships) {
        let resolvedCount = 0;
        for (const relationship of relationships) {
            const fromInterface = interfaces.find(i => i.name === this.toPascalCase(relationship.from.table));
            const toInterface = interfaces.find(i => i.name === this.toPascalCase(relationship.to.table));
            if (fromInterface && toInterface) {
                if (!fromInterface.relationships)
                    fromInterface.relationships = [];
                fromInterface.relationships.push({
                    property: this.toCamelCase(relationship.to.table),
                    type: relationship.type,
                    target: toInterface.name
                });
                resolvedCount++;
            }
        }
        return resolvedCount;
    }
    /**
     * Find relationships for a specific table
     */
    findTableRelationships(tableName, relationships) {
        return relationships
            .filter(rel => rel.from.table === tableName)
            .map(rel => ({
            property: this.toCamelCase(rel.to.table),
            type: rel.type,
            target: this.toPascalCase(rel.to.table)
        }));
    }
    /**
     * Write generated code to files
     */
    async writeGeneratedCode(result, outputPath) {
        // Ensure output directory exists
        await fs.promises.mkdir(outputPath, { recursive: true });
        // Write interfaces
        const interfacesContent = this.generateInterfacesFile(result.interfaces);
        await fs.promises.writeFile(path.join(outputPath, 'interfaces.ts'), interfacesContent);
        // Write API types
        if (result.apiTypes.length > 0) {
            const apiTypesContent = result.apiTypes.join('\n\n');
            await fs.promises.writeFile(path.join(outputPath, 'api-types.ts'), apiTypesContent);
        }
        // Write validation schemas
        if (result.validationSchemas.length > 0) {
            const validationContent = result.validationSchemas.join('\n\n');
            await fs.promises.writeFile(path.join(outputPath, 'validation.ts'), validationContent);
        }
    }
    /**
     * Generate complete interfaces file
     */
    generateInterfacesFile(interfaces) {
        const imports = new Set();
        const interfaceDefinitions = [];
        for (const tsInterface of interfaces) {
            // Collect imports
            tsInterface.imports.forEach(imp => imports.add(imp));
            // Generate interface definition
            const properties = Object.entries(tsInterface.properties)
                .map(([key, prop]) => {
                const optional = prop.optional ? '?' : '';
                const comment = prop.description ? `  /** ${prop.description} */\n` : '';
                return `${comment}  ${key}${optional}: ${prop.type};`;
            })
                .join('\n');
            interfaceDefinitions.push(`export interface ${tsInterface.name} {
${properties}
}`);
        }
        const importStatements = Array.from(imports)
            .map(imp => `import { ${imp} } from './${imp.toLowerCase()}';`)
            .join('\n');
        return `${importStatements ? importStatements + '\n\n' : ''}${interfaceDefinitions.join('\n\n')}`;
    }
    /**
     * Detect if changes were made compared to existing files
     */
    async detectChanges(result, outputPath) {
        try {
            const interfacesPath = path.join(outputPath, 'interfaces.ts');
            if (await fs.promises.access(interfacesPath).then(() => true).catch(() => false)) {
                const existingContent = await fs.promises.readFile(interfacesPath, 'utf-8');
                const newContent = this.generateInterfacesFile(result.interfaces);
                return existingContent !== newContent;
            }
            return true; // New files are considered changes
        }
        catch {
            return true;
        }
    }
    /**
     * Generate migration scripts for schema changes
     */
    async generateMigrationScripts(schema, outputPath) {
        // This would compare with existing schema and generate migration scripts
        // For now, return empty array as this is a complex feature
        return [];
    }
    /**
     * Initialize type mappings
     */
    initializeTypeMappings() {
        this.typeMapping = {
            'varchar': 'string',
            'text': 'string',
            'char': 'string',
            'integer': 'number',
            'int': 'number',
            'bigint': 'number',
            'smallint': 'number',
            'decimal': 'number',
            'numeric': 'number',
            'float': 'number',
            'double': 'number',
            'boolean': 'boolean',
            'bool': 'boolean',
            'timestamp': 'Date',
            'datetime': 'Date',
            'date': 'Date',
            'time': 'string',
            'json': 'Record<string, any>',
            'jsonb': 'Record<string, any>',
            'uuid': 'string',
            'enum': 'string'
        };
        this.validationMapping = {
            'varchar': ['string()'],
            'text': ['string()'],
            'integer': ['number()', 'integer()'],
            'decimal': ['number()'],
            'boolean': ['boolean()'],
            'timestamp': ['date()'],
            'json': ['object()']
        };
    }
    /**
     * Map database type to TypeScript type
     */
    mapDatabaseTypeToTypeScript(dbType) {
        const normalizedType = dbType.toLowerCase().split('(')[0]; // Remove size constraints
        return this.typeMapping[normalizedType] || 'any';
    }
    /**
     * Convert string to PascalCase
     */
    toPascalCase(str) {
        return str.replace(/(^\w|_\w)/g, (match) => match.replace('_', '').toUpperCase());
    }
    /**
     * Convert string to camelCase
     */
    toCamelCase(str) {
        const pascal = this.toPascalCase(str);
        return pascal.charAt(0).toLowerCase() + pascal.slice(1);
    }
}
exports.DatabaseCodeSynchronizer = DatabaseCodeSynchronizer;
//# sourceMappingURL=DatabaseCodeSynchronizer.js.map
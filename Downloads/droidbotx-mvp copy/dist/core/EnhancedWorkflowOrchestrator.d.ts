export interface EnhancedWorkflowConfig {
    enableImportOptimization: boolean;
    enableTestQualityEnhancement: boolean;
    enableDatabaseIntegration: boolean;
    enableServiceStandardization: boolean;
    enableQualityGates: boolean;
    enablePerformanceOptimization: boolean;
    targetProductionScore: number;
    targetPhaseCompletion: number;
    targetTestSuccessRate: number;
}
export interface WorkflowPhaseResult {
    phase: string;
    success: boolean;
    duration: number;
    qualityScore: number;
    optimizations: string[];
    issues: string[];
    metrics: any;
}
export interface EnhancedWorkflowResult {
    success: boolean;
    overallDuration: number;
    phaseCompletionRate: number;
    productionReadinessScore: number;
    testSuccessRate: number;
    phaseResults: WorkflowPhaseResult[];
    optimizationReport: any;
    qualityReport: any;
    recommendations: string[];
}
/**
 * Enhanced Workflow Orchestrator for Phase 3
 * Integrates all optimization systems to achieve 8-9/10 phase completion and 90-95/100 production readiness
 */
export declare class EnhancedWorkflowOrchestrator {
    private logger;
    private importManager;
    private testFramework;
    private databaseIntegration;
    private serviceStandardization;
    private qualityGates;
    private performanceOptimization;
    private config;
    constructor(config?: Partial<EnhancedWorkflowConfig>);
    /**
     * Execute enhanced workflow with all Phase 3 optimizations
     */
    executeEnhancedWorkflow(description: string, requirements: string[], sessionId: string): Promise<EnhancedWorkflowResult>;
    /**
     * Execute phase with all applicable optimizations
     */
    private executePhaseWithOptimizations;
    /**
     * Apply phase-specific optimizations
     */
    private applyPhaseSpecificOptimizations;
    /**
     * Placeholder phase execution methods
     */
    private executePlanningPhase;
    private executeBusinessLogicPhase;
    private executeGeneratePhase;
    private executeDatabasePhase;
    private executeServiceStandardizationPhase;
    private executeUIUXPhase;
    private executeSecurityPhase;
    private executeTestingPhase;
    private executeDebuggingPhase;
    private executeDeploymentPhase;
    /**
     * Calculate phase completion rate
     */
    private calculatePhaseCompletionRate;
    /**
     * Calculate test success rate
     */
    private calculateTestSuccessRate;
    /**
     * Generate quality report
     */
    private generateQualityReport;
    /**
     * Generate recommendations
     */
    private generateRecommendations;
    private logInfo;
    private logError;
}
//# sourceMappingURL=EnhancedWorkflowOrchestrator.d.ts.map
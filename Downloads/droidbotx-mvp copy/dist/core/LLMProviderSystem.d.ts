export interface LLMMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}
export interface LLMResponse {
    content: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}
export interface LLMRequestOptions {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
}
export declare class LLMProviderSystem {
    private static instance;
    private httpClient;
    private config;
    private logger;
    private constructor();
    static getInstance(): LLMProviderSystem;
    generateResponse(messages: LLMMessage[], options?: LLMRequestOptions): Promise<LLMResponse>;
    generateSingleResponse(prompt: string, options?: LLMRequestOptions): Promise<string>;
}
//# sourceMappingURL=LLMProviderSystem.d.ts.map
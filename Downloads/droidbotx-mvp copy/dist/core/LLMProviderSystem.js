"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMProviderSystem = void 0;
const axios_1 = __importDefault(require("axios"));
const ConfigManager_1 = require("./ConfigManager");
const Logger_1 = require("./Logger");
class LLMProviderSystem {
    constructor() {
        this.config = ConfigManager_1.ConfigManager.getInstance();
        this.logger = Logger_1.Logger.getInstance();
        this.httpClient = axios_1.default.create({
            baseURL: this.config.openRouter.baseUrl,
            headers: {
                'Authorization': `Bearer ${this.config.openRouter.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://droidbotx.ai',
                'X-Title': 'DroidBotX MVP',
            },
            timeout: 60000, // 60 seconds timeout
        });
    }
    static getInstance() {
        if (!LLMProviderSystem.instance) {
            LLMProviderSystem.instance = new LLMProviderSystem();
        }
        return LLMProviderSystem.instance;
    }
    async generateResponse(messages, options = {}) {
        const { model = this.config.openRouter.defaultModel, temperature = 0.7, maxTokens = 4000, systemPrompt, } = options;
        try {
            // Add system prompt if provided
            const finalMessages = systemPrompt
                ? [{ role: 'system', content: systemPrompt }, ...messages]
                : messages;
            this.logger.debug('Sending LLM request', {
                model,
                messageCount: finalMessages.length,
                temperature,
                maxTokens,
            });
            const response = await this.httpClient.post('/chat/completions', {
                model,
                messages: finalMessages,
                temperature,
                max_tokens: maxTokens,
                stream: false,
            });
            const choice = response.data.choices?.[0];
            if (!choice) {
                throw new Error('No response choice received from LLM');
            }
            const result = {
                content: choice.message.content,
                usage: response.data.usage ? {
                    promptTokens: response.data.usage.prompt_tokens,
                    completionTokens: response.data.usage.completion_tokens,
                    totalTokens: response.data.usage.total_tokens,
                } : undefined,
            };
            this.logger.info('LLM response received', {
                model,
                contentLength: result.content.length,
                usage: result.usage,
            });
            return result;
        }
        catch (error) {
            this.logger.error('LLM request failed', {
                error: error instanceof Error ? error.message : 'Unknown error',
                model,
                messageCount: messages.length,
            });
            throw error;
        }
    }
    async generateSingleResponse(prompt, options = {}) {
        const messages = [
            { role: 'user', content: prompt }
        ];
        const response = await this.generateResponse(messages, options);
        return response.content;
    }
}
exports.LLMProviderSystem = LLMProviderSystem;
//# sourceMappingURL=LLMProviderSystem.js.map
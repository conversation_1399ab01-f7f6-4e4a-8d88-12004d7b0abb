{"version": 3, "file": "LLMProviderSystem.js", "sourceRoot": "", "sources": ["../../src/core/LLMProviderSystem.ts"], "names": [], "mappings": ";;;;;;AACA,kDAA6C;AAC7C,mDAAgD;AAChD,qCAAkC;AAuBlC,MAAa,iBAAiB;IAM5B;QACE,IAAI,CAAC,MAAM,GAAG,6BAAa,CAAC,WAAW,EAAE,CAAC;QAC1C,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,CAAC,UAAU,GAAG,eAAK,CAAC,MAAM,CAAC;YAC7B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO;YACvC,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE;gBAC1D,cAAc,EAAE,kBAAkB;gBAClC,cAAc,EAAE,sBAAsB;gBACtC,SAAS,EAAE,eAAe;aAC3B;YACD,OAAO,EAAE,KAAK,EAAE,qBAAqB;SACtC,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YAChC,iBAAiB,CAAC,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACvD,CAAC;QACD,OAAO,iBAAiB,CAAC,QAAQ,CAAC;IACpC,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,QAAsB,EACtB,UAA6B,EAAE;QAE/B,MAAM,EACJ,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,EAC3C,WAAW,GAAG,GAAG,EACjB,SAAS,GAAG,IAAI,EAChB,YAAY,GACb,GAAG,OAAO,CAAC;QAEZ,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,aAAa,GAAG,YAAY;gBAChC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,GAAG,QAAQ,CAAC;gBACnE,CAAC,CAAC,QAAQ,CAAC;YAEb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBACvC,KAAK;gBACL,YAAY,EAAE,aAAa,CAAC,MAAM;gBAClC,WAAW;gBACX,SAAS;aACV,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/D,KAAK;gBACL,QAAQ,EAAE,aAAa;gBACvB,WAAW;gBACX,UAAU,EAAE,SAAS;gBACrB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,MAAM,GAAgB;gBAC1B,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/B,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC3B,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;oBAC/C,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB;oBACvD,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY;iBAC9C,CAAC,CAAC,CAAC,SAAS;aACd,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACxC,KAAK;gBACL,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;gBACpC,KAAK,EAAE,MAAM,CAAC,KAAK;aACpB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACtC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,KAAK;gBACL,YAAY,EAAE,QAAQ,CAAC,MAAM;aAC9B,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,sBAAsB,CACjC,MAAc,EACd,UAA6B,EAAE;QAE/B,MAAM,QAAQ,GAAiB;YAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE;SAClC,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAChE,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC;CAKF;AA5GD,8CA4GC"}
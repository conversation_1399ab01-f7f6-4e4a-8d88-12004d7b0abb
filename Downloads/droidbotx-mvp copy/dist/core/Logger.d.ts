export declare enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3
}
export interface LogEntry {
    timestamp: string;
    level: string;
    message: string;
    context?: Record<string, any>;
}
export declare class Logger {
    private static instance;
    private logLevel;
    private logFile;
    private constructor();
    static getInstance(): Logger;
    private parseLogLevel;
    private ensureLogDirectory;
    private formatLogEntry;
    private writeLog;
    error(message: string, context?: Record<string, any>): void;
    warn(message: string, context?: Record<string, any>): void;
    info(message: string, context?: Record<string, any>): void;
    debug(message: string, context?: Record<string, any>): void;
}
//# sourceMappingURL=Logger.d.ts.map
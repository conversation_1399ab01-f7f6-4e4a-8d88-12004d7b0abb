"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = exports.LogLevel = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const ConfigManager_1 = require("./ConfigManager");
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor() {
        const config = ConfigManager_1.ConfigManager.getInstance();
        this.logLevel = this.parseLogLevel(config.logging.level);
        this.logFile = config.logging.file;
        this.ensureLogDirectory();
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    parseLogLevel(level) {
        switch (level.toLowerCase()) {
            case 'error': return LogLevel.ERROR;
            case 'warn': return LogLevel.WARN;
            case 'info': return LogLevel.INFO;
            case 'debug': return LogLevel.DEBUG;
            default: return LogLevel.INFO;
        }
    }
    ensureLogDirectory() {
        const logDir = path.dirname(this.logFile);
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
    }
    formatLogEntry(level, message, context) {
        return {
            timestamp: new Date().toISOString(),
            level,
            message,
            context,
        };
    }
    writeLog(entry) {
        const logLine = JSON.stringify(entry) + '\n';
        // Console output
        console.log(`[${entry.timestamp}] ${entry.level.toUpperCase()}: ${entry.message}`);
        if (entry.context) {
            console.log('Context:', entry.context);
        }
        // File output
        fs.appendFileSync(this.logFile, logLine);
    }
    error(message, context) {
        if (this.logLevel >= LogLevel.ERROR) {
            this.writeLog(this.formatLogEntry('error', message, context));
        }
    }
    warn(message, context) {
        if (this.logLevel >= LogLevel.WARN) {
            this.writeLog(this.formatLogEntry('warn', message, context));
        }
    }
    info(message, context) {
        if (this.logLevel >= LogLevel.INFO) {
            this.writeLog(this.formatLogEntry('info', message, context));
        }
    }
    debug(message, context) {
        if (this.logLevel >= LogLevel.DEBUG) {
            this.writeLog(this.formatLogEntry('debug', message, context));
        }
    }
}
exports.Logger = Logger;
//# sourceMappingURL=Logger.js.map
/**
 * Monitoring and Observability System
 * Comprehensive logging, metrics collection, health checks, and performance monitoring
 * Ensures production applications can be properly monitored and debugged
 */
export interface MonitoringConfiguration {
    projectPath: string;
    environment: 'development' | 'staging' | 'production';
    metrics: {
        enabled: boolean;
        provider: 'prometheus' | 'datadog' | 'newrelic';
        customMetrics: string[];
    };
    logging: {
        level: 'debug' | 'info' | 'warn' | 'error';
        structured: boolean;
        destinations: Array<'console' | 'file' | 'elasticsearch' | 'cloudwatch'>;
    };
    healthChecks: {
        enabled: boolean;
        endpoints: string[];
        interval: number;
    };
    tracing: {
        enabled: boolean;
        provider: 'jaeger' | 'zipkin' | 'datadog';
        sampleRate: number;
    };
}
export interface MonitoringArtifact {
    type: 'logger' | 'metrics' | 'health-check' | 'middleware' | 'config';
    filePath: string;
    content: string;
    dependencies: string[];
}
export interface ObservabilityResult {
    artifacts: MonitoringArtifact[];
    configuration: {
        dockerCompose: string;
        prometheusConfig: string;
        grafanaDashboards: string[];
        alertingRules: string[];
    };
    healthChecks: Array<{
        name: string;
        endpoint: string;
        checks: string[];
    }>;
    metrics: Array<{
        name: string;
        type: 'counter' | 'gauge' | 'histogram' | 'summary';
        description: string;
    }>;
    recommendations: string[];
}
export declare class MonitoringObservabilitySystem {
    private logger;
    private monitoringTemplates;
    constructor();
    /**
     * Generate comprehensive monitoring and observability setup
     */
    generateMonitoringSetup(config: MonitoringConfiguration): Promise<ObservabilityResult>;
    /**
     * Generate structured logging system
     */
    private generateLoggingSystem;
    /**
     * Generate metrics collection system
     */
    private generateMetricsSystem;
    /**
     * Generate health check system
     */
    private generateHealthCheckSystem;
    /**
     * Generate performance monitoring
     */
    private generatePerformanceMonitoring;
    /**
     * Generate distributed tracing
     */
    private generateDistributedTracing;
    /**
     * Generate monitoring middleware
     */
    private generateMonitoringMiddleware;
    /**
     * Generate monitoring configuration files
     */
    private generateMonitoringConfiguration;
    /**
     * Generate health check definitions
     */
    private generateHealthCheckDefinitions;
    /**
     * Generate metrics definitions
     */
    private generateMetricsDefinitions;
    /**
     * Generate monitoring recommendations
     */
    private generateMonitoringRecommendations;
    /**
     * Write monitoring files to disk
     */
    private writeMonitoringFiles;
    private generateStructuredLoggerCode;
    private generateMetricsCollectorCode;
    private generateHealthCheckManagerCode;
    private generateLogCorrelationMiddleware;
    private generateRequestLoggingMiddleware;
    private generateBusinessMetricsCode;
    private generatePerformanceMetricsMiddleware;
    private generateDatabaseHealthCheckCode;
    private generateExternalServicesHealthCheckCode;
    private generatePerformanceMonitorCode;
    private generateDatabaseQueryMonitorCode;
    private generateTracingSetupCode;
    private generateTracingMiddleware;
    private generateErrorTrackingMiddleware;
    private generateSecurityMonitoringMiddleware;
    private generateDockerComposeForMonitoring;
    private generatePrometheusConfig;
    private generateApplicationDashboard;
    private generateInfrastructureDashboard;
    private generateBusinessMetricsDashboard;
    private generateAlertingRules;
    private initializeTemplates;
}
//# sourceMappingURL=MonitoringObservabilitySystem.d.ts.map
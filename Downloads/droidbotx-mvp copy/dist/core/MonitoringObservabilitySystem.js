"use strict";
/**
 * Monitoring and Observability System
 * Comprehensive logging, metrics collection, health checks, and performance monitoring
 * Ensures production applications can be properly monitored and debugged
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringObservabilitySystem = void 0;
const Logger_1 = require("./Logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class MonitoringObservabilitySystem {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
        this.monitoringTemplates = new Map();
        this.initializeTemplates();
    }
    /**
     * Generate comprehensive monitoring and observability setup
     */
    async generateMonitoringSetup(config) {
        this.logger.info('Starting monitoring and observability setup generation', {
            environment: config.environment,
            projectPath: config.projectPath
        });
        try {
            const artifacts = [];
            // Generate structured logging system
            const loggingArtifacts = await this.generateLoggingSystem(config);
            artifacts.push(...loggingArtifacts);
            // Generate metrics collection
            const metricsArtifacts = await this.generateMetricsSystem(config);
            artifacts.push(...metricsArtifacts);
            // Generate health check system
            const healthCheckArtifacts = await this.generateHealthCheckSystem(config);
            artifacts.push(...healthCheckArtifacts);
            // Generate performance monitoring
            const performanceArtifacts = await this.generatePerformanceMonitoring(config);
            artifacts.push(...performanceArtifacts);
            // Generate distributed tracing
            const tracingArtifacts = await this.generateDistributedTracing(config);
            artifacts.push(...tracingArtifacts);
            // Generate monitoring middleware
            const middlewareArtifacts = await this.generateMonitoringMiddleware(config);
            artifacts.push(...middlewareArtifacts);
            // Generate configuration files
            const configurationFiles = await this.generateMonitoringConfiguration(config);
            // Generate health check definitions
            const healthChecks = this.generateHealthCheckDefinitions(config);
            // Generate metrics definitions
            const metrics = this.generateMetricsDefinitions(config);
            // Generate recommendations
            const recommendations = this.generateMonitoringRecommendations(config);
            const result = {
                artifacts,
                configuration: configurationFiles,
                healthChecks,
                metrics,
                recommendations
            };
            // Write monitoring files to disk
            await this.writeMonitoringFiles(result, config.projectPath);
            this.logger.info('Monitoring and observability setup completed', {
                artifactsGenerated: artifacts.length,
                healthChecksCreated: healthChecks.length,
                metricsCreated: metrics.length
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown monitoring setup error';
            this.logger.error('Monitoring and observability setup failed', { error: errorMessage });
            throw error;
        }
    }
    /**
     * Generate structured logging system
     */
    async generateLoggingSystem(config) {
        const artifacts = [];
        // Enhanced logger with structured logging
        artifacts.push({
            type: 'logger',
            filePath: 'src/monitoring/StructuredLogger.ts',
            content: this.generateStructuredLoggerCode(config),
            dependencies: ['winston', 'winston-elasticsearch', '@types/winston']
        });
        // Log correlation middleware
        artifacts.push({
            type: 'middleware',
            filePath: 'src/monitoring/LogCorrelationMiddleware.ts',
            content: this.generateLogCorrelationMiddleware(config),
            dependencies: ['express', 'uuid', '@types/express']
        });
        // Request logging middleware
        artifacts.push({
            type: 'middleware',
            filePath: 'src/monitoring/RequestLoggingMiddleware.ts',
            content: this.generateRequestLoggingMiddleware(config),
            dependencies: ['express', 'morgan', '@types/express']
        });
        return artifacts;
    }
    /**
     * Generate metrics collection system
     */
    async generateMetricsSystem(config) {
        const artifacts = [];
        // Metrics collector
        artifacts.push({
            type: 'metrics',
            filePath: 'src/monitoring/MetricsCollector.ts',
            content: this.generateMetricsCollectorCode(config),
            dependencies: ['prom-client', '@types/prom-client']
        });
        // Business metrics
        artifacts.push({
            type: 'metrics',
            filePath: 'src/monitoring/BusinessMetrics.ts',
            content: this.generateBusinessMetricsCode(config),
            dependencies: ['prom-client']
        });
        // Performance metrics middleware
        artifacts.push({
            type: 'middleware',
            filePath: 'src/monitoring/PerformanceMetricsMiddleware.ts',
            content: this.generatePerformanceMetricsMiddleware(config),
            dependencies: ['express', 'prom-client', '@types/express']
        });
        return artifacts;
    }
    /**
     * Generate health check system
     */
    async generateHealthCheckSystem(config) {
        const artifacts = [];
        // Health check manager
        artifacts.push({
            type: 'health-check',
            filePath: 'src/monitoring/HealthCheckManager.ts',
            content: this.generateHealthCheckManagerCode(config),
            dependencies: ['express', '@types/express']
        });
        // Database health check
        artifacts.push({
            type: 'health-check',
            filePath: 'src/monitoring/DatabaseHealthCheck.ts',
            content: this.generateDatabaseHealthCheckCode(config),
            dependencies: ['typeorm']
        });
        // External services health check
        artifacts.push({
            type: 'health-check',
            filePath: 'src/monitoring/ExternalServicesHealthCheck.ts',
            content: this.generateExternalServicesHealthCheckCode(config),
            dependencies: ['axios', '@types/axios']
        });
        return artifacts;
    }
    /**
     * Generate performance monitoring
     */
    async generatePerformanceMonitoring(config) {
        const artifacts = [];
        // Performance monitor
        artifacts.push({
            type: 'metrics',
            filePath: 'src/monitoring/PerformanceMonitor.ts',
            content: this.generatePerformanceMonitorCode(config),
            dependencies: ['perf_hooks', 'prom-client']
        });
        // Database query monitor
        artifacts.push({
            type: 'metrics',
            filePath: 'src/monitoring/DatabaseQueryMonitor.ts',
            content: this.generateDatabaseQueryMonitorCode(config),
            dependencies: ['typeorm', 'prom-client']
        });
        return artifacts;
    }
    /**
     * Generate distributed tracing
     */
    async generateDistributedTracing(config) {
        const artifacts = [];
        if (config.tracing.enabled) {
            // Tracing setup
            artifacts.push({
                type: 'config',
                filePath: 'src/monitoring/TracingSetup.ts',
                content: this.generateTracingSetupCode(config),
                dependencies: ['@opentelemetry/api', '@opentelemetry/auto-instrumentations-node']
            });
            // Custom tracing middleware
            artifacts.push({
                type: 'middleware',
                filePath: 'src/monitoring/TracingMiddleware.ts',
                content: this.generateTracingMiddleware(config),
                dependencies: ['@opentelemetry/api', 'express']
            });
        }
        return artifacts;
    }
    /**
     * Generate monitoring middleware
     */
    async generateMonitoringMiddleware(config) {
        const artifacts = [];
        // Error tracking middleware
        artifacts.push({
            type: 'middleware',
            filePath: 'src/monitoring/ErrorTrackingMiddleware.ts',
            content: this.generateErrorTrackingMiddleware(config),
            dependencies: ['express', '@types/express']
        });
        // Security monitoring middleware
        artifacts.push({
            type: 'middleware',
            filePath: 'src/monitoring/SecurityMonitoringMiddleware.ts',
            content: this.generateSecurityMonitoringMiddleware(config),
            dependencies: ['express', 'helmet', '@types/express']
        });
        return artifacts;
    }
    /**
     * Generate monitoring configuration files
     */
    async generateMonitoringConfiguration(config) {
        return {
            dockerCompose: this.generateDockerComposeForMonitoring(config),
            prometheusConfig: this.generatePrometheusConfig(config),
            grafanaDashboards: [
                this.generateApplicationDashboard(config),
                this.generateInfrastructureDashboard(config),
                this.generateBusinessMetricsDashboard(config)
            ],
            alertingRules: [
                this.generateAlertingRules(config)
            ]
        };
    }
    /**
     * Generate health check definitions
     */
    generateHealthCheckDefinitions(config) {
        return [
            {
                name: 'database',
                endpoint: '/health/database',
                checks: ['connection', 'query-performance', 'disk-space']
            },
            {
                name: 'external-services',
                endpoint: '/health/external',
                checks: ['api-connectivity', 'response-time', 'authentication']
            },
            {
                name: 'application',
                endpoint: '/health/app',
                checks: ['memory-usage', 'cpu-usage', 'error-rate']
            }
        ];
    }
    /**
     * Generate metrics definitions
     */
    generateMetricsDefinitions(config) {
        return [
            {
                name: 'http_requests_total',
                type: 'counter',
                description: 'Total number of HTTP requests'
            },
            {
                name: 'http_request_duration_seconds',
                type: 'histogram',
                description: 'HTTP request duration in seconds'
            },
            {
                name: 'database_query_duration_seconds',
                type: 'histogram',
                description: 'Database query duration in seconds'
            },
            {
                name: 'active_connections',
                type: 'gauge',
                description: 'Number of active database connections'
            },
            {
                name: 'business_transactions_total',
                type: 'counter',
                description: 'Total number of business transactions'
            }
        ];
    }
    /**
     * Generate monitoring recommendations
     */
    generateMonitoringRecommendations(config) {
        const recommendations = [];
        recommendations.push('Set up alerting for critical metrics (error rate > 5%, response time > 2s)');
        recommendations.push('Implement log aggregation and centralized logging');
        recommendations.push('Monitor business KPIs alongside technical metrics');
        recommendations.push('Set up automated incident response workflows');
        recommendations.push('Implement distributed tracing for complex request flows');
        recommendations.push('Create runbooks for common operational scenarios');
        if (config.environment === 'production') {
            recommendations.push('Enable 24/7 monitoring and alerting');
            recommendations.push('Set up redundant monitoring infrastructure');
            recommendations.push('Implement chaos engineering practices');
        }
        return recommendations;
    }
    /**
     * Write monitoring files to disk
     */
    async writeMonitoringFiles(result, projectPath) {
        // Create monitoring directory structure
        const monitoringDir = path.join(projectPath, 'src/monitoring');
        const configDir = path.join(projectPath, 'monitoring');
        await fs.promises.mkdir(monitoringDir, { recursive: true });
        await fs.promises.mkdir(configDir, { recursive: true });
        // Write artifact files
        for (const artifact of result.artifacts) {
            const filePath = path.join(projectPath, artifact.filePath);
            const fileDir = path.dirname(filePath);
            await fs.promises.mkdir(fileDir, { recursive: true });
            await fs.promises.writeFile(filePath, artifact.content);
        }
        // Write configuration files
        await fs.promises.writeFile(path.join(configDir, 'docker-compose.monitoring.yml'), result.configuration.dockerCompose);
        await fs.promises.writeFile(path.join(configDir, 'prometheus.yml'), result.configuration.prometheusConfig);
        // Write Grafana dashboards
        const dashboardsDir = path.join(configDir, 'grafana/dashboards');
        await fs.promises.mkdir(dashboardsDir, { recursive: true });
        for (let i = 0; i < result.configuration.grafanaDashboards.length; i++) {
            await fs.promises.writeFile(path.join(dashboardsDir, `dashboard-${i + 1}.json`), result.configuration.grafanaDashboards[i]);
        }
    }
    // Code generation methods
    generateStructuredLoggerCode(config) {
        return `import winston from 'winston';
import { ElasticsearchTransport } from 'winston-elasticsearch';

export class StructuredLogger {
  private logger: winston.Logger;

  constructor() {
    const transports: winston.transport[] = [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.errors({ stack: true }),
          winston.format.json()
        )
      })
    ];

    if (process.env.NODE_ENV === 'production') {
      transports.push(
        new ElasticsearchTransport({
          level: 'info',
          clientOpts: { node: process.env.ELASTICSEARCH_URL }
        })
      );
    }

    this.logger = winston.createLogger({
      level: '${config.logging.level}',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      transports
    });
  }

  info(message: string, meta?: any) {
    this.logger.info(message, { ...meta, service: 'droidbotx-app' });
  }

  error(message: string, meta?: any) {
    this.logger.error(message, { ...meta, service: 'droidbotx-app' });
  }

  warn(message: string, meta?: any) {
    this.logger.warn(message, { ...meta, service: 'droidbotx-app' });
  }

  debug(message: string, meta?: any) {
    this.logger.debug(message, { ...meta, service: 'droidbotx-app' });
  }
}`;
    }
    generateMetricsCollectorCode(config) {
        return `import { register, Counter, Histogram, Gauge } from 'prom-client';

export class MetricsCollector {
  private httpRequestsTotal: Counter<string>;
  private httpRequestDuration: Histogram<string>;
  private activeConnections: Gauge<string>;

  constructor() {
    this.httpRequestsTotal = new Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code']
    });

    this.httpRequestDuration = new Histogram({
      name: 'http_request_duration_seconds',
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'route'],
      buckets: [0.1, 0.5, 1, 2, 5]
    });

    this.activeConnections = new Gauge({
      name: 'active_connections',
      help: 'Number of active database connections'
    });
  }

  incrementHttpRequests(method: string, route: string, statusCode: number) {
    this.httpRequestsTotal.inc({ method, route, status_code: statusCode.toString() });
  }

  recordHttpDuration(method: string, route: string, duration: number) {
    this.httpRequestDuration.observe({ method, route }, duration);
  }

  setActiveConnections(count: number) {
    this.activeConnections.set(count);
  }

  getMetrics() {
    return register.metrics();
  }
}`;
    }
    generateHealthCheckManagerCode(config) {
        return `import { Request, Response } from 'express';
import { DatabaseHealthCheck } from './DatabaseHealthCheck';
import { ExternalServicesHealthCheck } from './ExternalServicesHealthCheck';

export class HealthCheckManager {
  private databaseHealthCheck: DatabaseHealthCheck;
  private externalServicesHealthCheck: ExternalServicesHealthCheck;

  constructor() {
    this.databaseHealthCheck = new DatabaseHealthCheck();
    this.externalServicesHealthCheck = new ExternalServicesHealthCheck();
  }

  async checkHealth(req: Request, res: Response) {
    const checks = await Promise.allSettled([
      this.databaseHealthCheck.check(),
      this.externalServicesHealthCheck.check()
    ]);

    const results = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: checks[0].status === 'fulfilled' ? checks[0].value : { status: 'unhealthy', error: checks[0].reason },
        externalServices: checks[1].status === 'fulfilled' ? checks[1].value : { status: 'unhealthy', error: checks[1].reason }
      }
    };

    const isHealthy = Object.values(results.checks).every(check => check.status === 'healthy');
    results.status = isHealthy ? 'healthy' : 'unhealthy';

    res.status(isHealthy ? 200 : 503).json(results);
  }
}`;
    }
    // Additional helper methods for generating other monitoring components
    generateLogCorrelationMiddleware(config) {
        return '// Log correlation middleware implementation';
    }
    generateRequestLoggingMiddleware(config) {
        return '// Request logging middleware implementation';
    }
    generateBusinessMetricsCode(config) {
        return '// Business metrics implementation';
    }
    generatePerformanceMetricsMiddleware(config) {
        return '// Performance metrics middleware implementation';
    }
    generateDatabaseHealthCheckCode(config) {
        return '// Database health check implementation';
    }
    generateExternalServicesHealthCheckCode(config) {
        return '// External services health check implementation';
    }
    generatePerformanceMonitorCode(config) {
        return '// Performance monitor implementation';
    }
    generateDatabaseQueryMonitorCode(config) {
        return '// Database query monitor implementation';
    }
    generateTracingSetupCode(config) {
        return '// Distributed tracing setup implementation';
    }
    generateTracingMiddleware(config) {
        return '// Tracing middleware implementation';
    }
    generateErrorTrackingMiddleware(config) {
        return '// Error tracking middleware implementation';
    }
    generateSecurityMonitoringMiddleware(config) {
        return '// Security monitoring middleware implementation';
    }
    generateDockerComposeForMonitoring(config) {
        return '# Docker compose for monitoring stack';
    }
    generatePrometheusConfig(config) {
        return '# Prometheus configuration';
    }
    generateApplicationDashboard(config) {
        return '{}'; // Grafana dashboard JSON
    }
    generateInfrastructureDashboard(config) {
        return '{}'; // Grafana dashboard JSON
    }
    generateBusinessMetricsDashboard(config) {
        return '{}'; // Grafana dashboard JSON
    }
    generateAlertingRules(config) {
        return '# Prometheus alerting rules';
    }
    initializeTemplates() {
        // Initialize monitoring templates
        this.monitoringTemplates.set('logger', 'Structured logger template');
        this.monitoringTemplates.set('metrics', 'Metrics collector template');
        this.monitoringTemplates.set('health-check', 'Health check template');
    }
}
exports.MonitoringObservabilitySystem = MonitoringObservabilitySystem;
//# sourceMappingURL=MonitoringObservabilitySystem.js.map
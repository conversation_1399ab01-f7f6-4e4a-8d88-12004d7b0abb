export interface FileOperation {
    id: string;
    type: 'read' | 'write' | 'delete' | 'copy' | 'move';
    path: string;
    content?: string;
    destination?: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    timestamp: number;
}
export interface FileCache {
    path: string;
    content: string;
    lastModified: number;
    size: number;
    accessCount: number;
    lastAccessed: number;
}
export interface FileMetrics {
    totalOperations: number;
    cacheHits: number;
    cacheMisses: number;
    averageOperationTime: number;
    totalBytesProcessed: number;
    concurrentOperations: number;
    errorRate: number;
}
export interface FileManagerConfig {
    maxCacheSize: number;
    maxConcurrentOperations: number;
    cacheExpirationTime: number;
    compressionThreshold: number;
    batchSize: number;
    retryAttempts: number;
}
export declare class OptimizedFileManager {
    private logger;
    private config;
    private cache;
    private operationQueue;
    private activeOperations;
    private metrics;
    private compressionCache;
    constructor(config?: Partial<FileManagerConfig>);
    /**
     * Optimized file write with caching and batching
     */
    writeFileOptimized(filePath: string, content: string, priority?: FileOperation['priority']): Promise<void>;
    /**
     * Optimized file read with caching
     */
    readFileOptimized(filePath: string): Promise<string>;
    /**
     * Batch write multiple files
     */
    writeMultipleFiles(files: Array<{
        path: string;
        content: string;
        priority?: FileOperation['priority'];
    }>): Promise<void>;
    /**
     * Copy file with optimization
     */
    copyFileOptimized(sourcePath: string, destinationPath: string): Promise<void>;
    /**
     * Delete file with cache cleanup
     */
    deleteFileOptimized(filePath: string): Promise<void>;
    /**
     * Execute file operation with retry logic
     */
    private executeFileOperation;
    /**
     * Individual operation implementations
     */
    private executeWrite;
    private executeRead;
    private executeCopy;
    private executeDelete;
    private executeMove;
    /**
     * Cache management
     */
    private cacheFile;
    private ensureCacheSpace;
    private getCurrentCacheSize;
    private isCacheValid;
    /**
     * Compression utilities
     */
    private compressContent;
    private decompressIfNeeded;
    /**
     * Queue management
     */
    private addToQueue;
    private canProcessImmediately;
    private createBatches;
    private processBatch;
    private readFromDisk;
    /**
     * Background processes
     */
    private startQueueProcessor;
    private startCacheCleanup;
    /**
     * Metrics updates
     */
    private updateAverageOperationTime;
    private updateErrorRate;
    /**
     * Public API methods
     */
    getMetrics(): FileMetrics;
    getCacheStats(): {
        size: number;
        entries: number;
        hitRate: number;
    };
    clearCache(): void;
    updateConfig(newConfig: Partial<FileManagerConfig>): void;
}
//# sourceMappingURL=OptimizedFileManager.d.ts.map
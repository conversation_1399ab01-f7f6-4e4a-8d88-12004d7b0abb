"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimizedFileManager = void 0;
const Logger_1 = require("./Logger");
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
class OptimizedFileManager {
    constructor(config) {
        this.cache = new Map();
        this.operationQueue = [];
        this.activeOperations = new Set();
        this.compressionCache = new Map();
        this.logger = Logger_1.Logger.getInstance();
        this.config = {
            maxCacheSize: 100, // 100MB
            maxConcurrentOperations: 10,
            cacheExpirationTime: 30 * 60 * 1000, // 30 minutes
            compressionThreshold: 10 * 1024, // 10KB
            batchSize: 5,
            retryAttempts: 3,
            ...config
        };
        this.metrics = {
            totalOperations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageOperationTime: 0,
            totalBytesProcessed: 0,
            concurrentOperations: 0,
            errorRate: 0
        };
        this.startQueueProcessor();
        this.startCacheCleanup();
    }
    /**
     * Optimized file write with caching and batching
     */
    async writeFileOptimized(filePath, content, priority = 'medium') {
        const operation = {
            id: `write-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: 'write',
            path: filePath,
            content,
            priority,
            timestamp: Date.now()
        };
        // Check if we can process immediately
        if (this.canProcessImmediately()) {
            return await this.executeFileOperation(operation);
        }
        // Add to queue
        this.addToQueue(operation);
        // Return promise that resolves when operation completes
        return new Promise((resolve, reject) => {
            const checkCompletion = () => {
                if (!this.operationQueue.find(op => op.id === operation.id) &&
                    !this.activeOperations.has(operation.id)) {
                    resolve();
                }
                else {
                    setTimeout(checkCompletion, 100);
                }
            };
            checkCompletion();
        });
    }
    /**
     * Optimized file read with caching
     */
    async readFileOptimized(filePath) {
        const startTime = Date.now();
        this.metrics.totalOperations++;
        try {
            // Check cache first
            const cached = this.cache.get(filePath);
            if (cached && this.isCacheValid(cached)) {
                cached.accessCount++;
                cached.lastAccessed = Date.now();
                this.metrics.cacheHits++;
                this.logger.debug('File read from cache', {
                    path: filePath,
                    size: cached.size,
                    accessCount: cached.accessCount
                });
                return this.decompressIfNeeded(cached.content);
            }
            this.metrics.cacheMisses++;
            // Read from disk
            const content = await this.readFromDisk(filePath);
            // Cache the content
            await this.cacheFile(filePath, content);
            const duration = Date.now() - startTime;
            this.updateAverageOperationTime(duration);
            this.metrics.totalBytesProcessed += Buffer.byteLength(content, 'utf8');
            this.logger.debug('File read from disk and cached', {
                path: filePath,
                size: Buffer.byteLength(content, 'utf8'),
                duration
            });
            return content;
        }
        catch (error) {
            this.updateErrorRate(true);
            this.logger.error('Optimized file read failed', {
                path: filePath,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Batch write multiple files
     */
    async writeMultipleFiles(files) {
        this.logger.debug('Writing multiple files', { count: files.length });
        const operations = files.map(file => ({
            id: `batch-write-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: 'write',
            path: file.path,
            content: file.content,
            priority: file.priority || 'medium',
            timestamp: Date.now()
        }));
        // Process in batches
        const batches = this.createBatches(operations, this.config.batchSize);
        for (const batch of batches) {
            await this.processBatch(batch);
        }
    }
    /**
     * Copy file with optimization
     */
    async copyFileOptimized(sourcePath, destinationPath) {
        const operation = {
            id: `copy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: 'copy',
            path: sourcePath,
            destination: destinationPath,
            priority: 'medium',
            timestamp: Date.now()
        };
        return await this.executeFileOperation(operation);
    }
    /**
     * Delete file with cache cleanup
     */
    async deleteFileOptimized(filePath) {
        const operation = {
            id: `delete-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: 'delete',
            path: filePath,
            priority: 'high',
            timestamp: Date.now()
        };
        // Remove from cache immediately
        this.cache.delete(filePath);
        this.compressionCache.delete(filePath);
        return await this.executeFileOperation(operation);
    }
    /**
     * Execute file operation with retry logic
     */
    async executeFileOperation(operation) {
        const startTime = Date.now();
        this.metrics.totalOperations++;
        this.activeOperations.add(operation.id);
        this.metrics.concurrentOperations = this.activeOperations.size;
        let lastError = null;
        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                switch (operation.type) {
                    case 'write':
                        await this.executeWrite(operation);
                        break;
                    case 'read':
                        await this.executeRead(operation);
                        break;
                    case 'copy':
                        await this.executeCopy(operation);
                        break;
                    case 'delete':
                        await this.executeDelete(operation);
                        break;
                    case 'move':
                        await this.executeMove(operation);
                        break;
                }
                // Success
                const duration = Date.now() - startTime;
                this.updateAverageOperationTime(duration);
                this.updateErrorRate(false);
                this.logger.debug('File operation completed', {
                    type: operation.type,
                    path: operation.path,
                    duration,
                    attempt
                });
                break;
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                if (attempt === this.config.retryAttempts) {
                    this.updateErrorRate(true);
                    this.logger.error('File operation failed after all retries', {
                        type: operation.type,
                        path: operation.path,
                        attempts: attempt,
                        error: lastError.message
                    });
                    throw lastError;
                }
                // Wait before retry with exponential backoff
                const delay = Math.pow(2, attempt - 1) * 100;
                await new Promise(resolve => setTimeout(resolve, delay));
                this.logger.warn('File operation retry', {
                    type: operation.type,
                    path: operation.path,
                    attempt,
                    nextAttemptIn: delay * 2
                });
            }
        }
        this.activeOperations.delete(operation.id);
        this.metrics.concurrentOperations = this.activeOperations.size;
    }
    /**
     * Individual operation implementations
     */
    async executeWrite(operation) {
        if (!operation.content)
            throw new Error('No content provided for write operation');
        const dir = path.dirname(operation.path);
        await fs.mkdir(dir, { recursive: true });
        await fs.writeFile(operation.path, operation.content, 'utf8');
        // Update cache
        await this.cacheFile(operation.path, operation.content);
        this.metrics.totalBytesProcessed += Buffer.byteLength(operation.content, 'utf8');
    }
    async executeRead(operation) {
        const content = await fs.readFile(operation.path, 'utf8');
        await this.cacheFile(operation.path, content);
        this.metrics.totalBytesProcessed += Buffer.byteLength(content, 'utf8');
        return content;
    }
    async executeCopy(operation) {
        if (!operation.destination)
            throw new Error('No destination provided for copy operation');
        const content = await this.readFileOptimized(operation.path);
        await this.writeFileOptimized(operation.destination, content);
    }
    async executeDelete(operation) {
        await fs.unlink(operation.path);
    }
    async executeMove(operation) {
        if (!operation.destination)
            throw new Error('No destination provided for move operation');
        await fs.rename(operation.path, operation.destination);
        // Update cache
        const cached = this.cache.get(operation.path);
        if (cached) {
            this.cache.delete(operation.path);
            this.cache.set(operation.destination, { ...cached, path: operation.destination });
        }
    }
    /**
     * Cache management
     */
    async cacheFile(filePath, content) {
        const size = Buffer.byteLength(content, 'utf8');
        // Check if we need to compress
        let cachedContent = content;
        if (size > this.config.compressionThreshold) {
            cachedContent = await this.compressContent(content);
        }
        const cacheEntry = {
            path: filePath,
            content: cachedContent,
            lastModified: Date.now(),
            size,
            accessCount: 1,
            lastAccessed: Date.now()
        };
        // Check cache size and evict if necessary
        await this.ensureCacheSpace(size);
        this.cache.set(filePath, cacheEntry);
    }
    async ensureCacheSpace(newEntrySize) {
        const currentSize = this.getCurrentCacheSize();
        const maxSizeBytes = this.config.maxCacheSize * 1024 * 1024;
        if (currentSize + newEntrySize > maxSizeBytes) {
            // Evict least recently used entries
            const entries = Array.from(this.cache.entries());
            entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
            let freedSpace = 0;
            for (const [key, entry] of entries) {
                this.cache.delete(key);
                this.compressionCache.delete(key);
                freedSpace += entry.size;
                if (freedSpace >= newEntrySize)
                    break;
            }
            this.logger.debug('Cache space freed', {
                freedSpace,
                remainingEntries: this.cache.size
            });
        }
    }
    getCurrentCacheSize() {
        return Array.from(this.cache.values()).reduce((total, entry) => total + entry.size, 0);
    }
    isCacheValid(cached) {
        const age = Date.now() - cached.lastModified;
        return age < this.config.cacheExpirationTime;
    }
    /**
     * Compression utilities
     */
    async compressContent(content) {
        // Simple compression simulation - in practice, use zlib or similar
        const compressed = Buffer.from(content).toString('base64');
        return `COMPRESSED:${compressed}`;
    }
    decompressIfNeeded(content) {
        if (content.startsWith('COMPRESSED:')) {
            const compressed = content.substring('COMPRESSED:'.length);
            return Buffer.from(compressed, 'base64').toString('utf8');
        }
        return content;
    }
    /**
     * Queue management
     */
    addToQueue(operation) {
        // Insert based on priority
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        const insertIndex = this.operationQueue.findIndex(op => priorityOrder[op.priority] > priorityOrder[operation.priority]);
        if (insertIndex === -1) {
            this.operationQueue.push(operation);
        }
        else {
            this.operationQueue.splice(insertIndex, 0, operation);
        }
    }
    canProcessImmediately() {
        return this.activeOperations.size < this.config.maxConcurrentOperations;
    }
    createBatches(items, batchSize) {
        const batches = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }
    async processBatch(batch) {
        const promises = batch.map(operation => this.executeFileOperation(operation));
        await Promise.all(promises);
    }
    async readFromDisk(filePath) {
        return await fs.readFile(filePath, 'utf8');
    }
    /**
     * Background processes
     */
    startQueueProcessor() {
        setInterval(async () => {
            while (this.operationQueue.length > 0 && this.canProcessImmediately()) {
                const operation = this.operationQueue.shift();
                // Don't await - process in background
                this.executeFileOperation(operation).catch(error => {
                    this.logger.error('Queued operation failed', {
                        operationId: operation.id,
                        type: operation.type,
                        path: operation.path,
                        error: error instanceof Error ? error.message : String(error)
                    });
                });
            }
        }, 100);
    }
    startCacheCleanup() {
        setInterval(() => {
            const now = Date.now();
            const expiredEntries = [];
            for (const [key, entry] of this.cache.entries()) {
                if (now - entry.lastModified > this.config.cacheExpirationTime) {
                    expiredEntries.push(key);
                }
            }
            for (const key of expiredEntries) {
                this.cache.delete(key);
                this.compressionCache.delete(key);
            }
            if (expiredEntries.length > 0) {
                this.logger.debug('Cache cleanup completed', {
                    expiredEntries: expiredEntries.length,
                    remainingEntries: this.cache.size
                });
            }
        }, 5 * 60 * 1000); // Every 5 minutes
    }
    /**
     * Metrics updates
     */
    updateAverageOperationTime(duration) {
        const alpha = 0.1; // Exponential moving average factor
        this.metrics.averageOperationTime = (1 - alpha) * this.metrics.averageOperationTime + alpha * duration;
    }
    updateErrorRate(isError) {
        const alpha = 0.1;
        const errorValue = isError ? 1 : 0;
        this.metrics.errorRate = (1 - alpha) * this.metrics.errorRate + alpha * errorValue;
    }
    /**
     * Public API methods
     */
    getMetrics() {
        return { ...this.metrics };
    }
    getCacheStats() {
        const total = this.metrics.cacheHits + this.metrics.cacheMisses;
        return {
            size: this.getCurrentCacheSize(),
            entries: this.cache.size,
            hitRate: total > 0 ? (this.metrics.cacheHits / total) * 100 : 0
        };
    }
    clearCache() {
        this.cache.clear();
        this.compressionCache.clear();
        this.logger.info('File cache cleared');
    }
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.logger.debug('File manager configuration updated', this.config);
    }
}
exports.OptimizedFileManager = OptimizedFileManager;
//# sourceMappingURL=OptimizedFileManager.js.map
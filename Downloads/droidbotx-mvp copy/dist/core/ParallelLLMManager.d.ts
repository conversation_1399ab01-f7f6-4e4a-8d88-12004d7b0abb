export interface LLMPrompt {
    id: string;
    content: string;
    type: 'code-generation' | 'analysis' | 'validation' | 'enhancement';
    priority: 'high' | 'medium' | 'low';
    maxTokens?: number;
    temperature?: number;
    context?: any;
}
export interface LLMResponse {
    id: string;
    content: string;
    tokens: number;
    duration: number;
    success: boolean;
    error?: string;
}
export interface LLMBatch {
    id: string;
    prompts: LLMPrompt[];
    batchType: 'compatible' | 'sequential' | 'independent';
    estimatedTokens: number;
    priority: number;
}
export interface LLMMetrics {
    totalRequests: number;
    batchedRequests: number;
    parallelRequests: number;
    averageResponseTime: number;
    tokenUsage: number;
    batchingEfficiency: number;
    errorRate: number;
}
export interface LLMConfig {
    maxConcurrentRequests: number;
    maxBatchSize: number;
    batchTimeout: number;
    retryAttempts: number;
    rateLimitPerMinute: number;
}
export declare class ParallelLLMManager {
    private logger;
    private config;
    private metrics;
    private activeRequests;
    private requestQueue;
    private batchQueue;
    private rateLimitTracker;
    constructor(config?: Partial<LLMConfig>);
    /**
     * Generate multiple LLM responses in parallel with intelligent batching
     */
    generateMultipleResponses(prompts: LLMPrompt[]): Promise<LLMResponse[]>;
    /**
     * Generate single LLM response with queueing
     */
    generateSingleResponse(prompt: LLMPrompt): Promise<LLMResponse>;
    /**
     * Create optimal batches from prompts
     */
    private createOptimalBatches;
    /**
     * Group prompts that can be processed together
     */
    private groupCompatiblePrompts;
    /**
     * Check if two prompts can be batched together
     */
    private arePromptsCompatible;
    /**
     * Process a batch of prompts
     */
    private processBatch;
    /**
     * Process compatible prompts as a single request
     */
    private processCompatibleBatch;
    /**
     * Process prompts sequentially (when order matters)
     */
    private processSequentialBatch;
    /**
     * Process prompts independently in parallel
     */
    private processIndependentBatch;
    /**
     * Process individual prompt (mock implementation)
     */
    private processPrompt;
    /**
     * Generate mock response based on prompt type
     */
    private generateMockResponse;
    /**
     * Helper methods
     */
    private determineBatchType;
    private estimateBatchTokens;
    private calculateBatchPriority;
    private combinePrompts;
    private splitCombinedResponse;
    private reorderResponses;
    private executeWithConcurrencyControl;
    private canProcessImmediately;
    private enforceRateLimit;
    private updateMetrics;
    private startBatchProcessor;
    private startRateLimitCleaner;
    /**
     * Get current metrics
     */
    getMetrics(): LLMMetrics;
    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<LLMConfig>): void;
}
//# sourceMappingURL=ParallelLLMManager.d.ts.map
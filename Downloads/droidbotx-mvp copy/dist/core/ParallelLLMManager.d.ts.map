{"version": 3, "file": "ParallelLLMManager.d.ts", "sourceRoot": "", "sources": ["../../src/core/ParallelLLMManager.ts"], "names": [], "mappings": "AAEA,MAAM,WAAW,SAAS;IACxB,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,iBAAiB,GAAG,UAAU,GAAG,YAAY,GAAG,aAAa,CAAC;IACpE,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;IACpC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,GAAG,CAAC;CACf;AAED,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,QAAQ;IACvB,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,SAAS,EAAE,CAAC;IACrB,SAAS,EAAE,YAAY,GAAG,YAAY,GAAG,aAAa,CAAC;IACvD,eAAe,EAAE,MAAM,CAAC;IACxB,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,UAAU;IACzB,aAAa,EAAE,MAAM,CAAC;IACtB,eAAe,EAAE,MAAM,CAAC;IACxB,gBAAgB,EAAE,MAAM,CAAC;IACzB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,UAAU,EAAE,MAAM,CAAC;IACnB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,SAAS;IACxB,qBAAqB,EAAE,MAAM,CAAC;IAC9B,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;IACrB,aAAa,EAAE,MAAM,CAAC;IACtB,kBAAkB,EAAE,MAAM,CAAC;CAC5B;AAED,qBAAa,kBAAkB;IAC7B,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,MAAM,CAAY;IAC1B,OAAO,CAAC,OAAO,CAAa;IAC5B,OAAO,CAAC,cAAc,CAAgD;IACtE,OAAO,CAAC,YAAY,CAAmB;IACvC,OAAO,CAAC,UAAU,CAAkB;IACpC,OAAO,CAAC,gBAAgB,CAAgB;gBAE5B,MAAM,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC;IAyBvC;;OAEG;IACU,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IA0DpF;;OAEG;IACU,sBAAsB,CAAC,MAAM,EAAE,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC;IAkB5E;;OAEG;IACH,OAAO,CAAC,oBAAoB;IA8B5B;;OAEG;IACH,OAAO,CAAC,sBAAsB;IA0B9B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAuB5B;;OAEG;YACW,YAAY;IA0D1B;;OAEG;YACW,sBAAsB;IAcpC;;OAEG;YACW,sBAAsB;IA4BpC;;OAEG;YACW,uBAAuB;IAKrC;;OAEG;YACW,aAAa;IA+B3B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAe5B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAc1B,OAAO,CAAC,mBAAmB;IAQ3B,OAAO,CAAC,sBAAsB;IAM9B,OAAO,CAAC,cAAc;IAatB,OAAO,CAAC,qBAAqB;IAa7B,OAAO,CAAC,gBAAgB;YAKV,6BAA6B;IAqB3C,OAAO,CAAC,qBAAqB;YAIf,gBAAgB;IAoB9B,OAAO,CAAC,aAAa;IAYrB,OAAO,CAAC,mBAAmB;IAS3B,OAAO,CAAC,qBAAqB;IAO7B;;OAEG;IACI,UAAU,IAAI,UAAU;IAI/B;;OAEG;IACI,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI;CAIzD"}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParallelLLMManager = void 0;
const Logger_1 = require("./Logger");
class ParallelLLMManager {
    constructor(config) {
        this.activeRequests = new Map();
        this.requestQueue = [];
        this.batchQueue = [];
        this.rateLimitTracker = [];
        this.logger = Logger_1.Logger.getInstance();
        this.config = {
            maxConcurrentRequests: 5,
            maxBatchSize: 10,
            batchTimeout: 2000,
            retryAttempts: 3,
            rateLimitPerMinute: 60,
            ...config
        };
        this.metrics = {
            totalRequests: 0,
            batchedRequests: 0,
            parallelRequests: 0,
            averageResponseTime: 0,
            tokenUsage: 0,
            batchingEfficiency: 0,
            errorRate: 0
        };
        this.startBatchProcessor();
        this.startRateLimitCleaner();
    }
    /**
     * Generate multiple LLM responses in parallel with intelligent batching
     */
    async generateMultipleResponses(prompts) {
        this.logger.debug('Processing multiple LLM requests', {
            count: prompts.length,
            types: prompts.map(p => p.type)
        });
        const startTime = Date.now();
        this.metrics.totalRequests += prompts.length;
        try {
            // Analyze prompts for batching opportunities
            const batches = this.createOptimalBatches(prompts);
            this.logger.debug('Created LLM batches', {
                originalPrompts: prompts.length,
                batches: batches.length,
                batchSizes: batches.map(b => b.prompts.length)
            });
            // Process batches in parallel with concurrency control
            const batchPromises = batches.map(batch => this.processBatch(batch));
            const batchResults = await this.executeWithConcurrencyControl(batchPromises);
            // Flatten results and maintain original order
            const allResponses = batchResults.flat();
            const orderedResponses = this.reorderResponses(prompts, allResponses);
            // Update metrics
            const duration = Date.now() - startTime;
            this.updateMetrics(prompts.length, duration, orderedResponses);
            this.logger.debug('Multiple LLM responses completed', {
                totalPrompts: prompts.length,
                successfulResponses: orderedResponses.filter(r => r.success).length,
                duration,
                batchingEfficiency: this.metrics.batchingEfficiency
            });
            return orderedResponses;
        }
        catch (error) {
            this.logger.error('Multiple LLM responses failed', {
                error: error instanceof Error ? error.message : String(error),
                promptCount: prompts.length
            });
            // Return error responses for all prompts
            return prompts.map(prompt => ({
                id: prompt.id,
                content: '',
                tokens: 0,
                duration: Date.now() - startTime,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            }));
        }
    }
    /**
     * Generate single LLM response with queueing
     */
    async generateSingleResponse(prompt) {
        // Check if we can process immediately
        if (this.canProcessImmediately()) {
            return await this.processPrompt(prompt);
        }
        // Add to queue and wait for processing
        return new Promise((resolve, reject) => {
            const queuedPrompt = {
                ...prompt,
                resolve,
                reject
            };
            this.requestQueue.push(queuedPrompt);
        });
    }
    /**
     * Create optimal batches from prompts
     */
    createOptimalBatches(prompts) {
        const batches = [];
        // Group prompts by compatibility
        const compatibleGroups = this.groupCompatiblePrompts(prompts);
        for (const group of compatibleGroups) {
            // Split large groups into smaller batches
            while (group.length > 0) {
                const batchSize = Math.min(this.config.maxBatchSize, group.length);
                const batchPrompts = group.splice(0, batchSize);
                const batch = {
                    id: `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    prompts: batchPrompts,
                    batchType: this.determineBatchType(batchPrompts),
                    estimatedTokens: this.estimateBatchTokens(batchPrompts),
                    priority: this.calculateBatchPriority(batchPrompts)
                };
                batches.push(batch);
            }
        }
        // Sort batches by priority
        batches.sort((a, b) => b.priority - a.priority);
        return batches;
    }
    /**
     * Group prompts that can be processed together
     */
    groupCompatiblePrompts(prompts) {
        const groups = [];
        const processed = new Set();
        for (const prompt of prompts) {
            if (processed.has(prompt.id))
                continue;
            const compatibleGroup = [prompt];
            processed.add(prompt.id);
            // Find other prompts that can be batched with this one
            for (const otherPrompt of prompts) {
                if (processed.has(otherPrompt.id))
                    continue;
                if (this.arePromptsCompatible(prompt, otherPrompt)) {
                    compatibleGroup.push(otherPrompt);
                    processed.add(otherPrompt.id);
                }
            }
            groups.push(compatibleGroup);
        }
        return groups;
    }
    /**
     * Check if two prompts can be batched together
     */
    arePromptsCompatible(prompt1, prompt2) {
        // Same type prompts are usually compatible
        if (prompt1.type === prompt2.type)
            return true;
        // Code generation and enhancement can be batched
        if ((prompt1.type === 'code-generation' && prompt2.type === 'enhancement') ||
            (prompt1.type === 'enhancement' && prompt2.type === 'code-generation')) {
            return true;
        }
        // Analysis and validation can be batched
        if ((prompt1.type === 'analysis' && prompt2.type === 'validation') ||
            (prompt1.type === 'validation' && prompt2.type === 'analysis')) {
            return true;
        }
        // Similar temperature and token requirements
        const tempDiff = Math.abs((prompt1.temperature || 0.7) - (prompt2.temperature || 0.7));
        const tokenDiff = Math.abs((prompt1.maxTokens || 1000) - (prompt2.maxTokens || 1000));
        return tempDiff < 0.2 && tokenDiff < 500;
    }
    /**
     * Process a batch of prompts
     */
    async processBatch(batch) {
        this.logger.debug('Processing LLM batch', {
            batchId: batch.id,
            promptCount: batch.prompts.length,
            type: batch.batchType,
            estimatedTokens: batch.estimatedTokens
        });
        const startTime = Date.now();
        try {
            // Check rate limits
            await this.enforceRateLimit();
            let responses;
            switch (batch.batchType) {
                case 'compatible':
                    responses = await this.processCompatibleBatch(batch);
                    break;
                case 'sequential':
                    responses = await this.processSequentialBatch(batch);
                    break;
                case 'independent':
                default:
                    responses = await this.processIndependentBatch(batch);
                    break;
            }
            this.metrics.batchedRequests += batch.prompts.length;
            const duration = Date.now() - startTime;
            this.logger.debug('Batch processing completed', {
                batchId: batch.id,
                duration,
                successCount: responses.filter(r => r.success).length
            });
            return responses;
        }
        catch (error) {
            this.logger.error('Batch processing failed', {
                batchId: batch.id,
                error: error instanceof Error ? error.message : String(error)
            });
            // Return error responses for all prompts in batch
            return batch.prompts.map(prompt => ({
                id: prompt.id,
                content: '',
                tokens: 0,
                duration: Date.now() - startTime,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            }));
        }
    }
    /**
     * Process compatible prompts as a single request
     */
    async processCompatibleBatch(batch) {
        // Combine compatible prompts into a single request
        const combinedPrompt = this.combinePrompts(batch.prompts);
        const response = await this.processPrompt(combinedPrompt);
        if (response.success) {
            // Split the response back into individual responses
            return this.splitCombinedResponse(response, batch.prompts);
        }
        else {
            // If combined request fails, try individual requests
            return await this.processIndependentBatch(batch);
        }
    }
    /**
     * Process prompts sequentially (when order matters)
     */
    async processSequentialBatch(batch) {
        const responses = [];
        for (const prompt of batch.prompts) {
            const response = await this.processPrompt(prompt);
            responses.push(response);
            // If a critical prompt fails, stop processing
            if (!response.success && prompt.priority === 'high') {
                // Add error responses for remaining prompts
                const remaining = batch.prompts.slice(responses.length);
                for (const remainingPrompt of remaining) {
                    responses.push({
                        id: remainingPrompt.id,
                        content: '',
                        tokens: 0,
                        duration: 0,
                        success: false,
                        error: 'Batch stopped due to critical failure'
                    });
                }
                break;
            }
        }
        return responses;
    }
    /**
     * Process prompts independently in parallel
     */
    async processIndependentBatch(batch) {
        const promptPromises = batch.prompts.map(prompt => this.processPrompt(prompt));
        return await Promise.all(promptPromises);
    }
    /**
     * Process individual prompt (mock implementation)
     */
    async processPrompt(prompt) {
        const startTime = Date.now();
        try {
            // Mock LLM processing delay
            const processingTime = Math.random() * 2000 + 500; // 0.5-2.5 seconds
            await new Promise(resolve => setTimeout(resolve, processingTime));
            // Mock response generation
            const mockResponse = this.generateMockResponse(prompt);
            return {
                id: prompt.id,
                content: mockResponse,
                tokens: Math.floor(mockResponse.length / 4), // Rough token estimate
                duration: Date.now() - startTime,
                success: true
            };
        }
        catch (error) {
            return {
                id: prompt.id,
                content: '',
                tokens: 0,
                duration: Date.now() - startTime,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Generate mock response based on prompt type
     */
    generateMockResponse(prompt) {
        switch (prompt.type) {
            case 'code-generation':
                return `// Generated code for: ${prompt.content.substring(0, 50)}...\nfunction generatedFunction() {\n  return 'mock implementation';\n}`;
            case 'analysis':
                return `Analysis result: The provided content appears to be ${prompt.content.length > 100 ? 'complex' : 'simple'} and requires further processing.`;
            case 'validation':
                return `Validation result: The content is ${Math.random() > 0.8 ? 'invalid' : 'valid'} based on the specified criteria.`;
            case 'enhancement':
                return `Enhanced version: ${prompt.content} [with improvements applied]`;
            default:
                return `Processed: ${prompt.content.substring(0, 100)}...`;
        }
    }
    /**
     * Helper methods
     */
    determineBatchType(prompts) {
        if (prompts.length === 1)
            return 'independent';
        // If all prompts are the same type, they're compatible
        const types = new Set(prompts.map(p => p.type));
        if (types.size === 1)
            return 'compatible';
        // If prompts have dependencies, process sequentially
        const hasHighPriority = prompts.some(p => p.priority === 'high');
        if (hasHighPriority)
            return 'sequential';
        return 'independent';
    }
    estimateBatchTokens(prompts) {
        return prompts.reduce((total, prompt) => {
            const inputTokens = Math.floor(prompt.content.length / 4);
            const outputTokens = prompt.maxTokens || 1000;
            return total + inputTokens + outputTokens;
        }, 0);
    }
    calculateBatchPriority(prompts) {
        const priorityWeights = { high: 3, medium: 2, low: 1 };
        const totalWeight = prompts.reduce((sum, prompt) => sum + priorityWeights[prompt.priority], 0);
        return totalWeight / prompts.length;
    }
    combinePrompts(prompts) {
        const combinedContent = prompts.map((p, i) => `[Request ${i + 1}]: ${p.content}`).join('\n\n');
        return {
            id: `combined-${prompts.map(p => p.id).join('-')}`,
            content: combinedContent,
            type: prompts[0].type,
            priority: prompts[0].priority,
            maxTokens: prompts.reduce((sum, p) => sum + (p.maxTokens || 1000), 0),
            temperature: prompts[0].temperature
        };
    }
    splitCombinedResponse(response, originalPrompts) {
        // Simple splitting logic - in practice, this would be more sophisticated
        const sections = response.content.split(/\[Response \d+\]:/);
        return originalPrompts.map((prompt, index) => ({
            id: prompt.id,
            content: sections[index + 1] || response.content,
            tokens: Math.floor(response.tokens / originalPrompts.length),
            duration: response.duration,
            success: response.success
        }));
    }
    reorderResponses(originalPrompts, responses) {
        const responseMap = new Map(responses.map(r => [r.id, r]));
        return originalPrompts.map(prompt => responseMap.get(prompt.id));
    }
    async executeWithConcurrencyControl(promises) {
        const results = [];
        const executing = [];
        for (const promise of promises) {
            const p = promise.then(result => {
                results.push(result);
            });
            executing.push(p);
            if (executing.length >= this.config.maxConcurrentRequests) {
                await Promise.race(executing);
                executing.splice(executing.findIndex(p => p === p), 1);
            }
        }
        await Promise.all(executing);
        return results;
    }
    canProcessImmediately() {
        return this.activeRequests.size < this.config.maxConcurrentRequests;
    }
    async enforceRateLimit() {
        const now = Date.now();
        const oneMinuteAgo = now - 60000;
        // Clean old entries
        this.rateLimitTracker = this.rateLimitTracker.filter(time => time > oneMinuteAgo);
        if (this.rateLimitTracker.length >= this.config.rateLimitPerMinute) {
            const oldestRequest = Math.min(...this.rateLimitTracker);
            const waitTime = oldestRequest + 60000 - now;
            if (waitTime > 0) {
                this.logger.debug('Rate limit reached, waiting', { waitTime });
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
        this.rateLimitTracker.push(now);
    }
    updateMetrics(promptCount, duration, responses) {
        const successCount = responses.filter(r => r.success).length;
        const totalTokens = responses.reduce((sum, r) => sum + r.tokens, 0);
        // Update averages using exponential moving average
        const alpha = 0.1;
        this.metrics.averageResponseTime = (1 - alpha) * this.metrics.averageResponseTime + alpha * duration;
        this.metrics.tokenUsage += totalTokens;
        this.metrics.errorRate = (1 - alpha) * this.metrics.errorRate + alpha * ((promptCount - successCount) / promptCount);
        this.metrics.batchingEfficiency = (this.metrics.batchedRequests / this.metrics.totalRequests) * 100;
    }
    startBatchProcessor() {
        setInterval(() => {
            if (this.requestQueue.length > 0) {
                const batch = this.requestQueue.splice(0, this.config.maxBatchSize);
                // Process batch...
            }
        }, this.config.batchTimeout);
    }
    startRateLimitCleaner() {
        setInterval(() => {
            const oneMinuteAgo = Date.now() - 60000;
            this.rateLimitTracker = this.rateLimitTracker.filter(time => time > oneMinuteAgo);
        }, 60000);
    }
    /**
     * Get current metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Update configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.logger.debug('LLM manager configuration updated', this.config);
    }
}
exports.ParallelLLMManager = ParallelLLMManager;
//# sourceMappingURL=ParallelLLMManager.js.map
export interface PromptOptimization {
    originalPrompt: string;
    optimizedPrompt: string;
    tokenReduction: number;
    qualityScore: number;
    optimizationTechniques: string[];
}
export interface CacheEntry {
    key: string;
    value: any;
    timestamp: number;
    ttl: number;
    hitCount: number;
    size: number;
}
export interface PerformanceMetrics {
    totalExecutionTime: number;
    llmCallCount: number;
    cacheHitRate: number;
    tokensSaved: number;
    memoryUsage: number;
    redundancyReduction: number;
}
export interface OptimizationResult {
    phase: string;
    originalDuration: number;
    optimizedDuration: number;
    improvement: number;
    techniques: string[];
    metrics: PerformanceMetrics;
}
/**
 * Performance Optimization Engine for Phase 3
 * Optimizes workflow execution with efficient LLM prompts and intelligent caching
 */
export declare class PerformanceOptimization {
    private logger;
    private promptCache;
    private responseCache;
    private optimizedPrompts;
    private metrics;
    constructor();
    /**
     * Initialize performance optimizations
     */
    private initializeOptimizations;
    /**
     * Optimize LLM prompt for efficiency
     */
    optimizePrompt(originalPrompt: string, context?: any): PromptOptimization;
    /**
     * Perform actual prompt optimization
     */
    private performPromptOptimization;
    /**
     * Cache LLM response with intelligent key generation
     */
    cacheResponse(prompt: string, response: any, context?: any): void;
    /**
     * Retrieve cached response
     */
    getCachedResponse(prompt: string, context?: any): any | null;
    /**
     * Optimize agent communication by reducing redundant data transfer
     */
    optimizeAgentCommunication(data: any, targetAgent: string): any;
    /**
     * Detect and eliminate redundant operations
     */
    detectRedundancy(operations: any[]): {
        redundant: any[];
        unique: any[];
    };
    /**
     * Optimize workflow execution order
     */
    optimizeExecutionOrder(tasks: any[]): any[];
    /**
     * Get performance metrics
     */
    getMetrics(): PerformanceMetrics;
    /**
     * Generate optimization report
     */
    generateOptimizationReport(): OptimizationResult;
    /**
     * Clear all caches
     */
    clearCaches(): void;
    /**
     * Generate cache key
     */
    private generateCacheKey;
    /**
     * Check if cache entry is expired
     */
    private isCacheExpired;
    /**
     * Clean up expired cache entries
     */
    private cleanupExpiredCache;
    /**
     * Update performance metrics
     */
    private updateMetrics;
    /**
     * Remove verbose descriptions for optimization
     */
    private removeVerboseDescriptions;
    /**
     * Generate operation signature for redundancy detection
     */
    private generateOperationSignature;
    private logInfo;
    private logWarn;
    private logError;
}
//# sourceMappingURL=PerformanceOptimization.d.ts.map
{"version": 3, "file": "PerformanceOptimization.d.ts", "sourceRoot": "", "sources": ["../../src/core/PerformanceOptimization.ts"], "names": [], "mappings": "AAEA,MAAM,WAAW,kBAAkB;IACjC,cAAc,EAAE,MAAM,CAAC;IACvB,eAAe,EAAE,MAAM,CAAC;IACxB,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;IACrB,sBAAsB,EAAE,MAAM,EAAE,CAAC;CAClC;AAED,MAAM,WAAW,UAAU;IACzB,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,GAAG,CAAC;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,GAAG,EAAE,MAAM,CAAC;IACZ,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,kBAAkB;IACjC,kBAAkB,EAAE,MAAM,CAAC;IAC3B,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;IACrB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,mBAAmB,EAAE,MAAM,CAAC;CAC7B;AAED,MAAM,WAAW,kBAAkB;IACjC,KAAK,EAAE,MAAM,CAAC;IACd,gBAAgB,EAAE,MAAM,CAAC;IACzB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,OAAO,EAAE,kBAAkB,CAAC;CAC7B;AAED;;;GAGG;AACH,qBAAa,uBAAuB;IAClC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,WAAW,CAAsC;IACzD,OAAO,CAAC,aAAa,CAAsC;IAC3D,OAAO,CAAC,gBAAgB,CAA8C;IACtE,OAAO,CAAC,OAAO,CAOb;;IAOF;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAO/B;;OAEG;IACI,cAAc,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,GAAE,GAAQ,GAAG,kBAAkB;IA4BpF;;OAEG;IACH,OAAO,CAAC,yBAAyB;IA2EjC;;OAEG;IACI,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,GAAE,GAAQ,GAAG,IAAI;IAe5E;;OAEG;IACI,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,GAAE,GAAQ,GAAG,GAAG,GAAG,IAAI;IAevE;;OAEG;IACI,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,GAAG,GAAG;IA0CtE;;OAEG;IACI,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG;QAAE,SAAS,EAAE,GAAG,EAAE,CAAC;QAAC,MAAM,EAAE,GAAG,EAAE,CAAA;KAAE;IA6B/E;;OAEG;IACI,sBAAsB,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE;IA0BlD;;OAEG;IACI,UAAU,IAAI,kBAAkB;IAqBvC;;OAEG;IACI,0BAA0B,IAAI,kBAAkB;IAkBvD;;OAEG;IACI,WAAW,IAAI,IAAI;IAQ1B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAgBxB;;OAEG;IACH,OAAO,CAAC,cAAc;IAItB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IA4B3B;;OAEG;IACH,OAAO,CAAC,aAAa;IAoBrB;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAYjC;;OAEG;IACH,OAAO,CAAC,0BAA0B;IAWlC,OAAO,CAAC,OAAO;IAIf,OAAO,CAAC,OAAO;IAIf,OAAO,CAAC,QAAQ;CAGjB"}
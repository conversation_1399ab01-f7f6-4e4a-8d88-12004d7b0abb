"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceOptimization = void 0;
const Logger_1 = require("./Logger");
/**
 * Performance Optimization Engine for Phase 3
 * Optimizes workflow execution with efficient LLM prompts and intelligent caching
 */
class PerformanceOptimization {
    constructor() {
        this.promptCache = new Map();
        this.responseCache = new Map();
        this.optimizedPrompts = new Map();
        this.metrics = {
            totalExecutionTime: 0,
            llmCallCount: 0,
            cacheHitRate: 0,
            tokensSaved: 0,
            memoryUsage: 0,
            redundancyReduction: 0
        };
        this.logger = Logger_1.Logger.getInstance();
        this.initializeOptimizations();
    }
    /**
     * Initialize performance optimizations
     */
    initializeOptimizations() {
        // Set up cache cleanup interval
        setInterval(() => this.cleanupExpiredCache(), 300000); // 5 minutes
        this.logInfo('Performance optimization engine initialized');
    }
    /**
     * Optimize LLM prompt for efficiency
     */
    optimizePrompt(originalPrompt, context = {}) {
        const cacheKey = this.generateCacheKey('prompt_optimization', originalPrompt);
        const cached = this.promptCache.get(cacheKey);
        if (cached && !this.isCacheExpired(cached)) {
            cached.hitCount++;
            this.logInfo('Using cached prompt optimization', { cacheKey });
            return cached.value;
        }
        const optimization = this.performPromptOptimization(originalPrompt, context);
        // Cache the optimization
        this.promptCache.set(cacheKey, {
            key: cacheKey,
            value: optimization,
            timestamp: Date.now(),
            ttl: 3600000, // 1 hour
            hitCount: 1,
            size: JSON.stringify(optimization).length
        });
        this.optimizedPrompts.set(cacheKey, optimization);
        this.updateMetrics('prompt_optimization', optimization.tokenReduction);
        return optimization;
    }
    /**
     * Perform actual prompt optimization
     */
    performPromptOptimization(originalPrompt, context) {
        let optimizedPrompt = originalPrompt;
        const techniques = [];
        let tokenReduction = 0;
        // Remove redundant whitespace
        const beforeWhitespace = optimizedPrompt.length;
        optimizedPrompt = optimizedPrompt.replace(/\s+/g, ' ').trim();
        const afterWhitespace = optimizedPrompt.length;
        if (beforeWhitespace > afterWhitespace) {
            techniques.push('whitespace_optimization');
            tokenReduction += beforeWhitespace - afterWhitespace;
        }
        // Remove redundant phrases
        const redundantPhrases = [
            'please note that',
            'it is important to',
            'make sure to',
            'be sure to',
            'you should',
            'it would be good to'
        ];
        redundantPhrases.forEach(phrase => {
            const beforePhrase = optimizedPrompt.length;
            optimizedPrompt = optimizedPrompt.replace(new RegExp(phrase, 'gi'), '');
            const afterPhrase = optimizedPrompt.length;
            if (beforePhrase > afterPhrase) {
                techniques.push('redundant_phrase_removal');
                tokenReduction += beforePhrase - afterPhrase;
            }
        });
        // Optimize repetitive instructions
        if (optimizedPrompt.includes('Generate') && optimizedPrompt.split('Generate').length > 3) {
            optimizedPrompt = optimizedPrompt.replace(/Generate\s+/g, '');
            optimizedPrompt = 'Generate: ' + optimizedPrompt;
            techniques.push('instruction_consolidation');
            tokenReduction += 50; // Estimated
        }
        // Use context-aware abbreviations
        if (context.domain) {
            const domainAbbreviations = {
                'e-commerce': 'ecom',
                'project-management': 'pm',
                'customer-relationship-management': 'crm'
            };
            const abbrev = domainAbbreviations[context.domain];
            if (abbrev) {
                const beforeAbbrev = optimizedPrompt.length;
                optimizedPrompt = optimizedPrompt.replace(new RegExp(context.domain, 'g'), abbrev);
                const afterAbbrev = optimizedPrompt.length;
                if (beforeAbbrev > afterAbbrev) {
                    techniques.push('domain_abbreviation');
                    tokenReduction += beforeAbbrev - afterAbbrev;
                }
            }
        }
        // Calculate quality score (balance between reduction and clarity)
        const reductionRatio = tokenReduction / originalPrompt.length;
        const qualityScore = Math.max(0, 100 - (reductionRatio * 30)); // Penalize excessive reduction
        return {
            originalPrompt,
            optimizedPrompt,
            tokenReduction,
            qualityScore,
            optimizationTechniques: techniques
        };
    }
    /**
     * Cache LLM response with intelligent key generation
     */
    cacheResponse(prompt, response, context = {}) {
        const cacheKey = this.generateCacheKey('llm_response', prompt, context);
        this.responseCache.set(cacheKey, {
            key: cacheKey,
            value: response,
            timestamp: Date.now(),
            ttl: 1800000, // 30 minutes
            hitCount: 1,
            size: JSON.stringify(response).length
        });
        this.logInfo('Response cached', { cacheKey, responseSize: JSON.stringify(response).length });
    }
    /**
     * Retrieve cached response
     */
    getCachedResponse(prompt, context = {}) {
        const cacheKey = this.generateCacheKey('llm_response', prompt, context);
        const cached = this.responseCache.get(cacheKey);
        if (cached && !this.isCacheExpired(cached)) {
            cached.hitCount++;
            this.updateMetrics('cache_hit', 0);
            this.logInfo('Cache hit', { cacheKey, hitCount: cached.hitCount });
            return cached.value;
        }
        this.updateMetrics('cache_miss', 0);
        return null;
    }
    /**
     * Optimize agent communication by reducing redundant data transfer
     */
    optimizeAgentCommunication(data, targetAgent) {
        const optimized = { ...data };
        // Remove redundant metadata
        if (optimized.metadata) {
            delete optimized.metadata.generatedAt;
            delete optimized.metadata.generatedBy;
            delete optimized.metadata.version;
        }
        // Compress large arrays
        if (optimized.entities && Array.isArray(optimized.entities)) {
            optimized.entities = optimized.entities.map((entity) => ({
                name: entity.name,
                description: entity.description,
                fields: entity.fields?.slice(0, 10), // Limit fields for communication
                relationships: entity.relationships?.slice(0, 5) // Limit relationships
            }));
        }
        // Remove verbose descriptions for internal communication
        if (targetAgent !== 'user_facing') {
            this.removeVerboseDescriptions(optimized);
        }
        const originalSize = JSON.stringify(data).length;
        const optimizedSize = JSON.stringify(optimized).length;
        const reduction = originalSize - optimizedSize;
        this.updateMetrics('communication_optimization', reduction);
        this.logInfo('Agent communication optimized', {
            targetAgent,
            originalSize,
            optimizedSize,
            reduction,
            reductionPercentage: (reduction / originalSize) * 100
        });
        return optimized;
    }
    /**
     * Detect and eliminate redundant operations
     */
    detectRedundancy(operations) {
        const unique = [];
        const redundant = [];
        const seen = new Set();
        operations.forEach(operation => {
            const signature = this.generateOperationSignature(operation);
            if (seen.has(signature)) {
                redundant.push(operation);
            }
            else {
                seen.add(signature);
                unique.push(operation);
            }
        });
        const redundancyReduction = redundant.length;
        this.updateMetrics('redundancy_elimination', redundancyReduction);
        this.logInfo('Redundancy detection completed', {
            totalOperations: operations.length,
            uniqueOperations: unique.length,
            redundantOperations: redundant.length,
            reductionPercentage: (redundancyReduction / operations.length) * 100
        });
        return { redundant, unique };
    }
    /**
     * Optimize workflow execution order
     */
    optimizeExecutionOrder(tasks) {
        // Sort tasks by dependency and estimated execution time
        const optimized = [...tasks].sort((a, b) => {
            // Prioritize tasks with no dependencies
            const aDeps = a.dependencies?.length || 0;
            const bDeps = b.dependencies?.length || 0;
            if (aDeps !== bDeps) {
                return aDeps - bDeps;
            }
            // Then by estimated execution time (shorter first for quick wins)
            const aTime = a.estimatedTime || 0;
            const bTime = b.estimatedTime || 0;
            return aTime - bTime;
        });
        this.logInfo('Execution order optimized', {
            originalOrder: tasks.map(t => t.name),
            optimizedOrder: optimized.map(t => t.name)
        });
        return optimized;
    }
    /**
     * Get performance metrics
     */
    getMetrics() {
        // Calculate cache hit rate
        const totalCacheRequests = Array.from(this.responseCache.values())
            .reduce((sum, entry) => sum + entry.hitCount, 0);
        const cacheHits = Array.from(this.responseCache.values())
            .filter(entry => entry.hitCount > 1)
            .reduce((sum, entry) => sum + (entry.hitCount - 1), 0);
        this.metrics.cacheHitRate = totalCacheRequests > 0 ? (cacheHits / totalCacheRequests) * 100 : 0;
        // Calculate memory usage
        const promptCacheSize = Array.from(this.promptCache.values())
            .reduce((sum, entry) => sum + entry.size, 0);
        const responseCacheSize = Array.from(this.responseCache.values())
            .reduce((sum, entry) => sum + entry.size, 0);
        this.metrics.memoryUsage = promptCacheSize + responseCacheSize;
        return { ...this.metrics };
    }
    /**
     * Generate optimization report
     */
    generateOptimizationReport() {
        const metrics = this.getMetrics();
        return {
            phase: 'overall',
            originalDuration: metrics.totalExecutionTime + (metrics.tokensSaved * 0.1), // Estimated
            optimizedDuration: metrics.totalExecutionTime,
            improvement: (metrics.tokensSaved * 0.1) / (metrics.totalExecutionTime + (metrics.tokensSaved * 0.1)) * 100,
            techniques: [
                'prompt_optimization',
                'intelligent_caching',
                'redundancy_elimination',
                'communication_optimization'
            ],
            metrics
        };
    }
    /**
     * Clear all caches
     */
    clearCaches() {
        this.promptCache.clear();
        this.responseCache.clear();
        this.optimizedPrompts.clear();
        this.logInfo('All caches cleared');
    }
    /**
     * Generate cache key
     */
    generateCacheKey(type, ...inputs) {
        const combined = inputs.map(input => typeof input === 'string' ? input : JSON.stringify(input)).join('|');
        // Simple hash function for cache key
        let hash = 0;
        for (let i = 0; i < combined.length; i++) {
            const char = combined.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return `${type}_${Math.abs(hash)}`;
    }
    /**
     * Check if cache entry is expired
     */
    isCacheExpired(entry) {
        return Date.now() - entry.timestamp > entry.ttl;
    }
    /**
     * Clean up expired cache entries
     */
    cleanupExpiredCache() {
        let promptCleaned = 0;
        let responseCleaned = 0;
        // Clean prompt cache
        for (const [key, entry] of this.promptCache.entries()) {
            if (this.isCacheExpired(entry)) {
                this.promptCache.delete(key);
                promptCleaned++;
            }
        }
        // Clean response cache
        for (const [key, entry] of this.responseCache.entries()) {
            if (this.isCacheExpired(entry)) {
                this.responseCache.delete(key);
                responseCleaned++;
            }
        }
        if (promptCleaned > 0 || responseCleaned > 0) {
            this.logInfo('Cache cleanup completed', {
                promptEntriesCleaned: promptCleaned,
                responseEntriesCleaned: responseCleaned
            });
        }
    }
    /**
     * Update performance metrics
     */
    updateMetrics(operation, value) {
        switch (operation) {
            case 'prompt_optimization':
                this.metrics.tokensSaved += value;
                break;
            case 'cache_hit':
                // Cache hit rate calculated in getMetrics()
                break;
            case 'cache_miss':
                this.metrics.llmCallCount++;
                break;
            case 'communication_optimization':
                this.metrics.redundancyReduction += value;
                break;
            case 'redundancy_elimination':
                this.metrics.redundancyReduction += value;
                break;
        }
    }
    /**
     * Remove verbose descriptions for optimization
     */
    removeVerboseDescriptions(obj) {
        if (typeof obj !== 'object' || obj === null)
            return;
        for (const key in obj) {
            if (key === 'description' && typeof obj[key] === 'string' && obj[key].length > 100) {
                obj[key] = obj[key].substring(0, 50) + '...';
            }
            else if (typeof obj[key] === 'object') {
                this.removeVerboseDescriptions(obj[key]);
            }
        }
    }
    /**
     * Generate operation signature for redundancy detection
     */
    generateOperationSignature(operation) {
        const key = {
            type: operation.type,
            target: operation.target,
            method: operation.method,
            params: operation.params ? JSON.stringify(operation.params).substring(0, 100) : ''
        };
        return JSON.stringify(key);
    }
    logInfo(message, context) {
        this.logger.info(`[PerformanceOptimization] ${message}`, context);
    }
    logWarn(message, context) {
        this.logger.warn(`[PerformanceOptimization] ${message}`, context);
    }
    logError(message, context) {
        this.logger.error(`[PerformanceOptimization] ${message}`, context);
    }
}
exports.PerformanceOptimization = PerformanceOptimization;
//# sourceMappingURL=PerformanceOptimization.js.map
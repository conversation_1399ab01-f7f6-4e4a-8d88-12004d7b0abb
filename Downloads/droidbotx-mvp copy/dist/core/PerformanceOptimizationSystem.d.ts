/**
 * Performance Optimization and Scalability System
 * Implements caching layers, database query optimization, API response optimization,
 * and horizontal scaling capabilities for production-ready applications
 */
import { DatabaseSchema } from './DatabaseCodeSynchronizer';
import { OpenAPISpec } from './ContractFirstAPIGenerator';
export interface PerformanceConfiguration {
    projectPath: string;
    caching: {
        enabled: boolean;
        layers: Array<'redis' | 'memcached' | 'in-memory' | 'cdn'>;
        ttl: number;
        strategies: Array<'cache-aside' | 'write-through' | 'write-behind'>;
    };
    database: {
        optimization: boolean;
        indexing: boolean;
        queryAnalysis: boolean;
        connectionPooling: boolean;
        readReplicas: boolean;
    };
    api: {
        compression: boolean;
        pagination: boolean;
        rateLimit: boolean;
        responseOptimization: boolean;
        graphql: boolean;
    };
    scaling: {
        horizontal: boolean;
        loadBalancer: boolean;
        autoScaling: boolean;
        microservices: boolean;
    };
}
export interface PerformanceArtifact {
    type: 'cache' | 'database' | 'api' | 'middleware' | 'config' | 'monitoring';
    filePath: string;
    content: string;
    dependencies: string[];
    performance: {
        expectedImprovement: string;
        metrics: string[];
    };
}
export interface OptimizationResult {
    artifacts: PerformanceArtifact[];
    optimizations: {
        caching: Array<{
            layer: string;
            strategy: string;
            expectedSpeedup: string;
        }>;
        database: Array<{
            optimization: string;
            impact: string;
            implementation: string;
        }>;
        api: Array<{
            optimization: string;
            benefit: string;
            configuration: string;
        }>;
        scaling: Array<{
            strategy: string;
            capacity: string;
            automation: string;
        }>;
    };
    monitoring: {
        metrics: string[];
        alerts: string[];
        dashboards: string[];
    };
    recommendations: string[];
}
export declare class PerformanceOptimizationSystem {
    private logger;
    private optimizationTemplates;
    constructor();
    /**
     * Generate comprehensive performance optimization setup
     */
    generatePerformanceOptimizations(config: PerformanceConfiguration, context: {
        databaseSchema?: DatabaseSchema;
        openAPISpec?: OpenAPISpec;
        generatedCode?: Record<string, string>;
    }): Promise<OptimizationResult>;
    /**
     * Generate caching layer optimizations
     */
    private generateCachingOptimizations;
    /**
     * Generate database optimizations
     */
    private generateDatabaseOptimizations;
    /**
     * Generate API optimizations
     */
    private generateAPIOptimizations;
    /**
     * Generate scaling configurations
     */
    private generateScalingConfigurations;
    /**
     * Generate performance monitoring
     */
    private generatePerformanceMonitoring;
    /**
     * Organize optimizations by category
     */
    private organizeOptimizations;
    /**
     * Generate monitoring setup
     */
    private generateMonitoringSetup;
    /**
     * Generate performance recommendations
     */
    private generatePerformanceRecommendations;
    /**
     * Write optimization files to disk
     */
    private writeOptimizationFiles;
    private generateRedisCacheManager;
    private generateCacheMiddleware;
    private generateCacheStrategies;
    private generateCacheInvalidation;
    private generateQueryOptimizer;
    private generateConnectionPoolManager;
    private generateIndexRecommendations;
    private generateReadReplicaManager;
    private generateCompressionMiddleware;
    private generatePaginationMiddleware;
    private generateRateLimitMiddleware;
    private generateResponseOptimizer;
    private generateNginxLoadBalancer;
    private generateAdvancedHPA;
    private generateClusterManager;
    private generatePerformanceMetrics;
    private generatePerformanceProfiler;
    private generateOptimizationDocumentation;
    private initializeTemplates;
}
//# sourceMappingURL=PerformanceOptimizationSystem.d.ts.map
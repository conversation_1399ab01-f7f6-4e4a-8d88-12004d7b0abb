{"version": 3, "file": "PerformanceOptimizationSystem.d.ts", "sourceRoot": "", "sources": ["../../src/core/PerformanceOptimizationSystem.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAI1D,MAAM,WAAW,wBAAwB;IACvC,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE;QACP,OAAO,EAAE,OAAO,CAAC;QACjB,MAAM,EAAE,KAAK,CAAC,OAAO,GAAG,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC,CAAC;QAC3D,GAAG,EAAE,MAAM,CAAC;QACZ,UAAU,EAAE,KAAK,CAAC,aAAa,GAAG,eAAe,GAAG,cAAc,CAAC,CAAC;KACrE,CAAC;IACF,QAAQ,EAAE;QACR,YAAY,EAAE,OAAO,CAAC;QACtB,QAAQ,EAAE,OAAO,CAAC;QAClB,aAAa,EAAE,OAAO,CAAC;QACvB,iBAAiB,EAAE,OAAO,CAAC;QAC3B,YAAY,EAAE,OAAO,CAAC;KACvB,CAAC;IACF,GAAG,EAAE;QACH,WAAW,EAAE,OAAO,CAAC;QACrB,UAAU,EAAE,OAAO,CAAC;QACpB,SAAS,EAAE,OAAO,CAAC;QACnB,oBAAoB,EAAE,OAAO,CAAC;QAC9B,OAAO,EAAE,OAAO,CAAC;KAClB,CAAC;IACF,OAAO,EAAE;QACP,UAAU,EAAE,OAAO,CAAC;QACpB,YAAY,EAAE,OAAO,CAAC;QACtB,WAAW,EAAE,OAAO,CAAC;QACrB,aAAa,EAAE,OAAO,CAAC;KACxB,CAAC;CACH;AAED,MAAM,WAAW,mBAAmB;IAClC,IAAI,EAAE,OAAO,GAAG,UAAU,GAAG,KAAK,GAAG,YAAY,GAAG,QAAQ,GAAG,YAAY,CAAC;IAC5E,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,WAAW,EAAE;QACX,mBAAmB,EAAE,MAAM,CAAC;QAC5B,OAAO,EAAE,MAAM,EAAE,CAAC;KACnB,CAAC;CACH;AAED,MAAM,WAAW,kBAAkB;IACjC,SAAS,EAAE,mBAAmB,EAAE,CAAC;IACjC,aAAa,EAAE;QACb,OAAO,EAAE,KAAK,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;YACd,QAAQ,EAAE,MAAM,CAAC;YACjB,eAAe,EAAE,MAAM,CAAC;SACzB,CAAC,CAAC;QACH,QAAQ,EAAE,KAAK,CAAC;YACd,YAAY,EAAE,MAAM,CAAC;YACrB,MAAM,EAAE,MAAM,CAAC;YACf,cAAc,EAAE,MAAM,CAAC;SACxB,CAAC,CAAC;QACH,GAAG,EAAE,KAAK,CAAC;YACT,YAAY,EAAE,MAAM,CAAC;YACrB,OAAO,EAAE,MAAM,CAAC;YAChB,aAAa,EAAE,MAAM,CAAC;SACvB,CAAC,CAAC;QACH,OAAO,EAAE,KAAK,CAAC;YACb,QAAQ,EAAE,MAAM,CAAC;YACjB,QAAQ,EAAE,MAAM,CAAC;YACjB,UAAU,EAAE,MAAM,CAAC;SACpB,CAAC,CAAC;KACJ,CAAC;IACF,UAAU,EAAE;QACV,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,MAAM,EAAE,MAAM,EAAE,CAAC;QACjB,UAAU,EAAE,MAAM,EAAE,CAAC;KACtB,CAAC;IACF,eAAe,EAAE,MAAM,EAAE,CAAC;CAC3B;AAED,qBAAa,6BAA6B;IACxC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,qBAAqB,CAAsB;;IAQnD;;OAEG;IACG,gCAAgC,CACpC,MAAM,EAAE,wBAAwB,EAChC,OAAO,EAAE;QACP,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,WAAW,CAAC,EAAE,WAAW,CAAC;QAC1B,aAAa,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACxC,GACA,OAAO,CAAC,kBAAkB,CAAC;IAsE9B;;OAEG;YACW,4BAA4B;IA2D1C;;OAEG;YACW,6BAA6B;IA6D3C;;OAEG;YACW,wBAAwB;IA+DtC;;OAEG;YACW,6BAA6B;IAiD3C;;OAEG;YACW,6BAA6B;IA8B3C;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAwD7B;;OAEG;IACH,OAAO,CAAC,uBAAuB;IA4B/B;;OAEG;IACH,OAAO,CAAC,kCAAkC;IAuB1C;;OAEG;YACW,sBAAsB;IAwBpC,OAAO,CAAC,yBAAyB;IAkCjC,OAAO,CAAC,uBAAuB;IAI/B,OAAO,CAAC,uBAAuB;IAI/B,OAAO,CAAC,yBAAyB;IAIjC,OAAO,CAAC,sBAAsB;IAI9B,OAAO,CAAC,6BAA6B;IAIrC,OAAO,CAAC,4BAA4B;IAIpC,OAAO,CAAC,0BAA0B;IAIlC,OAAO,CAAC,6BAA6B;IAIrC,OAAO,CAAC,4BAA4B;IAIpC,OAAO,CAAC,2BAA2B;IAInC,OAAO,CAAC,yBAAyB;IAIjC,OAAO,CAAC,yBAAyB;IAIjC,OAAO,CAAC,mBAAmB;IAI3B,OAAO,CAAC,sBAAsB;IAI9B,OAAO,CAAC,0BAA0B;IAIlC,OAAO,CAAC,2BAA2B;IAInC,OAAO,CAAC,iCAAiC;IAIzC,OAAO,CAAC,mBAAmB;CAM5B"}
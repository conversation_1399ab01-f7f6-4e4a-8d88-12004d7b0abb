/**
 * Production Deployment Infrastructure
 * Implements automated deployment pipelines, containerization, and orchestration
 * Ensures seamless production deployments with zero-downtime and rollback capabilities
 */
export interface DeploymentConfiguration {
    projectPath: string;
    projectName: string;
    environment: 'staging' | 'production';
    containerization: {
        enabled: boolean;
        registry: string;
        baseImage: string;
        multiStage: boolean;
    };
    orchestration: {
        platform: 'kubernetes' | 'docker-swarm' | 'ecs';
        replicas: number;
        autoScaling: boolean;
        loadBalancer: boolean;
    };
    cicd: {
        provider: 'github-actions' | 'gitlab-ci' | 'jenkins' | 'azure-devops';
        stages: string[];
        deploymentStrategy: 'rolling' | 'blue-green' | 'canary';
    };
    infrastructure: {
        provider: 'aws' | 'gcp' | 'azure' | 'digitalocean';
        database: 'rds' | 'cloud-sql' | 'azure-sql' | 'managed-postgres';
        storage: 's3' | 'gcs' | 'azure-blob' | 'spaces';
        cdn: boolean;
    };
}
export interface DeploymentArtifact {
    type: 'dockerfile' | 'k8s-manifest' | 'ci-config' | 'terraform' | 'helm-chart' | 'script';
    filePath: string;
    content: string;
    dependencies: string[];
    environment?: string;
}
export interface DeploymentResult {
    artifacts: DeploymentArtifact[];
    infrastructure: {
        terraformModules: string[];
        kubernetesManifests: string[];
        helmCharts: string[];
        cicdPipelines: string[];
    };
    deployment: {
        strategy: string;
        rollbackPlan: string;
        healthChecks: string[];
        monitoring: string[];
    };
    security: {
        secrets: string[];
        rbac: string[];
        networkPolicies: string[];
    };
    recommendations: string[];
}
export declare class ProductionDeploymentInfrastructure {
    private logger;
    private deploymentTemplates;
    constructor();
    /**
     * Generate comprehensive production deployment infrastructure
     */
    generateDeploymentInfrastructure(config: DeploymentConfiguration): Promise<DeploymentResult>;
    /**
     * Generate containerization artifacts
     */
    private generateContainerization;
    /**
     * Generate orchestration manifests
     */
    private generateOrchestration;
    /**
     * Generate CI/CD pipelines
     */
    private generateCICDPipelines;
    /**
     * Generate infrastructure as code
     */
    private generateInfrastructureAsCode;
    /**
     * Generate deployment scripts
     */
    private generateDeploymentScripts;
    /**
     * Generate security configurations
     */
    private generateSecurityConfigurations;
    /**
     * Organize infrastructure components
     */
    private organizeInfrastructureComponents;
    /**
     * Generate deployment strategy
     */
    private generateDeploymentStrategy;
    /**
     * Generate security configuration
     */
    private generateSecurityConfiguration;
    /**
     * Generate deployment recommendations
     */
    private generateDeploymentRecommendations;
    /**
     * Write deployment files to disk
     */
    private writeDeploymentFiles;
    private generateProductionDockerfile;
    private generateDevelopmentDockerfile;
    private generateDockerCompose;
    private generateProductionDockerCompose;
    private generateDockerIgnore;
    private generateKubernetesDeployment;
    private generateKubernetesService;
    private generateKubernetesIngress;
    private generateKubernetesConfigMap;
    private generateKubernetesSecrets;
    private generateKubernetesHPA;
    private generateHelmChart;
    private generateHelmValues;
    private generateGitHubActionsCIPipeline;
    private generateGitHubActionsCDPipeline;
    private generateGitLabCIPipeline;
    private generateJenkinsPipeline;
    private generateTerraformMain;
    private generateTerraformVariables;
    private generateTerraformOutputs;
    private generateTerraformEnvironmentVars;
    private generateDeploymentScript;
    private generateRollbackScript;
    private generateHealthCheckScript;
    private generateMigrationScript;
    private generateNetworkPolicy;
    private generateRBACConfiguration;
    private generateSecurityContext;
    private generateDeploymentDocumentation;
    private initializeTemplates;
}
//# sourceMappingURL=ProductionDeploymentInfrastructure.d.ts.map
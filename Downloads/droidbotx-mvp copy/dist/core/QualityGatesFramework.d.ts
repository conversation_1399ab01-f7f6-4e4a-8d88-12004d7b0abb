export interface QualityGate {
    name: string;
    phase: string;
    type: 'validation' | 'verification' | 'compliance' | 'performance';
    criteria: QualityCriteria[];
    weight: number;
    mandatory: boolean;
}
export interface QualityCriteria {
    name: string;
    description: string;
    validator: (data: any) => QualityResult;
    threshold: number;
    weight: number;
}
export interface QualityResult {
    passed: boolean;
    score: number;
    message: string;
    details?: any;
}
export interface PhaseValidationResult {
    phase: string;
    overallScore: number;
    passed: boolean;
    gateResults: Map<string, QualityResult>;
    criticalIssues: QualityIssue[];
    warnings: QualityIssue[];
    recommendations: string[];
}
export interface QualityIssue {
    type: 'critical' | 'high' | 'medium' | 'low';
    gate: string;
    message: string;
    impact: string;
    resolution: string;
}
export interface CrossPhaseValidation {
    planningToBusinessLogic: QualityResult;
    businessLogicToGenerate: QualityResult;
    generateToDatabase: QualityResult;
    databaseToSecurity: QualityResult;
    securityToTesting: QualityResult;
    overallConsistency: number;
}
/**
 * Enhanced Quality Gates Framework for Phase 3
 * Implements comprehensive validation between phases
 */
export declare class QualityGatesFramework {
    private logger;
    private qualityGates;
    private phaseResults;
    private crossPhaseValidation;
    private adaptiveThresholds;
    private domainContext;
    constructor();
    /**
     * Initialize adaptive thresholds for different domains
     */
    private initializeAdaptiveThresholds;
    /**
     * Set domain context for adaptive scoring
     */
    setDomainContext(domain: string): void;
    /**
     * Get adaptive threshold for current domain
     */
    private getAdaptiveThreshold;
    /**
     * Initialize quality gates for each phase
     */
    private initializeQualityGates;
    /**
     * Validate phase against quality gates
     */
    validatePhase(phase: string, phaseData: any): PhaseValidationResult;
    /**
     * Validate cross-phase consistency
     */
    validateCrossPhaseConsistency(planningData: any, businessLogicData: any, generateData: any, databaseData: any, securityData: any): CrossPhaseValidation;
    /**
     * Get production readiness score
     */
    getProductionReadinessScore(): number;
    /**
     * Validation methods for each criteria
     */
    private validateRequirementsCompleteness;
    private validateArchitectureClarity;
    private validateEntityRelationships;
    private validateAPICompleteness;
    private validateTypeScriptCompilation;
    private validateImportConsistency;
    private validateForeignKeyConsistency;
    private validateJWTImplementation;
    private validateTestCoverage;
    private validatePlanningToBusinessLogic;
    /**
     * Get expected APIs per requirement based on domain
     */
    private getExpectedAPIsPerRequirement;
    private validateBusinessLogicToGenerate;
    private validateGenerateToDatabase;
    private validateDatabaseToSecurity;
    private validateSecurityToTesting;
    /**
     * Get all phase results
     */
    getPhaseResults(): Map<string, PhaseValidationResult>;
    /**
     * Apply fallback validation for failed mandatory gates
     */
    private applyFallbackValidation;
    /**
     * Fallback validation methods
     */
    private fallbackRequirementsValidation;
    private fallbackArchitectureValidation;
    private fallbackEntityValidation;
    private fallbackAPIValidation;
    private fallbackCodeQualityValidation;
    /**
     * Get domain-specific weight multipliers for cross-phase validation
     */
    private getDomainWeightMultipliers;
    /**
     * Get domain-specific consistency boost
     */
    private getDomainConsistencyBoost;
    /**
     * Get cross-phase validation results
     */
    getCrossPhaseValidation(): CrossPhaseValidation | null;
    private logInfo;
    private logWarn;
    private logError;
}
//# sourceMappingURL=QualityGatesFramework.d.ts.map
/**
 * Security Hardening and Compliance System
 * Implements comprehensive security measures including authentication, authorization,
 * input validation, SQL injection prevention, XSS protection, and compliance standards
 */
import { DatabaseSchema } from './DatabaseCodeSynchronizer';
import { OpenAPISpec } from './ContractFirstAPIGenerator';
export interface SecurityConfiguration {
    projectPath: string;
    authentication: {
        strategy: 'jwt' | 'oauth2' | 'saml' | 'multi-factor';
        providers: string[];
        sessionManagement: boolean;
        passwordPolicy: {
            minLength: number;
            requireSpecialChars: boolean;
            requireNumbers: boolean;
            requireUppercase: boolean;
        };
    };
    authorization: {
        rbac: boolean;
        abac: boolean;
        permissions: string[];
        roles: string[];
    };
    dataProtection: {
        encryption: {
            atRest: boolean;
            inTransit: boolean;
            algorithm: string;
        };
        pii: boolean;
        gdpr: boolean;
        dataRetention: boolean;
    };
    inputValidation: {
        sanitization: boolean;
        validation: boolean;
        sqlInjectionPrevention: boolean;
        xssProtection: boolean;
        csrfProtection: boolean;
    };
    compliance: {
        standards: Array<'OWASP' | 'SOC2' | 'GDPR' | 'HIPAA' | 'PCI-DSS'>;
        auditing: boolean;
        logging: boolean;
    };
}
export interface SecurityArtifact {
    type: 'auth' | 'validation' | 'middleware' | 'config' | 'policy' | 'audit';
    filePath: string;
    content: string;
    dependencies: string[];
    security: {
        threats: string[];
        mitigations: string[];
        compliance: string[];
    };
}
export interface SecurityResult {
    artifacts: SecurityArtifact[];
    security: {
        authentication: Array<{
            method: string;
            implementation: string;
            strength: string;
        }>;
        authorization: Array<{
            model: string;
            enforcement: string;
            granularity: string;
        }>;
        dataProtection: Array<{
            protection: string;
            method: string;
            compliance: string;
        }>;
        inputValidation: Array<{
            validation: string;
            protection: string;
            coverage: string;
        }>;
    };
    compliance: {
        standards: string[];
        requirements: string[];
        auditing: string[];
    };
    vulnerabilities: Array<{
        category: string;
        risk: 'low' | 'medium' | 'high' | 'critical';
        mitigation: string;
        status: 'protected' | 'needs-attention';
    }>;
    recommendations: string[];
}
export declare class SecurityHardeningSystem {
    private logger;
    private securityTemplates;
    private complianceStandards;
    constructor();
    /**
     * Generate comprehensive security hardening setup
     */
    generateSecurityHardening(config: SecurityConfiguration, context: {
        databaseSchema?: DatabaseSchema;
        openAPISpec?: OpenAPISpec;
        generatedCode?: Record<string, string>;
    }): Promise<SecurityResult>;
    /**
     * Generate authentication system
     */
    private generateAuthenticationSystem;
    /**
     * Generate authorization system
     */
    private generateAuthorizationSystem;
    /**
     * Generate input validation and sanitization
     */
    private generateInputValidation;
    /**
     * Generate data protection measures
     */
    private generateDataProtection;
    /**
     * Generate security middleware
     */
    private generateSecurityMiddleware;
    /**
     * Generate compliance configurations
     */
    private generateComplianceConfigurations;
    /**
     * Generate security auditing
     */
    private generateSecurityAuditing;
    /**
     * Organize security components
     */
    private organizeSecurityComponents;
    /**
     * Generate compliance mapping
     */
    private generateComplianceMapping;
    /**
     * Assess vulnerabilities
     */
    private assessVulnerabilities;
    /**
     * Generate security recommendations
     */
    private generateSecurityRecommendations;
    /**
     * Write security files to disk
     */
    private writeSecurityFiles;
    private generateJWTAuthService;
    private generatePasswordService;
    private generateMFAService;
    private generateSessionManager;
    private generateRBACService;
    private generateABACService;
    private generateAuthorizationMiddleware;
    private generateInputValidationService;
    private generateSQLInjectionPrevention;
    private generateXSSProtection;
    private generateCSRFProtection;
    private generateEncryptionService;
    private generatePIIProtection;
    private generateGDPRCompliance;
    private generateSecurityHeadersMiddleware;
    private generateSecurityRateLimitMiddleware;
    private generateComplianceConfiguration;
    private generateSecurityAuditLogger;
    private generateSecurityEventMonitor;
    private generateSecurityDocumentation;
    private initializeTemplates;
    private initializeComplianceStandards;
}
//# sourceMappingURL=SecurityHardeningSystem.d.ts.map
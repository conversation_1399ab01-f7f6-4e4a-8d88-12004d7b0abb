{"version": 3, "file": "SecurityHardeningSystem.d.ts", "sourceRoot": "", "sources": ["../../src/core/SecurityHardeningSystem.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAI1D,MAAM,WAAW,qBAAqB;IACpC,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE;QACd,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,cAAc,CAAC;QACrD,SAAS,EAAE,MAAM,EAAE,CAAC;QACpB,iBAAiB,EAAE,OAAO,CAAC;QAC3B,cAAc,EAAE;YACd,SAAS,EAAE,MAAM,CAAC;YAClB,mBAAmB,EAAE,OAAO,CAAC;YAC7B,cAAc,EAAE,OAAO,CAAC;YACxB,gBAAgB,EAAE,OAAO,CAAC;SAC3B,CAAC;KACH,CAAC;IACF,aAAa,EAAE;QACb,IAAI,EAAE,OAAO,CAAC;QACd,IAAI,EAAE,OAAO,CAAC;QACd,WAAW,EAAE,MAAM,EAAE,CAAC;QACtB,KAAK,EAAE,MAAM,EAAE,CAAC;KACjB,CAAC;IACF,cAAc,EAAE;QACd,UAAU,EAAE;YACV,MAAM,EAAE,OAAO,CAAC;YAChB,SAAS,EAAE,OAAO,CAAC;YACnB,SAAS,EAAE,MAAM,CAAC;SACnB,CAAC;QACF,GAAG,EAAE,OAAO,CAAC;QACb,IAAI,EAAE,OAAO,CAAC;QACd,aAAa,EAAE,OAAO,CAAC;KACxB,CAAC;IACF,eAAe,EAAE;QACf,YAAY,EAAE,OAAO,CAAC;QACtB,UAAU,EAAE,OAAO,CAAC;QACpB,sBAAsB,EAAE,OAAO,CAAC;QAChC,aAAa,EAAE,OAAO,CAAC;QACvB,cAAc,EAAE,OAAO,CAAC;KACzB,CAAC;IACF,UAAU,EAAE;QACV,SAAS,EAAE,KAAK,CAAC,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;QAClE,QAAQ,EAAE,OAAO,CAAC;QAClB,OAAO,EAAE,OAAO,CAAC;KAClB,CAAC;CACH;AAED,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,GAAG,YAAY,GAAG,YAAY,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC;IAC3E,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,QAAQ,EAAE;QACR,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,WAAW,EAAE,MAAM,EAAE,CAAC;QACtB,UAAU,EAAE,MAAM,EAAE,CAAC;KACtB,CAAC;CACH;AAED,MAAM,WAAW,cAAc;IAC7B,SAAS,EAAE,gBAAgB,EAAE,CAAC;IAC9B,QAAQ,EAAE;QACR,cAAc,EAAE,KAAK,CAAC;YACpB,MAAM,EAAE,MAAM,CAAC;YACf,cAAc,EAAE,MAAM,CAAC;YACvB,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC,CAAC;QACH,aAAa,EAAE,KAAK,CAAC;YACnB,KAAK,EAAE,MAAM,CAAC;YACd,WAAW,EAAE,MAAM,CAAC;YACpB,WAAW,EAAE,MAAM,CAAC;SACrB,CAAC,CAAC;QACH,cAAc,EAAE,KAAK,CAAC;YACpB,UAAU,EAAE,MAAM,CAAC;YACnB,MAAM,EAAE,MAAM,CAAC;YACf,UAAU,EAAE,MAAM,CAAC;SACpB,CAAC,CAAC;QACH,eAAe,EAAE,KAAK,CAAC;YACrB,UAAU,EAAE,MAAM,CAAC;YACnB,UAAU,EAAE,MAAM,CAAC;YACnB,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC,CAAC;KACJ,CAAC;IACF,UAAU,EAAE;QACV,SAAS,EAAE,MAAM,EAAE,CAAC;QACpB,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,QAAQ,EAAE,MAAM,EAAE,CAAC;KACpB,CAAC;IACF,eAAe,EAAE,KAAK,CAAC;QACrB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;QAC7C,UAAU,EAAE,MAAM,CAAC;QACnB,MAAM,EAAE,WAAW,GAAG,iBAAiB,CAAC;KACzC,CAAC,CAAC;IACH,eAAe,EAAE,MAAM,EAAE,CAAC;CAC3B;AAED,qBAAa,uBAAuB;IAClC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,iBAAiB,CAAsB;IAC/C,OAAO,CAAC,mBAAmB,CAAmB;;IAU9C;;OAEG;IACG,yBAAyB,CAC7B,MAAM,EAAE,qBAAqB,EAC7B,OAAO,EAAE;QACP,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,WAAW,CAAC,EAAE,WAAW,CAAC;QAC1B,aAAa,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACxC,GACA,OAAO,CAAC,cAAc,CAAC;IA4E1B;;OAEG;YACW,4BAA4B;IAmE1C;;OAEG;YACW,2BAA2B;IAoDzC;;OAEG;YACW,uBAAuB;IA+DrC;;OAEG;YACW,sBAAsB;IAsDpC;;OAEG;YACW,0BAA0B;IAgCxC;;OAEG;YACW,gCAAgC;IAuB9C;;OAEG;YACW,wBAAwB;IAkCtC;;OAEG;IACH,OAAO,CAAC,0BAA0B;IAiClC;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAmBjC;;OAEG;YACW,qBAAqB;IA0BnC;;OAEG;IACH,OAAO,CAAC,+BAA+B;IAgBvC;;OAEG;YACW,kBAAkB;IAwBhC,OAAO,CAAC,sBAAsB;IAyC9B,OAAO,CAAC,uBAAuB;IAI/B,OAAO,CAAC,kBAAkB;IAI1B,OAAO,CAAC,sBAAsB;IAI9B,OAAO,CAAC,mBAAmB;IAI3B,OAAO,CAAC,mBAAmB;IAI3B,OAAO,CAAC,+BAA+B;IAIvC,OAAO,CAAC,8BAA8B;IAItC,OAAO,CAAC,8BAA8B;IAItC,OAAO,CAAC,qBAAqB;IAI7B,OAAO,CAAC,sBAAsB;IAI9B,OAAO,CAAC,yBAAyB;IAIjC,OAAO,CAAC,qBAAqB;IAI7B,OAAO,CAAC,sBAAsB;IAI9B,OAAO,CAAC,iCAAiC;IAIzC,OAAO,CAAC,mCAAmC;IAI3C,OAAO,CAAC,+BAA+B;IAIvC,OAAO,CAAC,2BAA2B;IAInC,OAAO,CAAC,4BAA4B;IAIpC,OAAO,CAAC,6BAA6B;IAIrC,OAAO,CAAC,mBAAmB;IAO3B,OAAO,CAAC,6BAA6B;CAiBtC"}
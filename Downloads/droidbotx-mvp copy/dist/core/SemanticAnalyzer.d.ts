import { BaseAgent } from './BaseAgent';
export interface BusinessDomainAnalysis {
    domain: string;
    confidence: number;
    entities: BusinessEntity[];
    workflows: BusinessWorkflow[];
    userRoles: UserRole[];
    technicalRequirements: TechnicalRequirement[];
    codeGenerationContext: CodeGenerationContext;
}
export interface BusinessEntity {
    name: string;
    description: string;
    fields: EntityField[];
    relationships: EntityRelationship[];
    operations: EntityOperation[];
    validations: EntityValidation[];
}
export interface EntityField {
    name: string;
    type: string;
    required: boolean;
    description: string;
    constraints?: string[];
}
export interface EntityRelationship {
    type: 'oneToMany' | 'manyToOne' | 'manyToMany' | 'oneToOne';
    target: string;
    description: string;
    foreignKey?: string;
}
export interface EntityOperation {
    name: string;
    type: 'create' | 'read' | 'update' | 'delete' | 'custom';
    description: string;
    parameters: OperationParameter[];
    businessRules: string[];
}
export interface OperationParameter {
    name: string;
    type: string;
    required: boolean;
    description: string;
}
export interface EntityValidation {
    field: string;
    rule: string;
    message: string;
}
export interface BusinessWorkflow {
    name: string;
    description: string;
    steps: WorkflowStep[];
    entities: string[];
    userRoles: string[];
}
export interface WorkflowStep {
    name: string;
    description: string;
    action: string;
    conditions: string[];
    nextSteps: string[];
}
export interface UserRole {
    name: string;
    description: string;
    permissions: Permission[];
    accessLevel: 'admin' | 'manager' | 'user' | 'guest';
}
export interface Permission {
    resource: string;
    actions: string[];
    conditions?: string[];
}
export interface TechnicalRequirement {
    category: 'frontend' | 'backend' | 'database' | 'integration' | 'security';
    requirement: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    implementation: string;
}
export interface CodeGenerationContext {
    primaryFrameworks: {
        frontend: string;
        backend: string;
        database: string;
    };
    architecturalPatterns: string[];
    securityRequirements: string[];
    performanceRequirements: string[];
    integrationRequirements: string[];
}
export declare class SemanticAnalyzer extends BaseAgent {
    constructor();
    canHandle(task: any): boolean;
    execute(task: any): Promise<any>;
    /**
     * Analyze requirements to extract business domain and entities
     */
    analyzeBusinessDomain(projectName: string, description: string, requirements: string[]): Promise<BusinessDomainAnalysis>;
    private buildDomainAnalysisPrompt;
    private parseAnalysisResponse;
    /**
     * Repair truncated or malformed JSON responses
     */
    private repairTruncatedJSON;
}
//# sourceMappingURL=SemanticAnalyzer.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SemanticAnalyzer = void 0;
const BaseAgent_1 = require("./BaseAgent");
class SemanticAnalyzer extends BaseAgent_1.BaseAgent {
    constructor() {
        super('SemanticAnalyzer', 'Performs deep semantic analysis of requirements to understand business domains', `You are an expert business analyst and software architect specializing in semantic analysis of software requirements.

Your role is to perform deep semantic analysis of user requirements to understand:
1. The true business domain and context
2. Core business entities and their relationships
3. Business workflows and processes
4. User roles and permissions
5. Technical requirements and constraints

You must distinguish between different business domains accurately:
- E-learning platforms (courses, lessons, students, instructors, assignments)
- E-commerce systems (products, orders, customers, payments, inventory)
- Healthcare systems (patients, appointments, medical records, treatments)
- Financial systems (accounts, transactions, portfolios, compliance)
- Project management (projects, tasks, teams, milestones, resources)

Always provide structured, detailed analysis that enables accurate code generation.`);
    }
    canHandle(task) {
        return task.type === 'semantic_analysis';
    }
    async execute(task) {
        // Implementation will be added in next step
        return { success: true, data: {} };
    }
    /**
     * Analyze requirements to extract business domain and entities
     */
    async analyzeBusinessDomain(projectName, description, requirements) {
        const analysisPrompt = this.buildDomainAnalysisPrompt(projectName, description, requirements);
        try {
            const analysisResponse = await this.generateSingleLLMResponse(analysisPrompt, {
                temperature: 0.3,
                maxTokens: 8000 // Increased token limit to handle larger responses
            });
            const analysis = this.parseAnalysisResponse(analysisResponse);
            this.logInfo('Business domain analysis completed', {
                domain: analysis.domain,
                confidence: analysis.confidence,
                entitiesCount: analysis.entities.length,
                workflowsCount: analysis.workflows.length
            });
            return analysis;
        }
        catch (error) {
            this.logError('Failed to analyze business domain', { error: error instanceof Error ? error.message : String(error) });
            throw new Error(`Semantic analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    buildDomainAnalysisPrompt(projectName, description, requirements) {
        return `You are an expert business domain analyst. Perform comprehensive semantic analysis of this software project to identify the PRIMARY BUSINESS DOMAIN.

PROJECT: ${projectName}
DESCRIPTION: ${description}

REQUIREMENTS:
${requirements.map((req, index) => `${index + 1}. ${req}`).join('\n')}

DOMAIN IDENTIFICATION APPROACH:
You should identify the PRIMARY BUSINESS DOMAIN based on the core functionality and purpose of the application.

COMMON DOMAINS (for reference, but not limited to):
- HEALTHCARE: Patient care, medical records, doctors, appointments, prescriptions, treatments
- E-COMMERCE: Products, orders, customers, shopping, inventory, payments, catalog, retail
- E-LEARNING: Courses, lessons, students, instructors, assignments, education, learning
- FINANCE: Banking, transactions, accounts, investments, payments, financial planning
- PROJECT-MANAGEMENT: Tasks, projects, teams, milestones, resource allocation, planning
- SOCIAL: Social networking, messaging, posts, friends, communities, social interaction
- CONTENT-MANAGEMENT: Articles, blogs, content creation, publishing, media management
- QR-CODE: QR code generation, scanning, barcode reading, camera scanning, code history
- UTILITY: Tools, converters, calculators, generators, scanners, productivity utilities
- GAMING: Games, players, scores, levels, achievements, game mechanics
- IOT: Devices, sensors, monitoring, automation, smart home, industrial control
- LOGISTICS: Shipping, tracking, warehouses, inventory, supply chain, delivery
- REAL-ESTATE: Properties, listings, agents, buyers, rentals, property management
- FOOD-SERVICE: Restaurants, menus, orders, delivery, recipes, kitchen management
- TRAVEL: Bookings, hotels, flights, itineraries, destinations, travel planning
- FITNESS: Workouts, exercises, tracking, goals, nutrition, health monitoring
- ENTERTAINMENT: Movies, music, shows, events, tickets, streaming, media
- COMMUNICATION: Messaging, chat, video calls, notifications, collaboration
- PRODUCTIVITY: Task management, notes, calendars, reminders, organization
- CUSTOM: Any other domain not listed above - describe it based on the application's core purpose

IMPORTANT: If the application doesn't clearly fit into any common domain, create a CUSTOM domain name that accurately describes the application's primary purpose (e.g., "qr-code-scanner", "image-processing", "data-visualization", "file-converter", etc.)

CRITICAL: Look at the CORE BUSINESS PURPOSE, not just management features. For example:
- "Patient management" = HEALTHCARE (managing patients, not projects)
- "Course management" = E-LEARNING (managing courses, not projects)
- "Product management" = E-COMMERCE (managing products, not projects)

Analyze and provide a detailed JSON response with the following structure:

{
  "domain": "primary business domain (use existing domain or create custom domain name that describes the application's core purpose)",
  "confidence": "confidence score 0-1",
  "entities": [
    {
      "name": "EntityName",
      "description": "detailed description",
      "fields": [
        {
          "name": "fieldName",
          "type": "string|number|boolean|date|uuid|text|json",
          "required": true|false,
          "description": "field purpose",
          "constraints": ["constraint1", "constraint2"]
        }
      ],
      "relationships": [
        {
          "type": "oneToMany|manyToOne|manyToMany|oneToOne",
          "target": "TargetEntity",
          "description": "relationship description",
          "foreignKey": "field_name"
        }
      ],
      "operations": [
        {
          "name": "operationName",
          "type": "create|read|update|delete|custom",
          "description": "operation purpose",
          "parameters": [
            {
              "name": "paramName",
              "type": "string",
              "required": true,
              "description": "parameter purpose"
            }
          ],
          "businessRules": ["rule1", "rule2"]
        }
      ],
      "validations": [
        {
          "field": "fieldName",
          "rule": "validation rule",
          "message": "error message"
        }
      ]
    }
  ],
  "workflows": [
    {
      "name": "WorkflowName",
      "description": "workflow description",
      "steps": [
        {
          "name": "stepName",
          "description": "step description",
          "action": "action to perform",
          "conditions": ["condition1"],
          "nextSteps": ["nextStep1"]
        }
      ],
      "entities": ["Entity1", "Entity2"],
      "userRoles": ["Role1", "Role2"]
    }
  ],
  "userRoles": [
    {
      "name": "RoleName",
      "description": "role description",
      "permissions": [
        {
          "resource": "EntityName",
          "actions": ["create", "read", "update", "delete"],
          "conditions": ["condition if any"]
        }
      ],
      "accessLevel": "admin|manager|user|guest"
    }
  ],
  "technicalRequirements": [
    {
      "category": "frontend|backend|database|integration|security",
      "requirement": "specific requirement",
      "priority": "critical|high|medium|low",
      "implementation": "how to implement"
    }
  ],
  "codeGenerationContext": {
    "primaryFrameworks": {
      "frontend": "React with TypeScript",
      "backend": "Express.js with TypeScript",
      "database": "PostgreSQL"
    },
    "architecturalPatterns": ["MVC", "Repository Pattern", "Service Layer"],
    "securityRequirements": ["JWT Authentication", "Role-based Access Control"],
    "performanceRequirements": ["Database Indexing", "API Caching"],
    "integrationRequirements": ["REST APIs", "WebSocket for real-time features"]
  }
}

CRITICAL INSTRUCTIONS:
1. DOMAIN IDENTIFICATION: Analyze the CORE BUSINESS PURPOSE from the description and requirements
   - If managing patients/doctors/medical → HEALTHCARE
   - If managing products/orders/customers → E-COMMERCE
   - If managing courses/students/lessons → E-LEARNING
   - If managing accounts/transactions/payments → FINANCE
   - Only use PROJECT-MANAGEMENT if actually managing projects/tasks/teams

2. ENTITY EXTRACTION: Generate domain-specific entities based on the identified domain
   - Healthcare: Patient, Doctor, Appointment, Prescription, MedicalRecord, Treatment
   - E-commerce: Product, Order, Customer, Payment, Inventory, Category
   - E-learning: Course, Lesson, Student, Instructor, Assignment, Enrollment
   - Finance: Account, Transaction, Payment, Investment, Budget

3. BUSINESS OPERATIONS: Define realistic operations specific to the application's purpose (not generic CRUD)
4. WORKFLOWS: Create workflows that match the actual user journey and application flow
5. RELATIONSHIPS: Ensure entities have logical relationships based on the application's data model
6. CONFIDENCE: Set high confidence (0.8+) when the application's purpose is clearly identifiable
7. FOCUS ON REQUIREMENTS: Base your analysis entirely on what the user actually requested, not on predefined templates
8. AVOID IRRELEVANT ENTITIES: Don't add entities that aren't directly related to the application's core functionality

EXAMPLES:
- "Patient management system" → domain: "healthcare", entities: [Patient, Doctor, Appointment]
- "Online store platform" → domain: "e-commerce", entities: [Product, Order, Customer]
- "Learning management system" → domain: "e-learning", entities: [Course, Student, Instructor]
- "QR code reader and generator" → domain: "qr-code-scanner", entities: [QRCode, ScanHistory, ExportRecord]
- "Weather monitoring app" → domain: "weather-tracking", entities: [WeatherData, Location, Forecast]
- "Recipe sharing platform" → domain: "recipe-sharing", entities: [Recipe, Ingredient, User, Review]
- "Expense tracker" → domain: "expense-tracking", entities: [Expense, Category, Budget, Report]
- "Inventory scanner" → domain: "inventory-management", entities: [Item, Scan, Location, Stock]
- "Photo gallery manager" → domain: "photo-management", entities: [Photo, Album, Tag, Collection]
- "QR code reader and generator" → domain: "qr-code", entities: [QRCode, ScanHistory, ExportRecord]
- "Barcode scanner application" → domain: "qr-code", entities: [Barcode, ScanSession, Product]
- "Calculator tool" → domain: "utility", entities: [Calculation, History, Settings]

Return ONLY the JSON response, no additional text.`;
    }
    parseAnalysisResponse(response) {
        try {
            // Clean the response to extract JSON
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No valid JSON found in analysis response');
            }
            let jsonString = jsonMatch[0];
            // Try to parse the JSON directly first
            try {
                const analysis = JSON.parse(jsonString);
                // Validate required fields
                if (!analysis.domain || !analysis.entities || !Array.isArray(analysis.entities)) {
                    throw new Error('Invalid analysis structure: missing required fields');
                }
                return analysis;
            }
            catch (parseError) {
                // If direct parsing fails, try to repair the JSON
                this.logInfo('Direct JSON parsing failed, attempting repair', {
                    error: parseError instanceof Error ? parseError.message : String(parseError)
                });
                const repairedJson = this.repairTruncatedJSON(jsonString);
                const analysis = JSON.parse(repairedJson);
                // Validate required fields
                if (!analysis.domain || !analysis.entities || !Array.isArray(analysis.entities)) {
                    throw new Error('Invalid analysis structure: missing required fields');
                }
                this.logInfo('JSON repair successful', {
                    originalLength: jsonString.length,
                    repairedLength: repairedJson.length
                });
                return analysis;
            }
        }
        catch (error) {
            this.logError('Failed to parse analysis response', {
                error: error instanceof Error ? error.message : String(error),
                response: response.substring(0, 500)
            });
            throw new Error(`Failed to parse semantic analysis: ${error instanceof Error ? error.message : 'Invalid JSON'}`);
        }
    }
    /**
     * Repair truncated or malformed JSON responses
     */
    repairTruncatedJSON(jsonString) {
        let repaired = jsonString.trim();
        // Count open and close brackets/braces
        const openBraces = (repaired.match(/\{/g) || []).length;
        const closeBraces = (repaired.match(/\}/g) || []).length;
        const openBrackets = (repaired.match(/\[/g) || []).length;
        const closeBrackets = (repaired.match(/\]/g) || []).length;
        // Remove trailing incomplete content (common with truncated responses)
        if (repaired.endsWith(',') || repaired.endsWith('"')) {
            // Find the last complete object/array
            let lastCompleteIndex = -1;
            let braceCount = 0;
            let bracketCount = 0;
            let inString = false;
            let escaped = false;
            for (let i = 0; i < repaired.length; i++) {
                const char = repaired[i];
                if (escaped) {
                    escaped = false;
                    continue;
                }
                if (char === '\\') {
                    escaped = true;
                    continue;
                }
                if (char === '"') {
                    inString = !inString;
                    continue;
                }
                if (!inString) {
                    if (char === '{')
                        braceCount++;
                    else if (char === '}')
                        braceCount--;
                    else if (char === '[')
                        bracketCount++;
                    else if (char === ']')
                        bracketCount--;
                    // Mark position where we have balanced braces/brackets
                    if (braceCount === 1 && bracketCount === 0 && (char === '}' || char === ',')) {
                        lastCompleteIndex = i;
                    }
                }
            }
            if (lastCompleteIndex > 0) {
                repaired = repaired.substring(0, lastCompleteIndex + 1);
            }
        }
        // Close missing brackets and braces
        const missingCloseBrackets = openBrackets - closeBrackets;
        const missingCloseBraces = openBraces - closeBraces;
        for (let i = 0; i < missingCloseBrackets; i++) {
            repaired += ']';
        }
        for (let i = 0; i < missingCloseBraces; i++) {
            repaired += '}';
        }
        // Remove trailing commas before closing brackets/braces
        repaired = repaired.replace(/,(\s*[\]\}])/g, '$1');
        return repaired;
    }
}
exports.SemanticAnalyzer = SemanticAnalyzer;
//# sourceMappingURL=SemanticAnalyzer.js.map
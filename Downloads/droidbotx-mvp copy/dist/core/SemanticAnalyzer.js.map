{"version": 3, "file": "SemanticAnalyzer.js", "sourceRoot": "", "sources": ["../../src/core/SemanticAnalyzer.ts"], "names": [], "mappings": ";;;AAAA,2CAAwC;AA0GxC,MAAa,gBAAiB,SAAQ,qBAAS;IAC7C;QACE,KAAK,CACH,kBAAkB,EAClB,gFAAgF,EAChF;;;;;;;;;;;;;;;;oFAgB8E,CAC/E,CAAC;IACJ,CAAC;IAEM,SAAS,CAAC,IAAS;QACxB,OAAO,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC;IAC3C,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAS;QAC5B,4CAA4C;QAC5C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB,CAChC,WAAmB,EACnB,WAAmB,EACnB,YAAsB;QAEtB,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QAE9F,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE;gBAC5E,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,IAAI,CAAE,mDAAmD;aACrE,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;YAE9D,IAAI,CAAC,OAAO,CAAC,oCAAoC,EAAE;gBACjD,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,aAAa,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM;gBACvC,cAAc,EAAE,QAAQ,CAAC,SAAS,CAAC,MAAM;aAC1C,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACtH,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;IAEO,yBAAyB,CAC/B,WAAmB,EACnB,WAAmB,EACnB,YAAsB;QAEtB,OAAO;;WAEA,WAAW;eACP,WAAW;;;EAGxB,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDA4KlB,CAAC;IAClD,CAAC;IAEO,qBAAqB,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAE9B,uCAAuC;YACvC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAExC,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAChF,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;gBACzE,CAAC;gBAED,OAAO,QAAkC,CAAC;YAC5C,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,kDAAkD;gBAClD,IAAI,CAAC,OAAO,CAAC,+CAA+C,EAAE;oBAC5D,KAAK,EAAE,UAAU,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;iBAC7E,CAAC,CAAC;gBAEH,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAE1C,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAChF,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;gBACzE,CAAC;gBAED,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE;oBACrC,cAAc,EAAE,UAAU,CAAC,MAAM;oBACjC,cAAc,EAAE,YAAY,CAAC,MAAM;iBACpC,CAAC,CAAC;gBAEH,OAAO,QAAkC,CAAC;YAC5C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,CAAC,mCAAmC,EAAE;gBACjD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;aACrC,CAAC,CAAC;YACH,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC;QACnH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,UAAkB;QAC5C,IAAI,QAAQ,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;QAEjC,uCAAuC;QACvC,MAAM,UAAU,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACxD,MAAM,WAAW,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACzD,MAAM,YAAY,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAC1D,MAAM,aAAa,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAE3D,uEAAuE;QACvE,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACrD,sCAAsC;YACtC,IAAI,iBAAiB,GAAG,CAAC,CAAC,CAAC;YAC3B,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI,OAAO,GAAG,KAAK,CAAC;YAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACzC,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAEzB,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,GAAG,KAAK,CAAC;oBAChB,SAAS;gBACX,CAAC;gBAED,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;oBAClB,OAAO,GAAG,IAAI,CAAC;oBACf,SAAS;gBACX,CAAC;gBAED,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;oBACjB,QAAQ,GAAG,CAAC,QAAQ,CAAC;oBACrB,SAAS;gBACX,CAAC;gBAED,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,IAAI,IAAI,KAAK,GAAG;wBAAE,UAAU,EAAE,CAAC;yBAC1B,IAAI,IAAI,KAAK,GAAG;wBAAE,UAAU,EAAE,CAAC;yBAC/B,IAAI,IAAI,KAAK,GAAG;wBAAE,YAAY,EAAE,CAAC;yBACjC,IAAI,IAAI,KAAK,GAAG;wBAAE,YAAY,EAAE,CAAC;oBAEtC,uDAAuD;oBACvD,IAAI,UAAU,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;wBAC7E,iBAAiB,GAAG,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;gBAC1B,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,oCAAoC;QACpC,MAAM,oBAAoB,GAAG,YAAY,GAAG,aAAa,CAAC;QAC1D,MAAM,kBAAkB,GAAG,UAAU,GAAG,WAAW,CAAC;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,QAAQ,IAAI,GAAG,CAAC;QAClB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,QAAQ,IAAI,GAAG,CAAC;QAClB,CAAC;QAED,wDAAwD;QACxD,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAEnD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAxXD,4CAwXC"}
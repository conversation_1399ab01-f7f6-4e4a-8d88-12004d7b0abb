export interface ServicePattern {
    name: string;
    type: 'express_route' | 'react_component' | 'database_service' | 'utility_service';
    dependencies: string[];
    instantiationPattern: string;
    errorHandlingPattern: string;
    loggingPattern: string;
    testingPattern: string;
}
export interface ServiceRegistry {
    [serviceName: string]: {
        instance?: any;
        factory: () => any;
        dependencies: string[];
        singleton: boolean;
        initialized: boolean;
    };
}
export interface StandardizedService {
    name: string;
    filePath: string;
    content: string;
    dependencies: string[];
    exports: string[];
    patterns: ServicePattern[];
}
export interface ServiceValidationResult {
    isValid: boolean;
    errors: ServiceError[];
    warnings: ServiceWarning[];
    standardizationScore: number;
}
export interface ServiceError {
    type: 'missing_dependency' | 'circular_dependency' | 'invalid_pattern' | 'inconsistent_error_handling';
    service: string;
    message: string;
    severity: 'critical' | 'high' | 'medium' | 'low';
}
export interface ServiceWarning {
    type: 'performance' | 'naming' | 'pattern_inconsistency' | 'missing_tests';
    service: string;
    message: string;
    suggestion: string;
}
/**
 * Service Instantiation Standardization System for Phase 3
 * Standardizes service patterns across Express routes, React components, and database connections
 */
export declare class ServiceStandardization {
    private logger;
    private serviceRegistry;
    private standardPatterns;
    private validationResults;
    constructor();
    /**
     * Initialize standard service patterns
     */
    private initializeStandardPatterns;
    /**
     * Standardize service according to pattern
     */
    standardizeService(serviceName: string, serviceType: 'express_route' | 'react_component' | 'database_service' | 'utility_service', originalContent: string, dependencies?: string[]): StandardizedService;
    /**
     * Apply service pattern to content
     */
    private applyServicePattern;
    /**
     * Validate service standardization
     */
    validateServices(services: StandardizedService[]): ServiceValidationResult;
    /**
     * Generate service factory
     */
    generateServiceFactory(serviceName: string, dependencies: string[]): string;
    /**
     * Register service in registry
     */
    private registerService;
    /**
     * Check if dependency is available
     */
    private isDependencyAvailable;
    /**
     * Detect circular dependencies
     */
    private detectCircularDependencies;
    /**
     * Check if service has consistent error handling
     */
    private hasConsistentErrorHandling;
    /**
     * Check if service has test coverage
     */
    private hasTestCoverage;
    /**
     * Calculate standardization score
     */
    private calculateStandardizationScore;
    /**
     * Extract exports from service content
     */
    private extractExports;
    /**
     * Generate service file path
     */
    private generateServiceFilePath;
    /**
     * Convert to camelCase
     */
    private toCamelCase;
    /**
     * Get validation results
     */
    getValidationResults(): ServiceValidationResult | null;
    /**
     * Get service registry
     */
    getServiceRegistry(): ServiceRegistry;
    private logInfo;
    private logWarn;
    private logError;
}
//# sourceMappingURL=ServiceStandardization.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceStandardization = void 0;
const Logger_1 = require("./Logger");
/**
 * Service Instantiation Standardization System for Phase 3
 * Standardizes service patterns across Express routes, React components, and database connections
 */
class ServiceStandardization {
    constructor() {
        this.serviceRegistry = {};
        this.standardPatterns = new Map();
        this.validationResults = null;
        this.logger = Logger_1.Logger.getInstance();
        this.initializeStandardPatterns();
    }
    /**
     * Initialize standard service patterns
     */
    initializeStandardPatterns() {
        // Express Route Service Pattern
        this.standardPatterns.set('express_route', {
            name: 'Express Route Service',
            type: 'express_route',
            dependencies: ['express', 'service_layer', 'validation', 'auth_middleware'],
            instantiationPattern: `
import { Router, Request, Response, NextFunction } from 'express';
import { validateRequest } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import { {ServiceName} } from '../services/{ServiceName}';
import { logger } from '../core/Logger';

const router = Router();
const {serviceName} = new {ServiceName}();

router.use(authenticateToken);
router.use(validateRequest);`,
            errorHandlingPattern: `
const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Route error', { 
    route: req.path, 
    method: req.method, 
    error: error.message,
    stack: error.stack 
  });
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({ success: false, error: error.message });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  res.status(500).json({ success: false, error: 'Internal server error' });
};`,
            loggingPattern: `
logger.info('Route accessed', { 
  route: req.path, 
  method: req.method, 
  user: req.user?.id,
  timestamp: new Date().toISOString()
});`,
            testingPattern: `
import supertest from 'supertest';
import { app } from '../app';
import { {ServiceName} } from '../services/{ServiceName}';

jest.mock('../services/{ServiceName}');
const mockService = {ServiceName} as jest.Mocked<typeof {ServiceName}>;`
        });
        // React Component Service Pattern
        this.standardPatterns.set('react_component', {
            name: 'React Component Service',
            type: 'react_component',
            dependencies: ['react', 'service_hooks', 'error_boundary', 'loading_state'],
            instantiationPattern: `
import React, { useState, useEffect, useCallback } from 'react';
import { use{ServiceName} } from '../hooks/use{ServiceName}';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { LoadingSpinner } from '../components/LoadingSpinner';
import { logger } from '../utils/logger';

interface {ComponentName}Props {
  // Props interface
}

export const {ComponentName}: React.FC<{ComponentName}Props> = (props) => {
  const { data, loading, error, refetch } = use{ServiceName}();`,
            errorHandlingPattern: `
const handleError = useCallback((error: Error) => {
  logger.error('Component error', { 
    component: '{ComponentName}', 
    error: error.message,
    props: JSON.stringify(props)
  });
  
  // Show user-friendly error message
  setErrorMessage('Something went wrong. Please try again.');
}, [props]);

if (error) {
  return (
    <ErrorBoundary>
      <div className="error-message">
        {errorMessage || 'An unexpected error occurred'}
      </div>
    </ErrorBoundary>
  );
}`,
            loggingPattern: `
useEffect(() => {
  logger.info('Component mounted', { 
    component: '{ComponentName}',
    props: JSON.stringify(props),
    timestamp: new Date().toISOString()
  });
  
  return () => {
    logger.info('Component unmounted', { component: '{ComponentName}' });
  };
}, []);`,
            testingPattern: `
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { {ComponentName} } from './{ComponentName}';
import { use{ServiceName} } from '../hooks/use{ServiceName}';

jest.mock('../hooks/use{ServiceName}');
const mockUseService = use{ServiceName} as jest.MockedFunction<typeof use{ServiceName}>;`
        });
        // Database Service Pattern
        this.standardPatterns.set('database_service', {
            name: 'Database Service',
            type: 'database_service',
            dependencies: ['database_connection', 'transaction_manager', 'query_builder', 'validation'],
            instantiationPattern: `
import { DatabaseConnection } from '../database/connection';
import { TransactionManager } from '../database/transaction';
import { QueryBuilder } from '../database/queryBuilder';
import { validate } from '../utils/validation';
import { logger } from '../core/Logger';

export class {ServiceName} {
  private db: DatabaseConnection;
  private transaction: TransactionManager;
  private queryBuilder: QueryBuilder;

  constructor() {
    this.db = DatabaseConnection.getInstance();
    this.transaction = new TransactionManager(this.db);
    this.queryBuilder = new QueryBuilder();
  }`,
            errorHandlingPattern: `
private async handleDatabaseError(error: Error, operation: string, data?: any): Promise<never> {
  logger.error('Database operation failed', {
    service: '{ServiceName}',
    operation,
    error: error.message,
    data: JSON.stringify(data),
    stack: error.stack
  });

  if (error.message.includes('duplicate key')) {
    throw new Error('Record already exists');
  }
  
  if (error.message.includes('foreign key')) {
    throw new Error('Invalid reference');
  }
  
  if (error.message.includes('not null')) {
    throw new Error('Required field missing');
  }
  
  throw new Error('Database operation failed');
}`,
            loggingPattern: `
logger.info('Database operation', {
  service: '{ServiceName}',
  operation: '{operation}',
  duration: Date.now() - startTime,
  recordsAffected: result.rowCount,
  timestamp: new Date().toISOString()
});`,
            testingPattern: `
import { {ServiceName} } from './{ServiceName}';
import { DatabaseConnection } from '../database/connection';

jest.mock('../database/connection');
const mockDb = DatabaseConnection as jest.Mocked<typeof DatabaseConnection>;`
        });
        this.logInfo('Standard service patterns initialized', {
            patternsCount: this.standardPatterns.size
        });
    }
    /**
     * Standardize service according to pattern
     */
    standardizeService(serviceName, serviceType, originalContent, dependencies = []) {
        this.logInfo('Standardizing service', { serviceName, serviceType });
        const pattern = this.standardPatterns.get(serviceType);
        if (!pattern) {
            throw new Error(`Unknown service pattern: ${serviceType}`);
        }
        const standardizedContent = this.applyServicePattern(serviceName, originalContent, pattern, dependencies);
        const standardizedService = {
            name: serviceName,
            filePath: this.generateServiceFilePath(serviceName, serviceType),
            content: standardizedContent,
            dependencies: [...pattern.dependencies, ...dependencies],
            exports: this.extractExports(standardizedContent),
            patterns: [pattern]
        };
        // Register service in registry
        this.registerService(serviceName, standardizedService);
        this.logInfo('Service standardization completed', {
            serviceName,
            serviceType,
            dependenciesCount: standardizedService.dependencies.length,
            exportsCount: standardizedService.exports.length
        });
        return standardizedService;
    }
    /**
     * Apply service pattern to content
     */
    applyServicePattern(serviceName, originalContent, pattern, additionalDependencies) {
        let standardizedContent = originalContent;
        // Apply instantiation pattern
        const instantiationCode = pattern.instantiationPattern
            .replace(/{ServiceName}/g, serviceName)
            .replace(/{serviceName}/g, this.toCamelCase(serviceName))
            .replace(/{ComponentName}/g, serviceName);
        // Apply error handling pattern
        const errorHandlingCode = pattern.errorHandlingPattern
            .replace(/{ServiceName}/g, serviceName)
            .replace(/{ComponentName}/g, serviceName);
        // Apply logging pattern
        const loggingCode = pattern.loggingPattern
            .replace(/{ServiceName}/g, serviceName)
            .replace(/{ComponentName}/g, serviceName);
        // Combine patterns with original content
        standardizedContent = [
            '// Auto-generated standardized service',
            '// Pattern: ' + pattern.name,
            '',
            instantiationCode,
            '',
            originalContent,
            '',
            errorHandlingCode,
            '',
            '// Logging utilities',
            loggingCode,
            '',
            'export default ' + serviceName + ';'
        ].join('\n');
        return standardizedContent;
    }
    /**
     * Validate service standardization
     */
    validateServices(services) {
        const errors = [];
        const warnings = [];
        services.forEach(service => {
            // Check for missing dependencies
            service.dependencies.forEach(dep => {
                if (!this.isDependencyAvailable(dep)) {
                    errors.push({
                        type: 'missing_dependency',
                        service: service.name,
                        message: `Missing dependency: ${dep}`,
                        severity: 'high'
                    });
                }
            });
            // Check for circular dependencies
            const circularDeps = this.detectCircularDependencies(service, services);
            if (circularDeps.length > 0) {
                errors.push({
                    type: 'circular_dependency',
                    service: service.name,
                    message: `Circular dependency detected: ${circularDeps.join(' -> ')}`,
                    severity: 'critical'
                });
            }
            // Check pattern consistency
            if (!this.hasConsistentErrorHandling(service)) {
                warnings.push({
                    type: 'pattern_inconsistency',
                    service: service.name,
                    message: 'Inconsistent error handling pattern',
                    suggestion: 'Apply standard error handling pattern'
                });
            }
            // Check for test coverage
            if (!this.hasTestCoverage(service)) {
                warnings.push({
                    type: 'missing_tests',
                    service: service.name,
                    message: 'Service lacks test coverage',
                    suggestion: 'Add comprehensive test suite'
                });
            }
        });
        const standardizationScore = this.calculateStandardizationScore(errors, warnings, services.length);
        this.validationResults = {
            isValid: errors.filter(e => e.severity === 'critical').length === 0,
            errors,
            warnings,
            standardizationScore
        };
        this.logInfo('Service validation completed', {
            servicesValidated: services.length,
            errorsFound: errors.length,
            warningsFound: warnings.length,
            standardizationScore
        });
        return this.validationResults;
    }
    /**
     * Generate service factory
     */
    generateServiceFactory(serviceName, dependencies) {
        return `
export const create${serviceName} = (${dependencies.map(dep => `${dep}: any`).join(', ')}) => {
  return new ${serviceName}(${dependencies.join(', ')});
};

export const ${serviceName}Factory = {
  create: create${serviceName},
  dependencies: [${dependencies.map(dep => `'${dep}'`).join(', ')}],
  singleton: true
};`;
    }
    /**
     * Register service in registry
     */
    registerService(serviceName, service) {
        this.serviceRegistry[serviceName] = {
            factory: () => service,
            dependencies: service.dependencies,
            singleton: true,
            initialized: false
        };
    }
    /**
     * Check if dependency is available
     */
    isDependencyAvailable(dependency) {
        // Check if dependency is in registry or is a standard library
        return this.serviceRegistry[dependency] !== undefined ||
            ['express', 'react', 'lodash', 'axios'].includes(dependency);
    }
    /**
     * Detect circular dependencies
     */
    detectCircularDependencies(service, allServices) {
        const visited = new Set();
        const recursionStack = new Set();
        const dfs = (serviceName, path) => {
            if (recursionStack.has(serviceName)) {
                const cycleStart = path.indexOf(serviceName);
                return [...path.slice(cycleStart), serviceName];
            }
            if (visited.has(serviceName))
                return [];
            visited.add(serviceName);
            recursionStack.add(serviceName);
            const currentService = allServices.find(s => s.name === serviceName);
            if (currentService) {
                for (const dep of currentService.dependencies) {
                    const cycle = dfs(dep, [...path, serviceName]);
                    if (cycle.length > 0)
                        return cycle;
                }
            }
            recursionStack.delete(serviceName);
            return [];
        };
        return dfs(service.name, []);
    }
    /**
     * Check if service has consistent error handling
     */
    hasConsistentErrorHandling(service) {
        return service.content.includes('handleError') &&
            service.content.includes('logger.error');
    }
    /**
     * Check if service has test coverage
     */
    hasTestCoverage(service) {
        // This would check for corresponding test files in a real implementation
        return service.content.includes('jest.mock') ||
            service.filePath.includes('.test.') ||
            service.filePath.includes('.spec.');
    }
    /**
     * Calculate standardization score
     */
    calculateStandardizationScore(errors, warnings, serviceCount) {
        const criticalErrors = errors.filter(e => e.severity === 'critical').length;
        const highErrors = errors.filter(e => e.severity === 'high').length;
        const mediumErrors = errors.filter(e => e.severity === 'medium').length;
        const errorPenalty = (criticalErrors * 25) + (highErrors * 15) + (mediumErrors * 8) + (warnings.length * 3);
        const maxScore = 100;
        return Math.max(0, maxScore - errorPenalty);
    }
    /**
     * Extract exports from service content
     */
    extractExports(content) {
        const exports = [];
        const exportRegex = /export\s+(?:const|let|var|function|class|interface|type)\s+(\w+)/g;
        let match;
        while ((match = exportRegex.exec(content)) !== null) {
            exports.push(match[1]);
        }
        if (content.includes('export default')) {
            exports.push('default');
        }
        return exports;
    }
    /**
     * Generate service file path
     */
    generateServiceFilePath(serviceName, serviceType) {
        const typeToPath = {
            'express_route': `src/routes/${serviceName}Routes.ts`,
            'react_component': `src/components/${serviceName}.tsx`,
            'database_service': `src/services/${serviceName}Service.ts`,
            'utility_service': `src/utils/${serviceName}.ts`
        };
        return typeToPath[serviceType] || `src/services/${serviceName}.ts`;
    }
    /**
     * Convert to camelCase
     */
    toCamelCase(str) {
        return str.charAt(0).toLowerCase() + str.slice(1);
    }
    /**
     * Get validation results
     */
    getValidationResults() {
        return this.validationResults;
    }
    /**
     * Get service registry
     */
    getServiceRegistry() {
        return { ...this.serviceRegistry };
    }
    logInfo(message, context) {
        this.logger.info(`[ServiceStandardization] ${message}`, context);
    }
    logWarn(message, context) {
        this.logger.warn(`[ServiceStandardization] ${message}`, context);
    }
    logError(message, context) {
        this.logger.error(`[ServiceStandardization] ${message}`, context);
    }
}
exports.ServiceStandardization = ServiceStandardization;
//# sourceMappingURL=ServiceStandardization.js.map
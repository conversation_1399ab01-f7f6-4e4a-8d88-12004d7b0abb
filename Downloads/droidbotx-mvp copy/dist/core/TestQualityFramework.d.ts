import { BusinessEntity, BusinessDomainAnalysis } from './SemanticAnalyzer';
export interface TechnicalSpecification {
    projectName: string;
    businessDomain: string;
    architecture: any;
    [key: string]: any;
}
export interface TestSuite {
    name: string;
    type: 'unit' | 'integration' | 'e2e';
    filePath: string;
    testCases: TestCase[];
    coverage: TestCoverage;
    dependencies: string[];
}
export interface TestCase {
    name: string;
    description: string;
    type: 'positive' | 'negative' | 'edge';
    setup: string[];
    assertions: string[];
    mocks: MockDefinition[];
    expectedResult: 'pass' | 'fail';
}
export interface MockDefinition {
    target: string;
    type: 'function' | 'module' | 'class';
    implementation: string;
    returnValue?: any;
}
export interface TestCoverage {
    statements: number;
    branches: number;
    functions: number;
    lines: number;
}
export interface TestQualityMetrics {
    totalTests: number;
    passRate: number;
    coveragePercentage: number;
    mockQuality: number;
    testTypes: {
        unit: number;
        integration: number;
        e2e: number;
    };
}
/**
 * Test Quality Enhancement Framework for Phase 3
 * Achieves >80% test pass rate with comprehensive coverage
 */
export declare class TestQualityFramework {
    private logger;
    private testSuites;
    private mockRegistry;
    private qualityMetrics;
    constructor();
    private logInfo;
    private logWarn;
    private logError;
    /**
     * Generate comprehensive test suite for an entity
     */
    generateEntityTestSuite(entity: BusinessEntity, semanticAnalysis: BusinessDomainAnalysis, technicalSpec: TechnicalSpecification): TestSuite[];
    /**
     * Generate unit tests with proper mocks
     */
    private generateUnitTests;
    /**
     * Generate integration tests for API endpoints
     */
    private generateIntegrationTests;
    /**
     * Generate E2E tests for user workflows
     */
    private generateE2ETests;
    /**
     * Generate Jest test file content with enhanced structure
     */
    generateTestFileContent(testSuite: TestSuite): string;
    /**
     * Auto-fix failing tests
     */
    autoFixFailingTests(testSuite: TestSuite, failures: string[]): TestSuite;
    /**
     * Get enhanced quality metrics with Jest integration support
     */
    getQualityMetrics(): TestQualityMetrics;
    /**
     * Validate test coverage
     */
    validateCoverage(requiredCoverage?: number): boolean;
    /**
     * Generate mock data for entity
     */
    private generateMockData;
    /**
     * Generate edge case data
     */
    private generateEdgeCaseData;
    /**
     * Get mock value for field type
     */
    private getMockValueForType;
    /**
     * Get edge case value for field type
     */
    private getEdgeValueForType;
    /**
     * Generate test imports with proper Jest runtime support
     */
    private generateTestImports;
    /**
     * Generate mock setup code with enhanced Jest support
     */
    private generateMockSetup;
    /**
     * Generate test case code with enhanced error handling
     */
    private generateTestCaseCode;
    /**
     * Enhanced test execution function
     */
    private generateTestExecutionFunction;
    /**
     * Fix a failing test case
     */
    private fixTestCase;
    /**
     * Update quality metrics
     */
    private updateQualityMetrics;
    /**
     * Calculate average coverage across all test suites
     */
    private calculateAverageCoverage;
    /**
     * Calculate mock quality score
     */
    private calculateMockQuality;
}
//# sourceMappingURL=TestQualityFramework.d.ts.map
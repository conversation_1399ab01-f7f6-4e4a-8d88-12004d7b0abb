export interface Tool {
    name: string;
    description: string;
    execute(params: Record<string, any>): Promise<any>;
}
export interface ToolResult {
    success: boolean;
    data?: any;
    error?: string;
}
export declare class ToolManager {
    private static instance;
    private tools;
    private logger;
    private constructor();
    static getInstance(): ToolManager;
    registerTool(tool: Tool): void;
    executeTool(toolName: string, params?: Record<string, any>): Promise<ToolResult>;
    getAvailableTools(): string[];
    getToolDescription(toolName: string): string | undefined;
}
//# sourceMappingURL=ToolManager.d.ts.map
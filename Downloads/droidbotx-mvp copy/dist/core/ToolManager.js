"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolManager = void 0;
const Logger_1 = require("./Logger");
class ToolManager {
    constructor() {
        this.tools = new Map();
        this.logger = Logger_1.Logger.getInstance();
    }
    static getInstance() {
        if (!ToolManager.instance) {
            ToolManager.instance = new ToolManager();
        }
        return ToolManager.instance;
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
        this.logger.info(`Tool registered: ${tool.name}`, {
            description: tool.description,
        });
    }
    async executeTool(toolName, params = {}) {
        const tool = this.tools.get(toolName);
        if (!tool) {
            const error = `Tool not found: ${toolName}`;
            this.logger.error(error);
            return { success: false, error };
        }
        try {
            this.logger.debug(`Executing tool: ${toolName}`, { params });
            const result = await tool.execute(params);
            this.logger.info(`Tool executed successfully: ${toolName}`);
            return { success: true, data: result };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`Tool execution failed: ${toolName}`, {
                error: errorMessage,
                params,
            });
            return { success: false, error: errorMessage };
        }
    }
    getAvailableTools() {
        return Array.from(this.tools.keys());
    }
    getToolDescription(toolName) {
        const tool = this.tools.get(toolName);
        return tool?.description;
    }
}
exports.ToolManager = ToolManager;
//# sourceMappingURL=ToolManager.js.map
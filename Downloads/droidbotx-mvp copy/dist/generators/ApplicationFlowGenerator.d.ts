import { TechnicalSpecification } from '../agents/PlanningAgent';
export interface RouteDefinition {
    path: string;
    component: string;
    label: string;
    protected: boolean;
    icon?: string;
    description?: string;
}
export interface NavigationItem {
    label: string;
    path: string;
    icon: string;
    children?: NavigationItem[];
}
export interface ApplicationFlow {
    routes: RouteDefinition[];
    navigation: NavigationItem[];
    appComponent: string;
    dashboardComponent: string;
}
export declare class ApplicationFlowGenerator {
    private domainRouteMapping;
    constructor();
    generateApplicationFlow(requirements: string[], spec: TechnicalSpecification, generatedComponents: string[]): ApplicationFlow;
    private initializeDomainMappings;
    private findMatchingDomainRoutes;
    private determineApplicationContext;
    private generateNavigationFromRoutes;
    private getDomainFromPath;
    private generateAppComponent;
    private generateImports;
    private getComponentPath;
    private generateRouteElements;
    private generateDashboardComponent;
    private generateQuickActions;
    private generateDashboardCards;
    generateSidebarComponent(navigation: NavigationItem[]): string;
    private generateNavigationItems;
}
//# sourceMappingURL=ApplicationFlowGenerator.d.ts.map
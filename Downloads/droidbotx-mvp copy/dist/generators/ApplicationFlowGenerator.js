"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationFlowGenerator = void 0;
class ApplicationFlowGenerator {
    constructor() {
        this.domainRouteMapping = new Map();
        this.initializeDomainMappings();
    }
    generateApplicationFlow(requirements, spec, generatedComponents) {
        const routes = [];
        const navigation = [];
        const generatedRoutes = new Set();
        // Always include base routes
        routes.push({ path: '/', component: 'Home', label: 'Home', protected: false }, { path: '/login', component: 'Login', label: 'Login', protected: false }, { path: '/dashboard', component: 'Dashboard', label: 'Dashboard', protected: true, icon: 'dashboard' });
        // Determine application context for route filtering
        const applicationContext = this.determineApplicationContext(requirements, spec);
        // Analyze requirements and generate corresponding routes
        requirements.forEach(requirement => {
            const mappings = this.findMatchingDomainRoutes(requirement, applicationContext);
            mappings.forEach(routeList => {
                routeList.forEach(route => {
                    if (!generatedRoutes.has(route.path)) {
                        routes.push(route);
                        generatedRoutes.add(route.path);
                    }
                });
            });
        });
        // Generate navigation structure
        const navigationItems = this.generateNavigationFromRoutes(routes);
        navigation.push(...navigationItems);
        // Generate enhanced App component
        const appComponent = this.generateAppComponent(routes);
        // Generate enhanced Dashboard component
        const dashboardComponent = this.generateDashboardComponent(routes, requirements);
        return {
            routes,
            navigation,
            appComponent,
            dashboardComponent
        };
    }
    initializeDomainMappings() {
        // POS/Retail Domain Routes
        this.domainRouteMapping.set('pos', [
            {
                path: '/pos',
                component: 'POSTerminal',
                label: 'POS Terminal',
                protected: true,
                icon: 'point_of_sale',
                description: 'Point of sale terminal for processing transactions'
            },
            {
                path: '/pos/scanner',
                component: 'ProductScanner',
                label: 'Product Scanner',
                protected: true,
                icon: 'qr_code_scanner',
                description: 'Scan product barcodes'
            }
        ]);
        // Inventory Management Domain Routes
        this.domainRouteMapping.set('inventory', [
            {
                path: '/inventory',
                component: 'InventoryDashboard',
                label: 'Inventory',
                protected: true,
                icon: 'inventory',
                description: 'Manage inventory and stock levels'
            },
            {
                path: '/inventory/alerts',
                component: 'StockAlerts',
                label: 'Stock Alerts',
                protected: true,
                icon: 'warning',
                description: 'View low stock alerts and reorder points'
            }
        ]);
        // Vehicle/Automotive Domain Routes
        this.domainRouteMapping.set('vehicle', [
            {
                path: '/vehicles',
                component: 'VehicleSearch',
                label: 'Vehicle Lookup',
                protected: true,
                icon: 'directions_car',
                description: 'Search vehicles and check part compatibility'
            },
            {
                path: '/vehicles/compatibility',
                component: 'CompatibilityChecker',
                label: 'Compatibility Check',
                protected: true,
                icon: 'check_circle',
                description: 'Check part compatibility with vehicles'
            }
        ]);
        // Customer Management Domain Routes
        this.domainRouteMapping.set('customer', [
            {
                path: '/customers',
                component: 'CustomerManager',
                label: 'Customers',
                protected: true,
                icon: 'people',
                description: 'Manage customer information and history'
            },
            {
                path: '/customers/:id/history',
                component: 'CustomerHistory',
                label: 'Customer History',
                protected: true,
                icon: 'history',
                description: 'View customer purchase history'
            }
        ]);
        // Payment Processing Domain Routes
        this.domainRouteMapping.set('payment', [
            {
                path: '/payments',
                component: 'PaymentProcessor',
                label: 'Payments',
                protected: true,
                icon: 'payment',
                description: 'Process payments and manage payment methods'
            }
        ]);
        // Supplier Management Domain Routes
        this.domainRouteMapping.set('supplier', [
            {
                path: '/suppliers',
                component: 'SupplierManager',
                label: 'Suppliers',
                protected: true,
                icon: 'business',
                description: 'Manage suppliers and orders'
            },
            {
                path: '/suppliers/orders',
                component: 'OrderManagement',
                label: 'Orders',
                protected: true,
                icon: 'shopping_cart',
                description: 'Manage supplier orders'
            }
        ]);
        // Warranty Management Domain Routes
        this.domainRouteMapping.set('warranty', [
            {
                path: '/warranty',
                component: 'WarrantyTracker',
                label: 'Warranty',
                protected: true,
                icon: 'verified',
                description: 'Track warranties and process claims'
            },
            {
                path: '/warranty/claims',
                component: 'WarrantyClaims',
                label: 'Warranty Claims',
                protected: true,
                icon: 'report_problem',
                description: 'Process warranty claims'
            }
        ]);
        // Reports/Analytics Domain Routes
        this.domainRouteMapping.set('reports', [
            {
                path: '/reports',
                component: 'SalesReports',
                label: 'Reports',
                protected: true,
                icon: 'assessment',
                description: 'View sales reports and analytics'
            },
            {
                path: '/analytics',
                component: 'AnalyticsDashboard',
                label: 'Analytics',
                protected: true,
                icon: 'analytics',
                description: 'Business analytics and insights'
            }
        ]);
    }
    findMatchingDomainRoutes(requirement, applicationContext) {
        const matches = [];
        const reqLower = requirement.toLowerCase();
        // Check for POS/Product related keywords (only for physical retail, not e-commerce)
        const isPhysicalPOS = (reqLower.includes('pos terminal') || reqLower.includes('point of sale')) &&
            !reqLower.includes('ecommerce') && !reqLower.includes('e-commerce') &&
            !reqLower.includes('online');
        if (isPhysicalPOS || (reqLower.includes('barcode scanner') && applicationContext !== 'ecommerce')) {
            const posMapping = this.domainRouteMapping.get('pos');
            if (posMapping)
                matches.push(posMapping);
        }
        // Check for inventory keywords
        if (reqLower.includes('inventory') || reqLower.includes('stock') || reqLower.includes('reorder') || reqLower.includes('alert')) {
            const inventoryMapping = this.domainRouteMapping.get('inventory');
            if (inventoryMapping)
                matches.push(inventoryMapping);
        }
        // Check for vehicle keywords (only for automotive context)
        if (applicationContext === 'automotive' &&
            (reqLower.includes('vehicle compatibility') || reqLower.includes('automotive') ||
                reqLower.includes('car parts') || reqLower.includes('auto parts') ||
                reqLower.includes('vehicle make') || reqLower.includes('vehicle model'))) {
            const vehicleMapping = this.domainRouteMapping.get('vehicle');
            if (vehicleMapping)
                matches.push(vehicleMapping);
        }
        // Check for customer keywords
        if (reqLower.includes('customer') || reqLower.includes('purchase history')) {
            const customerMapping = this.domainRouteMapping.get('customer');
            if (customerMapping)
                matches.push(customerMapping);
        }
        // Check for payment keywords
        if (reqLower.includes('payment') || reqLower.includes('cash') || reqLower.includes('card') || reqLower.includes('mobile')) {
            const paymentMapping = this.domainRouteMapping.get('payment');
            if (paymentMapping)
                matches.push(paymentMapping);
        }
        // Check for supplier keywords
        if (reqLower.includes('supplier') || reqLower.includes('order')) {
            const supplierMapping = this.domainRouteMapping.get('supplier');
            if (supplierMapping)
                matches.push(supplierMapping);
        }
        // Check for warranty keywords
        if (reqLower.includes('warranty') || reqLower.includes('claim')) {
            const warrantyMapping = this.domainRouteMapping.get('warranty');
            if (warrantyMapping)
                matches.push(warrantyMapping);
        }
        // Check for reports keywords
        if (reqLower.includes('report') || reqLower.includes('analytics') || reqLower.includes('dashboard')) {
            const reportsMapping = this.domainRouteMapping.get('reports');
            if (reportsMapping)
                matches.push(reportsMapping);
        }
        return matches;
    }
    determineApplicationContext(requirements, spec) {
        const allText = (requirements.join(' ') + ' ' + spec.description + ' ' + spec.projectName).toLowerCase();
        // Check for e-commerce indicators (prioritize e-commerce detection)
        if (allText.includes('ecommerce') || allText.includes('e-commerce') ||
            allText.includes('online store') || allText.includes('shopping') ||
            allText.includes('electronics store') || allText.includes('gadgets store') ||
            allText.includes('electronics') || allText.includes('gadgets')) {
            return 'ecommerce';
        }
        // Check for automotive indicators (be more specific to avoid false positives)
        if ((allText.includes('auto parts') && allText.includes('vehicle')) ||
            allText.includes('automotive') ||
            (allText.includes('vehicle') && (allText.includes('compatibility') || allText.includes('make') || allText.includes('model'))) ||
            allText.includes('car parts') ||
            (allText.includes('pos') && allText.includes('auto parts'))) {
            return 'automotive';
        }
        // Check for general retail/POS
        if (allText.includes('pos') || allText.includes('point of sale') ||
            allText.includes('retail') || allText.includes('barcode')) {
            return 'retail';
        }
        return 'general';
    }
    generateNavigationFromRoutes(routes) {
        const navigation = [];
        const mainRoutes = routes.filter(route => route.protected && route.icon && !route.path.includes('/:'));
        // Group routes by domain
        const groupedRoutes = new Map();
        mainRoutes.forEach(route => {
            const domain = this.getDomainFromPath(route.path);
            if (!groupedRoutes.has(domain)) {
                groupedRoutes.set(domain, []);
            }
            groupedRoutes.get(domain).push(route);
        });
        // Create navigation items
        groupedRoutes.forEach((domainRoutes, domain) => {
            if (domainRoutes.length === 1) {
                // Single route - add directly
                const route = domainRoutes[0];
                navigation.push({
                    label: route.label,
                    path: route.path,
                    icon: route.icon || 'circle'
                });
            }
            else {
                // Multiple routes - create group
                const mainRoute = domainRoutes[0];
                navigation.push({
                    label: mainRoute.label,
                    path: mainRoute.path,
                    icon: mainRoute.icon || 'circle',
                    children: domainRoutes.slice(1).map(route => ({
                        label: route.label,
                        path: route.path,
                        icon: route.icon || 'circle'
                    }))
                });
            }
        });
        return navigation;
    }
    getDomainFromPath(path) {
        const segments = path.split('/').filter(s => s);
        return segments[0] || 'general';
    }
    generateAppComponent(routes) {
        const imports = this.generateImports(routes);
        const routeElements = this.generateRouteElements(routes);
        return `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import PrivateRoute from './components/PrivateRoute';
${imports}
import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Header />
          <div className="app-layout">
            <Sidebar />
            <main className="main-content">
              <Routes>
${routeElements}
              </Routes>
            </main>
          </div>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;`;
    }
    generateImports(routes) {
        const components = new Set();
        routes.forEach(route => {
            components.add(route.component);
        });
        const imports = [];
        components.forEach(component => {
            if (component === 'Home' || component === 'Login' || component === 'Dashboard') {
                imports.push(`import ${component} from './pages/${component}';`);
            }
            else {
                // Try to determine the component path based on naming
                const componentPath = this.getComponentPath(component);
                imports.push(`import ${component} from './components/${componentPath}';`);
            }
        });
        return imports.join('\n');
    }
    getComponentPath(componentName) {
        // Map component names to their likely paths
        const pathMappings = {
            'POSTerminal': 'POS/POSTerminal',
            'ProductScanner': 'scanning/ProductScanner',
            'InventoryDashboard': 'Inventory/InventoryDashboard',
            'StockAlerts': 'Inventory/StockAlerts',
            'VehicleSearch': 'VehicleLookup/VehicleSearch',
            'CompatibilityChecker': 'VehicleLookup/CompatibilityChecker',
            'CustomerManager': 'Customers/CustomerManager',
            'CustomerHistory': 'Customers/CustomerHistory',
            'PaymentProcessor': 'payments/PaymentProcessor',
            'SupplierManager': 'Suppliers/SupplierManager',
            'OrderManagement': 'Suppliers/OrderManagement',
            'WarrantyTracker': 'Warranty/WarrantyTracker',
            'WarrantyClaims': 'Warranty/WarrantyClaims',
            'SalesReports': 'reports/SalesReports',
            'AnalyticsDashboard': 'Analytics/AnalyticsDashboard'
        };
        return pathMappings[componentName] || `Common/${componentName}`;
    }
    generateRouteElements(routes) {
        return routes.map(route => {
            if (route.protected) {
                return `                <Route
                  path="${route.path}"
                  element={
                    <PrivateRoute>
                      <${route.component} />
                    </PrivateRoute>
                  }
                />`;
            }
            else {
                return `                <Route path="${route.path}" element={<${route.component} />} />`;
            }
        }).join('\n');
    }
    generateDashboardComponent(routes, requirements) {
        const quickActions = this.generateQuickActions(routes);
        const dashboardCards = this.generateDashboardCards(routes, requirements);
        return `import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import './Dashboard.css';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Welcome back, {user?.name}!</h1>
        <p className="dashboard-subtitle">Here's what's happening with your business today</p>
      </div>

      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-grid">
${quickActions}
        </div>
      </div>

      <div className="dashboard-cards">
${dashboardCards}
      </div>
    </div>
  );
};

export default Dashboard;`;
    }
    generateQuickActions(routes) {
        const primaryRoutes = routes.filter(route => route.protected &&
            route.icon &&
            !route.path.includes('/:') &&
            ['pos', 'inventory', 'customers', 'reports'].some(domain => route.path.includes(domain)));
        return primaryRoutes.map(route => `          <Link to="${route.path}" className="action-card">
            <div className="action-icon">
              <span className="material-icons">${route.icon}</span>
            </div>
            <div className="action-content">
              <h3>${route.label}</h3>
              <p>${route.description || 'Access ' + route.label.toLowerCase()}</p>
            </div>
          </Link>`).join('\n');
    }
    generateDashboardCards(routes, requirements) {
        const cards = [];
        // Add sales summary card if physical POS is included (not for e-commerce)
        const hasPhysicalPOS = requirements.some(req => {
            const reqLower = req.toLowerCase();
            return (reqLower.includes('pos terminal') || reqLower.includes('point of sale')) &&
                !reqLower.includes('ecommerce') && !reqLower.includes('e-commerce') &&
                !reqLower.includes('online');
        });
        if (hasPhysicalPOS) {
            cards.push(`        <div className="dashboard-card">
          <h3>Today's Sales</h3>
          <div className="card-content">
            <div className="metric">
              <span className="metric-value">$0.00</span>
              <span className="metric-label">Total Sales</span>
            </div>
            <div className="metric">
              <span className="metric-value">0</span>
              <span className="metric-label">Transactions</span>
            </div>
          </div>
        </div>`);
        }
        // Add inventory card if inventory is included
        if (requirements.some(req => req.toLowerCase().includes('inventory') || req.toLowerCase().includes('stock'))) {
            cards.push(`        <div className="dashboard-card">
          <h3>Inventory Status</h3>
          <div className="card-content">
            <div className="metric">
              <span className="metric-value">0</span>
              <span className="metric-label">Low Stock Items</span>
            </div>
            <div className="metric">
              <span className="metric-value">0</span>
              <span className="metric-label">Total Products</span>
            </div>
          </div>
        </div>`);
        }
        // Add customer card if customer management is included
        if (requirements.some(req => req.toLowerCase().includes('customer'))) {
            cards.push(`        <div className="dashboard-card">
          <h3>Customer Overview</h3>
          <div className="card-content">
            <div className="metric">
              <span className="metric-value">0</span>
              <span className="metric-label">Total Customers</span>
            </div>
            <div className="metric">
              <span className="metric-value">0</span>
              <span className="metric-label">New This Month</span>
            </div>
          </div>
        </div>`);
        }
        return cards.join('\n');
    }
    generateSidebarComponent(navigation) {
        const navigationItems = this.generateNavigationItems(navigation);
        return `import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import './Sidebar.css';

const Sidebar: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (path: string) => {
    setExpandedItems(prev =>
      prev.includes(path)
        ? prev.filter(item => item !== path)
        : [...prev, path]
    );
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  if (!user) return null;

  return (
    <aside className="sidebar">
      <nav className="sidebar-nav">
        <ul className="nav-list">
${navigationItems}
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;`;
    }
    generateNavigationItems(navigation) {
        return navigation.map(item => {
            if (item.children && item.children.length > 0) {
                const childItems = item.children.map(child => `              <li>
                <Link
                  to="${child.path}"
                  className={\`nav-link \${isActive('${child.path}') ? 'active' : ''}\`}
                >
                  <span className="material-icons">${child.icon}</span>
                  <span className="nav-text">${child.label}</span>
                </Link>
              </li>`).join('\n');
                return `          <li className="nav-item">
            <button
              className={\`nav-link expandable \${expandedItems.includes('${item.path}') ? 'expanded' : ''}\`}
              onClick={() => toggleExpanded('${item.path}')}
            >
              <span className="material-icons">${item.icon}</span>
              <span className="nav-text">${item.label}</span>
              <span className="material-icons expand-icon">
                {expandedItems.includes('${item.path}') ? 'expand_less' : 'expand_more'}
              </span>
            </button>
            {expandedItems.includes('${item.path}') && (
              <ul className="nav-submenu">
${childItems}
              </ul>
            )}
          </li>`;
            }
            else {
                return `          <li className="nav-item">
            <Link
              to="${item.path}"
              className={\`nav-link \${isActive('${item.path}') ? 'active' : ''}\`}
            >
              <span className="material-icons">${item.icon}</span>
              <span className="nav-text">${item.label}</span>
            </Link>
          </li>`;
            }
        }).join('\n');
    }
}
exports.ApplicationFlowGenerator = ApplicationFlowGenerator;
//# sourceMappingURL=ApplicationFlowGenerator.js.map
import { TechnicalSpecification } from '../agents/PlanningAgent';
import { BusinessDomainAnalysis } from '../core/SemanticAnalyzer';
import { BaseAgent } from '../core/BaseAgent';
export interface TableDefinition {
    name: string;
    columns: string;
    indexes?: string[];
    constraints?: string[];
}
export interface DatabaseSchema {
    tables: TableDefinition[];
    indexes: string[];
    seedData: string[];
    migrations: string[];
}
export declare class DatabaseSchemaGenerator extends BaseAgent {
    private aiCodeGenerator;
    constructor();
    canHandle(task: any): boolean;
    execute(task: any): Promise<any>;
    generateSchemaFromRequirements(requirements: string[], spec: TechnicalSpecification): Promise<DatabaseSchema>;
    generateSchemaFromSemanticAnalysis(semanticAnalysis: BusinessDomainAnalysis, spec: TechnicalSpecification): Promise<DatabaseSchema>;
    private parseGeneratedSchema;
    generateDatabaseInitScript(schema: DatabaseSchema, applicationContext?: string): string;
}
//# sourceMappingURL=DatabaseSchemaGenerator.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseSchemaGenerator = void 0;
const AICodeGenerator_1 = require("../core/AICodeGenerator");
const BaseAgent_1 = require("../core/BaseAgent");
class DatabaseSchemaGenerator extends BaseAgent_1.BaseAgent {
    constructor() {
        super('DatabaseSchemaGenerator', 'Generates database schemas using AI-driven analysis of business entities', `You are a database architect specializing in generating optimized PostgreSQL schemas based on business domain analysis.

Your role is to create database schemas that accurately reflect business entities and their relationships, ensuring:
1. Proper data types and constraints
2. Optimized indexes for performance
3. Foreign key relationships
4. Data integrity constraints
5. Scalable design patterns

You generate schemas for various business domains with domain-specific optimizations.`);
        this.aiCodeGenerator = new AICodeGenerator_1.AICodeGenerator();
    }
    canHandle(task) {
        return task.type === 'database_schema_generation';
    }
    async execute(task) {
        return { success: true, data: {} };
    }
    async generateSchemaFromRequirements(requirements, spec) {
        this.logInfo('Generating database schema using AI-driven analysis');
        // Extract semantic analysis from spec metadata
        const semanticAnalysis = spec.metadata?.semanticAnalysis || spec.businessDomain;
        if (!semanticAnalysis) {
            throw new Error('Semantic analysis required for AI-driven schema generation');
        }
        return await this.generateSchemaFromSemanticAnalysis(semanticAnalysis, spec);
    }
    async generateSchemaFromSemanticAnalysis(semanticAnalysis, spec) {
        this.logInfo('Generating database schema from semantic analysis', {
            domain: semanticAnalysis.domain,
            entitiesCount: semanticAnalysis.entities.length
        });
        const context = {
            projectName: spec.projectName,
            businessDomain: semanticAnalysis,
            targetFramework: 'PostgreSQL',
            architecturalPatterns: ['Relational Design', 'Foreign Key Constraints', 'Indexing Strategy'],
            existingEntities: [],
            relatedEntities: semanticAnalysis.entities,
        };
        // Generate complete schema using AI
        const schemaCode = await this.aiCodeGenerator.generateDatabaseSchema(semanticAnalysis.entities, context);
        // Parse the generated SQL to extract components
        const schema = this.parseGeneratedSchema(schemaCode.content, semanticAnalysis);
        this.logInfo('Database schema generated successfully', {
            tablesCount: schema.tables.length,
            indexesCount: schema.indexes.length
        });
        return schema;
    }
    parseGeneratedSchema(sqlContent, semanticAnalysis) {
        const tables = [];
        const indexes = [];
        const seedData = [];
        const migrations = [];
        // Extract CREATE TABLE statements
        const tableRegex = /CREATE TABLE\s+(\w+)\s*\(([\s\S]*?)\);/gi;
        let match;
        while ((match = tableRegex.exec(sqlContent)) !== null) {
            const tableName = match[1];
            const columns = match[2].trim();
            tables.push({
                name: tableName,
                columns: columns,
                indexes: [],
                constraints: []
            });
        }
        // Extract CREATE INDEX statements
        const indexRegex = /CREATE\s+(?:UNIQUE\s+)?INDEX\s+[\w\s]+ON\s+[\w\s]+\([^)]+\);/gi;
        let indexMatch;
        while ((indexMatch = indexRegex.exec(sqlContent)) !== null) {
            indexes.push(indexMatch[0]);
        }
        // Extract INSERT statements for seed data
        const insertRegex = /INSERT\s+INTO\s+[\s\S]*?;/gi;
        let insertMatch;
        while ((insertMatch = insertRegex.exec(sqlContent)) !== null) {
            seedData.push(insertMatch[0]);
        }
        // Create migration script
        migrations.push(`-- Migration: Initial schema for ${semanticAnalysis.domain} domain\n${sqlContent}`);
        return {
            tables,
            indexes,
            seedData,
            migrations
        };
    }
    generateDatabaseInitScript(schema, applicationContext) {
        const systemType = applicationContext || 'application system';
        let script = `-- Database initialization script for ${systemType}
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

`;
        // Add table creation
        schema.tables.forEach(table => {
            script += `
-- Create ${table.name} table
CREATE TABLE IF NOT EXISTS ${table.name} (${table.columns}
);
`;
        });
        // Add indexes
        script += `
-- Create indexes for better performance
`;
        schema.indexes.forEach(index => {
            script += index + '\n';
        });
        // Add seed data
        if (schema.seedData.length > 0) {
            script += `
-- Insert sample data (remove in production)
`;
            schema.seedData.forEach(seed => {
                script += seed + '\n';
            });
        }
        return script;
    }
}
exports.DatabaseSchemaGenerator = DatabaseSchemaGenerator;
//# sourceMappingURL=DatabaseSchemaGenerator.js.map
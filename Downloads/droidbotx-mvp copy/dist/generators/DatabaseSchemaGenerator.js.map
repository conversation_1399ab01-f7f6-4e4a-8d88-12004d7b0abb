{"version": 3, "file": "DatabaseSchemaGenerator.js", "sourceRoot": "", "sources": ["../../src/generators/DatabaseSchemaGenerator.ts"], "names": [], "mappings": ";;;AACA,6DAA0D;AAE1D,iDAA8C;AAgB9C,MAAa,uBAAwB,SAAQ,qBAAS;IAGpD;QACE,KAAK,CACH,yBAAyB,EACzB,0EAA0E,EAC1E;;;;;;;;;sFASgF,CACjF,CAAC;QAEF,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;IAC/C,CAAC;IAEM,SAAS,CAAC,IAAS;QACxB,OAAO,IAAI,CAAC,IAAI,KAAK,4BAA4B,CAAC;IACpD,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAS;QAC5B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,8BAA8B,CAAC,YAAsB,EAAE,IAA4B;QAC9F,IAAI,CAAC,OAAO,CAAC,qDAAqD,CAAC,CAAC;QAEpE,+CAA+C;QAC/C,MAAM,gBAAgB,GAA4B,IAAY,CAAC,QAAQ,EAAE,gBAAgB,IAAK,IAAY,CAAC,cAAc,CAAC;QAE1H,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,kCAAkC,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC/E,CAAC;IAEM,KAAK,CAAC,kCAAkC,CAC7C,gBAAwC,EACxC,IAA4B;QAE5B,IAAI,CAAC,OAAO,CAAC,mDAAmD,EAAE;YAChE,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,aAAa,EAAE,gBAAgB,CAAC,QAAQ,CAAC,MAAM;SAChD,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG;YACd,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,gBAAgB;YAChC,eAAe,EAAE,YAAY;YAC7B,qBAAqB,EAAE,CAAC,mBAAmB,EAAE,yBAAyB,EAAE,mBAAmB,CAAC;YAC5F,gBAAgB,EAAE,EAAE;YACpB,eAAe,EAAE,gBAAgB,CAAC,QAAQ;SAC3C,CAAC;QAEF,oCAAoC;QACpC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEzG,gDAAgD;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAE/E,IAAI,CAAC,OAAO,CAAC,wCAAwC,EAAE;YACrD,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM;YACjC,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;SACpC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,UAAkB,EAAE,gBAAwC;QACvF,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,kCAAkC;QAClC,MAAM,UAAU,GAAG,0CAA0C,CAAC;QAC9D,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACtD,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,EAAE;aAChB,CAAC,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,MAAM,UAAU,GAAG,gEAAgE,CAAC;QACpF,IAAI,UAAU,CAAC;QAEf,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QAED,0CAA0C;QAC1C,MAAM,WAAW,GAAG,6BAA6B,CAAC;QAClD,IAAI,WAAW,CAAC;QAEhB,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC7D,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,0BAA0B;QAC1B,UAAU,CAAC,IAAI,CAAC,oCAAoC,gBAAgB,CAAC,MAAM,YAAY,UAAU,EAAE,CAAC,CAAC;QAErG,OAAO;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;SACX,CAAC;IACJ,CAAC;IAEM,0BAA0B,CAAC,MAAsB,EAAE,kBAA2B;QACnF,MAAM,UAAU,GAAG,kBAAkB,IAAI,oBAAoB,CAAC;QAC9D,IAAI,MAAM,GAAG,yCAAyC,UAAU;;;;;;CAMnE,CAAC;QAEE,qBAAqB;QACrB,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC5B,MAAM,IAAI;YACJ,KAAK,CAAC,IAAI;6BACO,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO;;CAExD,CAAC;QACE,CAAC,CAAC,CAAC;QAEH,cAAc;QACd,MAAM,IAAI;;CAEb,CAAC;QACE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC7B,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI;;CAEf,CAAC;YACI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC7B,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC;YACxB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAnKD,0DAmKC"}
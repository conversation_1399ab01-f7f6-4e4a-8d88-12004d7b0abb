import { TechnicalSpecification } from '../agents/PlanningAgent';
import { BusinessDomainAnalysis } from '../core/SemanticAnalyzer';
import { BaseAgent } from '../core/BaseAgent';
export interface APIEndpoint {
    path: string;
    content: string;
    dependencies?: string[];
}
export declare class DomainAPIGenerator extends BaseAgent {
    private aiCodeGenerator;
    constructor();
    canHandle(task: any): boolean;
    execute(task: any): Promise<any>;
    generateAPIsFromRequirements(requirements: string[], spec: TechnicalSpecification, domainAnalysis?: any): Promise<{
        [filePath: string]: string;
    }>;
    private convertDomainAnalysis;
    generateAPIsFromSemanticAnalysis(semanticAnalysis: BusinessDomainAnalysis, spec: TechnicalSpecification): Promise<{
        [filePath: string]: string;
    }>;
    private generateModelInterface;
    private generateRouterIndex;
}
//# sourceMappingURL=DomainAPIGenerator.d.ts.map
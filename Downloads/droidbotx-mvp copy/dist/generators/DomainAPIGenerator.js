"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainAPIGenerator = void 0;
const AICodeGenerator_1 = require("../core/AICodeGenerator");
const BaseAgent_1 = require("../core/BaseAgent");
class DomainAPIGenerator extends BaseAgent_1.BaseAgent {
    constructor() {
        super('DomainAPIGenerator', 'Generates domain-specific API endpoints using AI-driven analysis', `You are a backend API architect specializing in generating domain-specific REST APIs based on business entity analysis.

Your role is to create API endpoints that accurately reflect business operations and entities, ensuring:
1. RESTful design principles
2. Proper HTTP methods and status codes
3. Input validation and error handling
4. Authentication and authorization
5. Business rule enforcement

You generate APIs for various business domains with domain-specific operations and validations.`);
        this.aiCodeGenerator = new AICodeGenerator_1.AICodeGenerator();
    }
    canHandle(task) {
        return task.type === 'api_generation';
    }
    async execute(task) {
        return { success: true, data: {} };
    }
    async generateAPIsFromRequirements(requirements, spec, domainAnalysis) {
        // Use provided domain analysis or extract from spec metadata
        let semanticAnalysis;
        if (domainAnalysis) {
            // Convert BusinessLogicAgent domain analysis to BusinessDomainAnalysis format
            semanticAnalysis = this.convertDomainAnalysis(domainAnalysis, spec);
        }
        else {
            semanticAnalysis = spec.metadata?.semanticAnalysis || spec.businessDomain;
        }
        if (!semanticAnalysis) {
            throw new Error('Domain analysis required for AI-driven API generation');
        }
        this.logInfo('Generating APIs using AI-driven analysis', {
            domain: semanticAnalysis.domain,
            entitiesCount: semanticAnalysis.entities?.length || 0
        });
        return await this.generateAPIsFromSemanticAnalysis(semanticAnalysis, spec);
    }
    convertDomainAnalysis(domainAnalysis, spec) {
        // Convert BusinessLogicAgent domain analysis format to BusinessDomainAnalysis format
        const entities = (domainAnalysis.entities || []).map((entity) => ({
            name: entity.name,
            description: entity.description,
            fields: (entity.attributes || []).map((attr) => ({
                name: attr,
                type: 'string',
                required: true,
                description: `${attr} field for ${entity.name}`
            })),
            relationships: (entity.relationships || []).map((rel) => ({
                type: 'reference',
                targetEntity: rel,
                cardinality: 'one-to-many'
            })),
            operations: (entity.operations || ['create', 'read', 'update', 'delete']).map((op) => ({
                name: op,
                type: op,
                description: `${op} operation for ${entity.name}`,
                parameters: [],
                returnType: entity.name
            })),
            validations: []
        }));
        return {
            domain: spec.businessDomain?.name || 'application',
            confidence: 0.9, // High confidence since it's AI-generated
            entities,
            workflows: (domainAnalysis.workflows || []).map((workflow) => ({
                name: workflow.name,
                description: workflow.description,
                steps: workflow.steps || [],
                entities: workflow.entities || [],
                triggers: workflow.triggers || []
            })),
            userRoles: (domainAnalysis.userRoles || []).map((role) => ({
                name: role.name,
                description: role.description,
                permissions: role.permissions || [],
                workflows: role.workflows || []
            })),
            technicalRequirements: [],
            codeGenerationContext: {
                primaryFrameworks: {
                    frontend: 'React with TypeScript',
                    backend: 'Express.js with TypeScript',
                    database: 'PostgreSQL'
                },
                architecturalPatterns: ['RESTful API', 'MVC Pattern'],
                securityRequirements: ['JWT Authentication', 'Input Validation'],
                performanceRequirements: ['Response Caching', 'Database Indexing'],
                integrationRequirements: ['REST API', 'Database Integration']
            }
        };
    }
    async generateAPIsFromSemanticAnalysis(semanticAnalysis, spec) {
        const files = {};
        const context = {
            projectName: spec.projectName,
            businessDomain: semanticAnalysis,
            targetFramework: 'Express.js with TypeScript',
            architecturalPatterns: ['RESTful API', 'MVC Pattern', 'Service Layer'],
            existingEntities: (semanticAnalysis.entities || []).map(e => e.name),
            relatedEntities: semanticAnalysis.entities || [],
        };
        // Generate API routes for each business entity
        for (const entity of semanticAnalysis.entities || []) {
            const routeRequest = {
                type: 'backend-route',
                entity,
                context,
                specifications: {
                    fileName: `${entity.name.toLowerCase()}Routes.ts`,
                    purpose: `REST API routes for ${entity.name} entity in ${semanticAnalysis.domain} domain`,
                    requirements: [
                        `Complete CRUD operations for ${entity.name}`,
                        'Authentication and authorization middleware',
                        'Input validation using Joi or similar',
                        'Comprehensive error handling',
                        'Business rule enforcement',
                        'Proper HTTP status codes',
                        'API documentation comments',
                    ],
                    dependencies: ['express', 'joi', 'jsonwebtoken', 'bcryptjs'],
                    businessRules: entity.operations.flatMap(op => op.businessRules),
                    validationRules: entity.validations.map(v => v.rule),
                    securityRequirements: [
                        'JWT authentication',
                        'Role-based access control',
                        'Input sanitization',
                        'SQL injection prevention',
                        'Rate limiting',
                    ],
                },
            };
            const routeCode = await this.aiCodeGenerator.generateExpressRoute(routeRequest);
            files[`backend/src/routes/${entity.name.toLowerCase()}Routes.ts`] = routeCode.content;
            // Generate service class for business logic
            const serviceRequest = {
                type: 'service-class',
                entity,
                context,
                specifications: {
                    fileName: `${entity.name}Service.ts`,
                    purpose: `Business logic service for ${entity.name} entity`,
                    requirements: [
                        `Business operations for ${entity.name}`,
                        'Data validation and transformation',
                        'Business rule enforcement',
                        'Database interaction',
                        'Error handling and logging',
                    ],
                    dependencies: ['pg', 'joi'],
                    businessRules: entity.operations.flatMap(op => op.businessRules),
                    validationRules: entity.validations.map(v => v.rule),
                    securityRequirements: ['Data validation', 'Business rule enforcement'],
                },
            };
            const serviceCode = await this.aiCodeGenerator.generateServiceClass(serviceRequest);
            files[`backend/src/services/${entity.name}Service.ts`] = serviceCode.content;
            // Generate model/interface
            const modelCode = await this.generateModelInterface(entity, semanticAnalysis);
            files[`backend/src/models/${entity.name}.ts`] = modelCode;
        }
        // Generate main router index
        const routerIndexCode = await this.generateRouterIndex(semanticAnalysis.entities, semanticAnalysis);
        files['backend/src/routes/index.ts'] = routerIndexCode;
        this.logInfo('AI-driven API generation completed', {
            filesGenerated: Object.keys(files).length,
            entitiesProcessed: semanticAnalysis.entities.length
        });
        return files;
    }
    async generateModelInterface(entity, semanticAnalysis) {
        const modelPrompt = `Generate a TypeScript interface for ${entity.name} entity in a ${semanticAnalysis.domain} application.

Entity: ${entity.name}
Description: ${entity.description}

Fields:
${entity.fields.map(field => `- ${field.name}: ${field.type}${field.required ? ' (required)' : ''} - ${field.description}`).join('\n')}

Relationships:
${entity.relationships.map(rel => `- ${rel.type} with ${rel.target}: ${rel.description}`).join('\n')}

Generate a complete TypeScript interface with:
1. Proper field types
2. Optional/required field markers
3. JSDoc comments for documentation
4. Validation constraints as comments
5. Related entity type references

Return ONLY the TypeScript interface code, no additional text.`;
        return await this.generateSingleLLMResponse(modelPrompt, {
            temperature: 0.2,
            maxTokens: 2000
        });
    }
    async generateRouterIndex(entities, semanticAnalysis) {
        const routerPrompt = `Generate a main router index file for a ${semanticAnalysis.domain} application with Express.js.

Entities to include:
${entities.map(entity => `- ${entity.name}: ${entity.description}`).join('\n')}

Generate a TypeScript file that:
1. Imports all entity route files
2. Sets up Express router
3. Configures middleware (authentication, logging, etc.)
4. Mounts all entity routes with proper prefixes
5. Includes error handling middleware
6. Exports the configured router

Route prefixes should be: /api/{entityNamePlural}

Return ONLY the TypeScript code, no additional text.`;
        return await this.generateSingleLLMResponse(routerPrompt, {
            temperature: 0.2,
            maxTokens: 2000
        });
    }
}
exports.DomainAPIGenerator = DomainAPIGenerator;
//# sourceMappingURL=DomainAPIGenerator.js.map
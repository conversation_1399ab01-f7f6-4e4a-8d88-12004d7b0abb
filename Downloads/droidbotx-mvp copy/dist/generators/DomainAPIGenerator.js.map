{"version": 3, "file": "DomainAPIGenerator.js", "sourceRoot": "", "sources": ["../../src/generators/DomainAPIGenerator.ts"], "names": [], "mappings": ";;;AACA,6DAAiF;AAEjF,iDAA8C;AAQ9C,MAAa,kBAAmB,SAAQ,qBAAS;IAG/C;QACE,KAAK,CACH,oBAAoB,EACpB,kEAAkE,EAClE;;;;;;;;;gGAS0F,CAC3F,CAAC;QAEF,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;IAC/C,CAAC;IAEM,SAAS,CAAC,IAAS;QACxB,OAAO,IAAI,CAAC,IAAI,KAAK,gBAAgB,CAAC;IACxC,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAS;QAC5B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,4BAA4B,CAAC,YAAsB,EAAE,IAA4B,EAAE,cAAoB;QAClH,6DAA6D;QAC7D,IAAI,gBAAwC,CAAC;QAE7C,IAAI,cAAc,EAAE,CAAC;YACnB,8EAA8E;YAC9E,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACtE,CAAC;aAAM,CAAC;YACN,gBAAgB,GAAI,IAAY,CAAC,QAAQ,EAAE,gBAAgB,IAAK,IAAY,CAAC,cAAc,CAAC;QAC9F,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,0CAA0C,EAAE;YACvD,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,aAAa,EAAE,gBAAgB,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC;SACtD,CAAC,CAAC;QAEH,OAAO,MAAM,IAAI,CAAC,gCAAgC,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC7E,CAAC;IAEO,qBAAqB,CAAC,cAAmB,EAAE,IAA4B;QAC7E,qFAAqF;QACrF,MAAM,QAAQ,GAAqB,CAAC,cAAc,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;YACvF,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,MAAM,EAAE,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,CAAC;gBACvD,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,GAAG,IAAI,cAAc,MAAM,CAAC,IAAI,EAAE;aAChD,CAAC,CAAC;YACH,aAAa,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC;gBAChE,IAAI,EAAE,WAAW;gBACjB,YAAY,EAAE,GAAG;gBACjB,WAAW,EAAE,aAAa;aAC3B,CAAC,CAAC;YACH,UAAU,EAAE,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAU,EAAE,EAAE,CAAC,CAAC;gBAC7F,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,WAAW,EAAE,GAAG,EAAE,kBAAkB,MAAM,CAAC,IAAI,EAAE;gBACjD,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,MAAM,CAAC,IAAI;aACxB,CAAC,CAAC;YACH,WAAW,EAAE,EAAE;SAChB,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,aAAa;YAClD,UAAU,EAAE,GAAG,EAAE,0CAA0C;YAC3D,QAAQ;YACR,SAAS,EAAE,CAAC,cAAc,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAAC,CAAC;gBAClE,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,EAAE;gBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,EAAE;gBACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,EAAE;aAClC,CAAC,CAAC;YACH,SAAS,EAAE,CAAC,cAAc,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC9D,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,EAAE;aAChC,CAAC,CAAC;YACH,qBAAqB,EAAE,EAAE;YACzB,qBAAqB,EAAE;gBACrB,iBAAiB,EAAE;oBACjB,QAAQ,EAAE,uBAAuB;oBACjC,OAAO,EAAE,4BAA4B;oBACrC,QAAQ,EAAE,YAAY;iBACvB;gBACD,qBAAqB,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;gBACrD,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC;gBAChE,uBAAuB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;gBAClE,uBAAuB,EAAE,CAAC,UAAU,EAAE,sBAAsB,CAAC;aAC9D;SACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,gCAAgC,CAC3C,gBAAwC,EACxC,IAA4B;QAE5B,MAAM,KAAK,GAAmC,EAAE,CAAC;QAEjD,MAAM,OAAO,GAAG;YACd,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,gBAAgB;YAChC,eAAe,EAAE,4BAA4B;YAC7C,qBAAqB,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;YACtE,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACpE,eAAe,EAAE,gBAAgB,CAAC,QAAQ,IAAI,EAAE;SACjD,CAAC;QAEF,+CAA+C;QAC/C,KAAK,MAAM,MAAM,IAAI,gBAAgB,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;YACrD,MAAM,YAAY,GAA0B;gBAC1C,IAAI,EAAE,eAAe;gBACrB,MAAM;gBACN,OAAO;gBACP,cAAc,EAAE;oBACd,QAAQ,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW;oBACjD,OAAO,EAAE,uBAAuB,MAAM,CAAC,IAAI,cAAc,gBAAgB,CAAC,MAAM,SAAS;oBACzF,YAAY,EAAE;wBACZ,gCAAgC,MAAM,CAAC,IAAI,EAAE;wBAC7C,6CAA6C;wBAC7C,uCAAuC;wBACvC,8BAA8B;wBAC9B,2BAA2B;wBAC3B,0BAA0B;wBAC1B,4BAA4B;qBAC7B;oBACD,YAAY,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,CAAC;oBAC5D,aAAa,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;oBAChE,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;oBACpD,oBAAoB,EAAE;wBACpB,oBAAoB;wBACpB,2BAA2B;wBAC3B,oBAAoB;wBACpB,0BAA0B;wBAC1B,eAAe;qBAChB;iBACF;aACF,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAChF,KAAK,CAAC,sBAAsB,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;YAEtF,4CAA4C;YAC5C,MAAM,cAAc,GAA0B;gBAC5C,IAAI,EAAE,eAAe;gBACrB,MAAM;gBACN,OAAO;gBACP,cAAc,EAAE;oBACd,QAAQ,EAAE,GAAG,MAAM,CAAC,IAAI,YAAY;oBACpC,OAAO,EAAE,8BAA8B,MAAM,CAAC,IAAI,SAAS;oBAC3D,YAAY,EAAE;wBACZ,2BAA2B,MAAM,CAAC,IAAI,EAAE;wBACxC,oCAAoC;wBACpC,2BAA2B;wBAC3B,sBAAsB;wBACtB,4BAA4B;qBAC7B;oBACD,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;oBAC3B,aAAa,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;oBAChE,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;oBACpD,oBAAoB,EAAE,CAAC,iBAAiB,EAAE,2BAA2B,CAAC;iBACvE;aACF,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YACpF,KAAK,CAAC,wBAAwB,MAAM,CAAC,IAAI,YAAY,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;YAE7E,2BAA2B;YAC3B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAC9E,KAAK,CAAC,sBAAsB,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,SAAS,CAAC;QAC5D,CAAC;QAED,6BAA6B;QAC7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QACpG,KAAK,CAAC,6BAA6B,CAAC,GAAG,eAAe,CAAC;QAEvD,IAAI,CAAC,OAAO,CAAC,oCAAoC,EAAE;YACjD,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM;YACzC,iBAAiB,EAAE,gBAAgB,CAAC,QAAQ,CAAC,MAAM;SACpD,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAAsB,EAAE,gBAAwC;QACnG,MAAM,WAAW,GAAG,uCAAuC,MAAM,CAAC,IAAI,gBAAgB,gBAAgB,CAAC,MAAM;;UAEvG,MAAM,CAAC,IAAI;eACN,MAAM,CAAC,WAAW;;;EAG/B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGpI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;+DASrC,CAAC;QAE5D,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE;YACvD,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,QAA0B,EAAE,gBAAwC;QACpG,MAAM,YAAY,GAAG,2CAA2C,gBAAgB,CAAC,MAAM;;;EAGzF,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;qDAYzB,CAAC;QAElD,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE;YACxD,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;IACL,CAAC;CACF;AA5PD,gDA4PC"}
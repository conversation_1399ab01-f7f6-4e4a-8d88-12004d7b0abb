import { ConfigManager } from './core/ConfigManager';
import { Orchestrator } from './orchestration/Orchestrator';
export declare class DroidBotX {
    private config;
    private logger;
    private llmProvider;
    private toolManager;
    private orchestrator;
    constructor();
    initialize(): Promise<void>;
    private testLLMConnection;
    getOrchestrator(): Orchestrator;
    getConfig(): ConfigManager;
}
export { ConfigManager, Logger, LLMProviderSystem, ToolManager, BaseAgent, } from './core';
export { Orchestrator, } from './orchestration';
export { PlanningAgent, BusinessLogicAgent, CodingAgent, TestingAgent, DeploymentAgent, } from './agents';
export type { WorkflowPhase, } from './orchestration';
export type { TechnicalSpecification, GeneratedCode, DeploymentConfiguration, } from './agents';
//# sourceMappingURL=index.d.ts.map
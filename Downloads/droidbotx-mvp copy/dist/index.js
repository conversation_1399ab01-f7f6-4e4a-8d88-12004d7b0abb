"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeploymentAgent = exports.TestingAgent = exports.CodingAgent = exports.BusinessLogicAgent = exports.PlanningAgent = exports.Orchestrator = exports.BaseAgent = exports.ToolManager = exports.LLMProviderSystem = exports.Logger = exports.ConfigManager = exports.DroidBotX = void 0;
const ConfigManager_1 = require("./core/ConfigManager");
const Logger_1 = require("./core/Logger");
const LLMProviderSystem_1 = require("./core/LLMProviderSystem");
const ToolManager_1 = require("./core/ToolManager");
const Orchestrator_1 = require("./orchestration/Orchestrator");
const agents_1 = require("./agents");
const TestingAgent_1 = require("./agents/TestingAgent");
const BusinessLogicAgent_1 = require("./agents/BusinessLogicAgent");
const DatabaseAgent_1 = require("./agents/DatabaseAgent");
class DroidBotX {
    constructor() {
        this.config = ConfigManager_1.ConfigManager.getInstance();
        this.logger = Logger_1.Logger.getInstance();
        this.llmProvider = LLMProviderSystem_1.LLMProviderSystem.getInstance();
        this.toolManager = ToolManager_1.ToolManager.getInstance();
        this.orchestrator = Orchestrator_1.Orchestrator.getInstance();
    }
    async initialize() {
        this.logger.info('Initializing DroidBotX MVP...');
        try {
            // Test LLM connection
            await this.testLLMConnection();
            // Register core agents (simplified from 10 to 6)
            const planningAgent = new agents_1.PlanningAgent();
            const businessLogicAgent = new BusinessLogicAgent_1.BusinessLogicAgent();
            const codingAgent = new agents_1.CodingAgent();
            const databaseAgent = new DatabaseAgent_1.DatabaseAgent();
            const testingAgent = new TestingAgent_1.TestingAgent();
            const deploymentAgent = new agents_1.DeploymentAgent();
            this.orchestrator.registerAgent(planningAgent);
            this.orchestrator.registerAgent(businessLogicAgent);
            this.orchestrator.registerAgent(codingAgent);
            this.orchestrator.registerAgent(databaseAgent);
            this.orchestrator.registerAgent(testingAgent);
            this.orchestrator.registerAgent(deploymentAgent);
            this.logger.info('Core agents registered successfully', {
                agents: [
                    planningAgent.getName(),
                    businessLogicAgent.getName(),
                    codingAgent.getName(),
                    databaseAgent.getName(),
                    testingAgent.getName(),
                    deploymentAgent.getName()
                ],
            });
            this.logger.info('DroidBotX MVP initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize DroidBotX MVP', {
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            throw error;
        }
    }
    async testLLMConnection() {
        try {
            const response = await this.llmProvider.generateSingleResponse('Hello! Please respond with "Connection successful" to confirm the LLM is working.', { maxTokens: 50 });
            this.logger.info('LLM connection test completed', {
                response: response.substring(0, 100),
            });
        }
        catch (error) {
            throw new Error(`LLM connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    getOrchestrator() {
        return this.orchestrator;
    }
    getConfig() {
        return this.config;
    }
}
exports.DroidBotX = DroidBotX;
// Export main classes for external use
var core_1 = require("./core");
Object.defineProperty(exports, "ConfigManager", { enumerable: true, get: function () { return core_1.ConfigManager; } });
Object.defineProperty(exports, "Logger", { enumerable: true, get: function () { return core_1.Logger; } });
Object.defineProperty(exports, "LLMProviderSystem", { enumerable: true, get: function () { return core_1.LLMProviderSystem; } });
Object.defineProperty(exports, "ToolManager", { enumerable: true, get: function () { return core_1.ToolManager; } });
Object.defineProperty(exports, "BaseAgent", { enumerable: true, get: function () { return core_1.BaseAgent; } });
var orchestration_1 = require("./orchestration");
Object.defineProperty(exports, "Orchestrator", { enumerable: true, get: function () { return orchestration_1.Orchestrator; } });
var agents_2 = require("./agents");
Object.defineProperty(exports, "PlanningAgent", { enumerable: true, get: function () { return agents_2.PlanningAgent; } });
Object.defineProperty(exports, "BusinessLogicAgent", { enumerable: true, get: function () { return agents_2.BusinessLogicAgent; } });
Object.defineProperty(exports, "CodingAgent", { enumerable: true, get: function () { return agents_2.CodingAgent; } });
Object.defineProperty(exports, "TestingAgent", { enumerable: true, get: function () { return agents_2.TestingAgent; } });
Object.defineProperty(exports, "DeploymentAgent", { enumerable: true, get: function () { return agents_2.DeploymentAgent; } });
// Main entry point for testing
async function main() {
    try {
        const droidBotX = new DroidBotX();
        await droidBotX.initialize();
        console.log('DroidBotX MVP is ready!');
        console.log('Available configuration:');
        console.log('- OpenRouter Model:', droidBotX.getConfig().openRouter.defaultModel);
        console.log('- Log Level:', droidBotX.getConfig().logging.level);
        console.log('- Environment:', droidBotX.getConfig().application.nodeEnv);
    }
    catch (error) {
        console.error('Failed to start DroidBotX MVP:', error);
        process.exit(1);
    }
}
// Run main function if this file is executed directly
if (require.main === module) {
    main();
}
//# sourceMappingURL=index.js.map
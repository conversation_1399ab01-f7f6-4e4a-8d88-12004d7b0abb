import { Pool } from 'pg';
export interface DatabaseConnectionConfig {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl?: boolean | object;
    maxConnections?: number;
    idleTimeoutMs?: number;
    connectionTimeoutMs?: number;
}
export interface DatabaseEnvironment {
    id: string;
    config: DatabaseConnectionConfig;
    pool: Pool;
    containerName?: string;
    isIsolated: boolean;
    createdAt: Date;
    lastUsed: Date;
}
export interface SchemaValidationResult {
    isValid: boolean;
    missingTables: string[];
    extraTables: string[];
    schemaErrors: string[];
    score: number;
}
export declare class DatabaseManager {
    private static instance;
    private logger;
    private environments;
    private mainPool;
    private testPool;
    private containerProcesses;
    private retryAttempts;
    private retryDelayMs;
    private constructor();
    static getInstance(): DatabaseManager;
    /**
     * Initialize main database connection
     */
    private initializeMainDatabase;
    /**
     * Get main database configuration from environment
     */
    private getMainDatabaseConfig;
    /**
     * Create a new database pool with enhanced configuration
     */
    private createPool;
    /**
     * Test database connection with retry mechanism
     */
    private testConnection;
    /**
     * Create isolated database environment for workflow execution
     */
    createIsolatedEnvironment(sessionId: string): Promise<DatabaseEnvironment>;
    /**
     * Create isolated database configuration
     */
    private createIsolatedDatabaseConfig;
    /**
     * Create database container for isolated environment
     */
    private createDatabaseContainer;
    /**
     * Initialize database schema
     */
    private initializeSchema;
    /**
     * Get database environment for session
     */
    getEnvironment(sessionId: string): DatabaseEnvironment | null;
    /**
     * Get main database pool
     */
    getMainPool(): Pool;
    /**
     * Validate database schema against expected structure
     */
    validateSchema(pool: Pool, expectedTables: string[]): Promise<SchemaValidationResult>;
    /**
     * Clean up isolated environment
     */
    cleanupEnvironment(environmentId: string): Promise<void>;
    /**
     * Remove database container
     */
    private removeContainer;
    /**
     * Cleanup all environments and connections
     */
    cleanup(): Promise<void>;
    /**
     * Utility method for delays
     */
    private delay;
}
//# sourceMappingURL=DatabaseManager.d.ts.map
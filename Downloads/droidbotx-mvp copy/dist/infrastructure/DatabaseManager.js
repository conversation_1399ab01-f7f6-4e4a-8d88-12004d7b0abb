"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = void 0;
const pg_1 = require("pg");
const Logger_1 = require("../core/Logger");
const child_process_1 = require("child_process");
class DatabaseManager {
    constructor() {
        this.environments = new Map();
        this.mainPool = null;
        this.testPool = null;
        this.containerProcesses = new Map();
        this.retryAttempts = 3;
        this.retryDelayMs = 1000;
        this.logger = Logger_1.Logger.getInstance();
        this.initializeMainDatabase();
    }
    static getInstance() {
        if (!DatabaseManager.instance) {
            DatabaseManager.instance = new DatabaseManager();
        }
        return DatabaseManager.instance;
    }
    /**
     * Initialize main database connection
     */
    async initializeMainDatabase() {
        try {
            const config = this.getMainDatabaseConfig();
            this.mainPool = this.createPool(config);
            // Test connection
            await this.testConnection(this.mainPool);
            this.logger.info('Main database initialized successfully', {
                host: config.host,
                port: config.port,
                database: config.database
            });
        }
        catch (error) {
            this.logger.error('Failed to initialize main database', { error });
            throw error;
        }
    }
    /**
     * Get main database configuration from environment
     */
    getMainDatabaseConfig() {
        return {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '5434'),
            database: process.env.DB_NAME || 'droidbotx_db',
            user: process.env.DB_USER || 'droidbotx_user',
            password: process.env.DB_PASSWORD || 'droidbotx_secure_password_2025',
            maxConnections: parseInt(process.env.DB_POOL_MAX || '20'),
            idleTimeoutMs: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
            connectionTimeoutMs: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
            ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
        };
    }
    /**
     * Create a new database pool with enhanced configuration
     */
    createPool(config) {
        const poolConfig = {
            host: config.host,
            port: config.port,
            database: config.database,
            user: config.user,
            password: config.password,
            ssl: config.ssl,
            max: config.maxConnections || 20,
            min: 2,
            idleTimeoutMillis: config.idleTimeoutMs || 30000,
            connectionTimeoutMillis: config.connectionTimeoutMs || 10000
        };
        const pool = new pg_1.Pool(poolConfig);
        // Add error handling
        pool.on('error', (err) => {
            this.logger.error('Database pool error', { error: err.message });
        });
        pool.on('connect', () => {
            this.logger.debug('New database connection established');
        });
        pool.on('remove', () => {
            this.logger.debug('Database connection removed from pool');
        });
        return pool;
    }
    /**
     * Test database connection with retry mechanism
     */
    async testConnection(pool) {
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const client = await pool.connect();
                await client.query('SELECT 1');
                client.release();
                return;
            }
            catch (error) {
                this.logger.warn(`Database connection attempt ${attempt} failed`, { error });
                if (attempt === this.retryAttempts) {
                    throw new Error(`Database connection failed after ${this.retryAttempts} attempts`);
                }
                await this.delay(this.retryDelayMs * attempt);
            }
        }
    }
    /**
     * Create isolated database environment for workflow execution
     */
    async createIsolatedEnvironment(sessionId) {
        const environmentId = `isolated_${sessionId}_${Date.now()}`;
        try {
            // Create isolated database configuration
            const config = await this.createIsolatedDatabaseConfig(environmentId);
            // Create database container if needed
            const containerName = await this.createDatabaseContainer(environmentId, config);
            // Create connection pool
            const pool = this.createPool(config);
            // Test connection
            await this.testConnection(pool);
            // Initialize schema
            await this.initializeSchema(pool);
            const environment = {
                id: environmentId,
                config,
                pool,
                containerName,
                isIsolated: true,
                createdAt: new Date(),
                lastUsed: new Date()
            };
            this.environments.set(environmentId, environment);
            this.logger.info('Isolated database environment created', {
                environmentId,
                sessionId,
                containerName
            });
            return environment;
        }
        catch (error) {
            this.logger.error('Failed to create isolated database environment', {
                environmentId,
                sessionId,
                error
            });
            throw error;
        }
    }
    /**
     * Create isolated database configuration
     */
    async createIsolatedDatabaseConfig(environmentId) {
        const basePort = 5435;
        const port = basePort + Math.floor(Math.random() * 1000);
        return {
            host: 'localhost',
            port,
            database: `test_${environmentId}`,
            user: `test_user_${environmentId}`,
            password: `test_pass_${Date.now()}`,
            maxConnections: 10,
            idleTimeoutMs: 15000,
            connectionTimeoutMs: 5000
        };
    }
    /**
     * Create database container for isolated environment
     */
    async createDatabaseContainer(environmentId, config) {
        const containerName = `droidbotx_test_${environmentId}`;
        return new Promise((resolve, reject) => {
            const dockerArgs = [
                'run', '-d',
                '--name', containerName,
                '-e', `POSTGRES_DB=${config.database}`,
                '-e', `POSTGRES_USER=${config.user}`,
                '-e', `POSTGRES_PASSWORD=${config.password}`,
                '-p', `${config.port}:5432`,
                '--rm',
                'postgres:15-alpine'
            ];
            const dockerProcess = (0, child_process_1.spawn)('docker', dockerArgs);
            dockerProcess.on('close', (code) => {
                if (code === 0) {
                    this.containerProcesses.set(containerName, dockerProcess);
                    this.logger.info('Database container created', { containerName, port: config.port });
                    // Wait for container to be ready
                    setTimeout(() => resolve(containerName), 3000);
                }
                else {
                    reject(new Error(`Failed to create database container: exit code ${code}`));
                }
            });
            dockerProcess.on('error', (error) => {
                reject(new Error(`Docker process error: ${error.message}`));
            });
        });
    }
    /**
     * Initialize database schema
     */
    async initializeSchema(pool) {
        const client = await pool.connect();
        try {
            // Create basic schema for testing
            await client.query(`
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        CREATE EXTENSION IF NOT EXISTS "pgcrypto";
        
        CREATE SCHEMA IF NOT EXISTS test_schema;
        
        CREATE TABLE IF NOT EXISTS test_schema.workflow_sessions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          session_id VARCHAR(255) UNIQUE NOT NULL,
          status VARCHAR(50) DEFAULT 'active',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
            this.logger.debug('Database schema initialized');
        }
        finally {
            client.release();
        }
    }
    /**
     * Get database environment for session
     */
    getEnvironment(sessionId) {
        for (const [id, env] of this.environments.entries()) {
            if (id.includes(sessionId)) {
                env.lastUsed = new Date();
                return env;
            }
        }
        return null;
    }
    /**
     * Get main database pool
     */
    getMainPool() {
        if (!this.mainPool) {
            throw new Error('Main database pool not initialized');
        }
        return this.mainPool;
    }
    /**
     * Validate database schema against expected structure
     */
    async validateSchema(pool, expectedTables) {
        const client = await pool.connect();
        try {
            // Get existing tables
            const result = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' OR table_schema = 'test_schema'
      `);
            const existingTables = result.rows.map(row => row.table_name);
            const missingTables = expectedTables.filter(table => !existingTables.includes(table));
            const extraTables = existingTables.filter(table => !expectedTables.includes(table));
            const score = Math.max(0, 100 - (missingTables.length * 20) - (extraTables.length * 5));
            return {
                isValid: missingTables.length === 0,
                missingTables,
                extraTables,
                schemaErrors: [],
                score
            };
        }
        catch (error) {
            return {
                isValid: false,
                missingTables: expectedTables,
                extraTables: [],
                schemaErrors: [error instanceof Error ? error.message : String(error)],
                score: 0
            };
        }
        finally {
            client.release();
        }
    }
    /**
     * Clean up isolated environment
     */
    async cleanupEnvironment(environmentId) {
        const environment = this.environments.get(environmentId);
        if (!environment) {
            return;
        }
        try {
            // Close pool connections
            await environment.pool.end();
            // Remove container if exists
            if (environment.containerName) {
                await this.removeContainer(environment.containerName);
            }
            // Remove from environments
            this.environments.delete(environmentId);
            this.logger.info('Database environment cleaned up', { environmentId });
        }
        catch (error) {
            this.logger.error('Failed to cleanup database environment', {
                environmentId,
                error
            });
        }
    }
    /**
     * Remove database container
     */
    async removeContainer(containerName) {
        return new Promise((resolve) => {
            const dockerProcess = (0, child_process_1.spawn)('docker', ['rm', '-f', containerName]);
            dockerProcess.on('close', () => {
                this.containerProcesses.delete(containerName);
                this.logger.debug('Database container removed', { containerName });
                resolve();
            });
            dockerProcess.on('error', (error) => {
                this.logger.warn('Failed to remove container', { containerName, error });
                resolve(); // Don't fail cleanup for container removal issues
            });
        });
    }
    /**
     * Cleanup all environments and connections
     */
    async cleanup() {
        // Cleanup isolated environments
        for (const environmentId of this.environments.keys()) {
            await this.cleanupEnvironment(environmentId);
        }
        // Close main pools
        if (this.mainPool) {
            await this.mainPool.end();
            this.mainPool = null;
        }
        if (this.testPool) {
            await this.testPool.end();
            this.testPool = null;
        }
        this.logger.info('Database manager cleanup completed');
    }
    /**
     * Utility method for delays
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.DatabaseManager = DatabaseManager;
//# sourceMappingURL=DatabaseManager.js.map
import { Pool } from 'pg';
export interface Migration {
    id: string;
    name: string;
    version: number;
    sql: string;
    checksum: string;
    appliedAt?: Date;
    rollbackSql?: string;
}
export interface MigrationResult {
    success: boolean;
    migrationsApplied: number;
    errors: string[];
    appliedMigrations: Migration[];
}
export interface SchemaComparison {
    isConsistent: boolean;
    missingTables: string[];
    extraTables: string[];
    missingColumns: Array<{
        table: string;
        column: string;
    }>;
    extraColumns: Array<{
        table: string;
        column: string;
    }>;
    inconsistencies: string[];
    score: number;
}
export declare class DatabaseMigrationManager {
    private logger;
    private migrationsPath;
    constructor(migrationsPath?: string);
    /**
     * Initialize migration tracking table
     */
    initializeMigrationTracking(pool: Pool): Promise<void>;
    /**
     * Load migrations from directory
     */
    loadMigrations(): Promise<Migration[]>;
    /**
     * Get applied migrations from database
     */
    getAppliedMigrations(pool: Pool): Promise<Migration[]>;
    /**
     * Apply pending migrations
     */
    applyMigrations(pool: Pool): Promise<MigrationResult>;
    /**
     * Validate schema consistency between business logic and database
     */
    validateSchemaConsistency(pool: Pool, expectedSchema: any): Promise<SchemaComparison>;
    /**
     * Generate migration SQL from schema differences
     */
    generateMigrationSQL(comparison: SchemaComparison): string;
    /**
     * Build schema map from database rows
     */
    private buildSchemaMap;
    /**
     * Check if data types are compatible
     */
    private isDataTypeCompatible;
    /**
     * Calculate checksum for migration content
     */
    private calculateChecksum;
}
//# sourceMappingURL=DatabaseMigrationManager.d.ts.map
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseMigrationManager = void 0;
const Logger_1 = require("../core/Logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class DatabaseMigrationManager {
    constructor(migrationsPath) {
        this.logger = Logger_1.Logger.getInstance();
        this.migrationsPath = migrationsPath || path.join(__dirname, '../../database/migrations');
    }
    /**
     * Initialize migration tracking table
     */
    async initializeMigrationTracking(pool) {
        const client = await pool.connect();
        try {
            await client.query(`
        CREATE TABLE IF NOT EXISTS schema_migrations (
          id SERIAL PRIMARY KEY,
          migration_id VARCHAR(255) UNIQUE NOT NULL,
          name VARCHAR(255) NOT NULL,
          version INTEGER NOT NULL,
          checksum VARCHAR(64) NOT NULL,
          applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          execution_time_ms INTEGER,
          success BOOLEAN DEFAULT true
        );
        
        CREATE INDEX IF NOT EXISTS idx_schema_migrations_version 
        ON schema_migrations(version);
        
        CREATE INDEX IF NOT EXISTS idx_schema_migrations_applied_at 
        ON schema_migrations(applied_at);
      `);
            this.logger.info('Migration tracking table initialized');
        }
        catch (error) {
            this.logger.error('Failed to initialize migration tracking', { error });
            throw error;
        }
        finally {
            client.release();
        }
    }
    /**
     * Load migrations from directory
     */
    async loadMigrations() {
        const migrations = [];
        try {
            if (!fs.existsSync(this.migrationsPath)) {
                this.logger.warn('Migrations directory not found', { path: this.migrationsPath });
                return migrations;
            }
            const files = fs.readdirSync(this.migrationsPath)
                .filter(file => file.endsWith('.sql'))
                .sort();
            for (const file of files) {
                const filePath = path.join(this.migrationsPath, file);
                const sql = fs.readFileSync(filePath, 'utf8');
                // Parse migration metadata from filename
                const match = file.match(/^(\d+)_(.+)\.sql$/);
                if (!match) {
                    this.logger.warn('Invalid migration filename format', { file });
                    continue;
                }
                const [, versionStr, name] = match;
                const version = parseInt(versionStr);
                const id = `${version}_${name}`;
                const checksum = this.calculateChecksum(sql);
                migrations.push({
                    id,
                    name,
                    version,
                    sql,
                    checksum
                });
            }
            this.logger.info('Migrations loaded', { count: migrations.length });
            return migrations;
        }
        catch (error) {
            this.logger.error('Failed to load migrations', { error });
            throw error;
        }
    }
    /**
     * Get applied migrations from database
     */
    async getAppliedMigrations(pool) {
        const client = await pool.connect();
        try {
            const result = await client.query(`
        SELECT migration_id, name, version, checksum, applied_at
        FROM schema_migrations
        ORDER BY version ASC
      `);
            return result.rows.map(row => ({
                id: row.migration_id,
                name: row.name,
                version: row.version,
                sql: '', // Not stored in database
                checksum: row.checksum,
                appliedAt: row.applied_at
            }));
        }
        catch (error) {
            // If table doesn't exist, return empty array
            if (error instanceof Error && error.message.includes('does not exist')) {
                return [];
            }
            throw error;
        }
        finally {
            client.release();
        }
    }
    /**
     * Apply pending migrations
     */
    async applyMigrations(pool) {
        const result = {
            success: true,
            migrationsApplied: 0,
            errors: [],
            appliedMigrations: []
        };
        try {
            // Initialize migration tracking
            await this.initializeMigrationTracking(pool);
            // Load all migrations
            const allMigrations = await this.loadMigrations();
            const appliedMigrations = await this.getAppliedMigrations(pool);
            // Find pending migrations
            const appliedIds = new Set(appliedMigrations.map(m => m.id));
            const pendingMigrations = allMigrations.filter(m => !appliedIds.has(m.id));
            if (pendingMigrations.length === 0) {
                this.logger.info('No pending migrations to apply');
                return result;
            }
            // Apply migrations in transaction
            const client = await pool.connect();
            try {
                await client.query('BEGIN');
                for (const migration of pendingMigrations) {
                    try {
                        const startTime = Date.now();
                        // Apply migration
                        await client.query(migration.sql);
                        const executionTime = Date.now() - startTime;
                        // Record migration
                        await client.query(`
              INSERT INTO schema_migrations 
              (migration_id, name, version, checksum, execution_time_ms)
              VALUES ($1, $2, $3, $4, $5)
            `, [migration.id, migration.name, migration.version, migration.checksum, executionTime]);
                        result.migrationsApplied++;
                        result.appliedMigrations.push(migration);
                        this.logger.info('Migration applied successfully', {
                            migrationId: migration.id,
                            executionTime
                        });
                    }
                    catch (error) {
                        const errorMessage = error instanceof Error ? error.message : String(error);
                        result.errors.push(`Migration ${migration.id}: ${errorMessage}`);
                        this.logger.error('Migration failed', {
                            migrationId: migration.id,
                            error: errorMessage
                        });
                        throw error; // Will trigger rollback
                    }
                }
                await client.query('COMMIT');
                this.logger.info('All migrations applied successfully', {
                    count: result.migrationsApplied
                });
            }
            catch (error) {
                await client.query('ROLLBACK');
                result.success = false;
                throw error;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            result.success = false;
            const errorMessage = error instanceof Error ? error.message : String(error);
            if (!result.errors.includes(errorMessage)) {
                result.errors.push(errorMessage);
            }
            this.logger.error('Migration process failed', { error: errorMessage });
        }
        return result;
    }
    /**
     * Validate schema consistency between business logic and database
     */
    async validateSchemaConsistency(pool, expectedSchema) {
        const client = await pool.connect();
        try {
            // Get current database schema
            const tablesResult = await client.query(`
        SELECT table_name, column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public'
        ORDER BY table_name, ordinal_position
      `);
            const currentSchema = this.buildSchemaMap(tablesResult.rows);
            const expectedTables = expectedSchema.tables || [];
            // Compare schemas
            const currentTableNames = Object.keys(currentSchema);
            const expectedTableNames = expectedTables.map((t) => t.name);
            const missingTables = expectedTableNames.filter((name) => !currentTableNames.includes(name));
            const extraTables = currentTableNames.filter(name => !expectedTableNames.includes(name));
            const missingColumns = [];
            const extraColumns = [];
            const inconsistencies = [];
            // Check column consistency for common tables
            for (const expectedTable of expectedTables) {
                const tableName = expectedTable.name;
                const currentTable = currentSchema[tableName];
                if (!currentTable)
                    continue;
                const expectedColumns = expectedTable.columns || [];
                const currentColumnNames = Object.keys(currentTable);
                const expectedColumnNames = expectedColumns.map((c) => c.name);
                // Find missing and extra columns
                for (const expectedCol of expectedColumnNames) {
                    if (!currentColumnNames.includes(expectedCol)) {
                        missingColumns.push({ table: tableName, column: expectedCol });
                    }
                }
                for (const currentCol of currentColumnNames) {
                    if (!expectedColumnNames.includes(currentCol)) {
                        extraColumns.push({ table: tableName, column: currentCol });
                    }
                }
                // Check data type consistency
                for (const expectedCol of expectedColumns) {
                    const currentCol = currentTable[expectedCol.name];
                    if (currentCol && expectedCol.type) {
                        if (!this.isDataTypeCompatible(currentCol.data_type, expectedCol.type)) {
                            inconsistencies.push(`Table ${tableName}.${expectedCol.name}: expected ${expectedCol.type}, got ${currentCol.data_type}`);
                        }
                    }
                }
            }
            // Calculate consistency score
            const totalIssues = missingTables.length + extraTables.length +
                missingColumns.length + extraColumns.length +
                inconsistencies.length;
            const score = Math.max(0, 100 - (totalIssues * 10));
            return {
                isConsistent: totalIssues === 0,
                missingTables,
                extraTables,
                missingColumns,
                extraColumns,
                inconsistencies,
                score
            };
        }
        catch (error) {
            this.logger.error('Schema validation failed', { error });
            return {
                isConsistent: false,
                missingTables: [],
                extraTables: [],
                missingColumns: [],
                extraColumns: [],
                inconsistencies: [error instanceof Error ? error.message : String(error)],
                score: 0
            };
        }
        finally {
            client.release();
        }
    }
    /**
     * Generate migration SQL from schema differences
     */
    generateMigrationSQL(comparison) {
        const statements = [];
        // Add missing tables (basic structure)
        for (const tableName of comparison.missingTables) {
            statements.push(`-- Create missing table: ${tableName}`);
            statements.push(`CREATE TABLE IF NOT EXISTS ${tableName} (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`);
        }
        // Add missing columns
        for (const { table, column } of comparison.missingColumns) {
            statements.push(`-- Add missing column: ${table}.${column}`);
            statements.push(`ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS ${column} TEXT;`);
        }
        // Add comments for manual review
        if (comparison.extraTables.length > 0) {
            statements.push(`-- Extra tables found (review manually): ${comparison.extraTables.join(', ')}`);
        }
        if (comparison.extraColumns.length > 0) {
            statements.push(`-- Extra columns found (review manually):`);
            for (const { table, column } of comparison.extraColumns) {
                statements.push(`-- ${table}.${column}`);
            }
        }
        return statements.join('\n\n');
    }
    /**
     * Build schema map from database rows
     */
    buildSchemaMap(rows) {
        const schema = {};
        for (const row of rows) {
            if (!schema[row.table_name]) {
                schema[row.table_name] = {};
            }
            schema[row.table_name][row.column_name] = {
                data_type: row.data_type,
                is_nullable: row.is_nullable
            };
        }
        return schema;
    }
    /**
     * Check if data types are compatible
     */
    isDataTypeCompatible(dbType, expectedType) {
        const typeMap = {
            'string': ['character varying', 'varchar', 'text', 'char'],
            'text': ['text', 'character varying', 'varchar'],
            'integer': ['integer', 'int4', 'bigint', 'int8'],
            'number': ['numeric', 'decimal', 'real', 'double precision'],
            'boolean': ['boolean', 'bool'],
            'date': ['timestamp with time zone', 'timestamp', 'date'],
            'uuid': ['uuid']
        };
        const normalizedExpected = expectedType.toLowerCase();
        const normalizedDb = dbType.toLowerCase();
        if (normalizedExpected === normalizedDb) {
            return true;
        }
        const compatibleTypes = typeMap[normalizedExpected] || [];
        return compatibleTypes.includes(normalizedDb);
    }
    /**
     * Calculate checksum for migration content
     */
    calculateChecksum(content) {
        const crypto = require('crypto');
        return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
    }
}
exports.DatabaseMigrationManager = DatabaseMigrationManager;
//# sourceMappingURL=DatabaseMigrationManager.js.map
import { WorkflowPhase } from '../orchestration/Orchestrator';
import { AgentTask, AgentResult } from '../core/BaseAgent';
export interface ParallelTask {
    id: string;
    phase: WorkflowPhase;
    task: AgentTask;
    dependencies: WorkflowPhase[];
    priority: number;
    estimatedDuration: number;
    canRunInParallel: boolean;
}
export interface ParallelExecutionPlan {
    batches: ParallelTask[][];
    totalEstimatedTime: number;
    parallelizationOpportunities: number;
    sequentialFallback: ParallelTask[];
}
export interface ParallelExecutionResult {
    results: Map<WorkflowPhase, AgentResult>;
    executionTime: number;
    parallelEfficiency: number;
    errors: Array<{
        phase: WorkflowPhase;
        error: string;
    }>;
}
export declare class ParallelProcessingManager {
    private static instance;
    private logger;
    private maxConcurrentTasks;
    private taskQueue;
    private runningTasks;
    private completedTasks;
    private parallelizationRules;
    private constructor();
    static getInstance(): ParallelProcessingManager;
    /**
     * Create execution plan with parallel processing opportunities
     */
    createExecutionPlan(tasks: ParallelTask[]): ParallelExecutionPlan;
    /**
     * Execute tasks in parallel according to the execution plan
     */
    executeParallelPlan(plan: ParallelExecutionPlan, executor: (task: AgentTask) => Promise<AgentResult>): Promise<ParallelExecutionResult>;
    /**
     * Identify parallel processing opportunities within a single agent
     */
    identifyIntraAgentParallelism(phase: WorkflowPhase, taskData: any): string[];
    /**
     * Check if a task can be executed given completed phases
     */
    private canExecuteTask;
    /**
     * Check if a task can run in parallel with other tasks in the batch
     */
    private canRunInParallel;
    /**
     * Get dependency depth for a phase
     */
    private getDependencyDepth;
    /**
     * Calculate total estimated time for execution plan
     */
    private calculateTotalTime;
    /**
     * Create parallel task from agent task
     */
    createParallelTask(phase: WorkflowPhase, task: AgentTask, dependencies?: WorkflowPhase[], priority?: number, estimatedDuration?: number): ParallelTask;
    /**
     * Check if a phase can be parallelized
     */
    private isPhaseParallelizable;
    /**
     * Get parallel processing recommendations
     */
    getParallelizationRecommendations(tasks: ParallelTask[]): string[];
    /**
     * Set maximum concurrent tasks
     */
    setMaxConcurrentTasks(max: number): void;
    /**
     * Get current parallel processing statistics
     */
    getParallelProcessingStats(): {
        maxConcurrentTasks: number;
        runningTasks: number;
        queuedTasks: number;
        completedTasks: number;
        independentPhases: WorkflowPhase[];
        conditionalParallelRules: {
            [k: string]: WorkflowPhase[];
        };
        strictSequentialRules: {
            [k: string]: WorkflowPhase[];
        };
    };
}
//# sourceMappingURL=ParallelProcessingManager.d.ts.map
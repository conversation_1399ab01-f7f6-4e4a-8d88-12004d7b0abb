{"version": 3, "file": "ParallelProcessingManager.d.ts", "sourceRoot": "", "sources": ["../../src/infrastructure/ParallelProcessingManager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAE3D,MAAM,WAAW,YAAY;IAC3B,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,aAAa,CAAC;IACrB,IAAI,EAAE,SAAS,CAAC;IAChB,YAAY,EAAE,aAAa,EAAE,CAAC;IAC9B,QAAQ,EAAE,MAAM,CAAC;IACjB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,gBAAgB,EAAE,OAAO,CAAC;CAC3B;AAED,MAAM,WAAW,qBAAqB;IACpC,OAAO,EAAE,YAAY,EAAE,EAAE,CAAC;IAC1B,kBAAkB,EAAE,MAAM,CAAC;IAC3B,4BAA4B,EAAE,MAAM,CAAC;IACrC,kBAAkB,EAAE,YAAY,EAAE,CAAC;CACpC;AAED,MAAM,WAAW,uBAAuB;IACtC,OAAO,EAAE,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IACzC,aAAa,EAAE,MAAM,CAAC;IACtB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,MAAM,EAAE,KAAK,CAAC;QAAE,KAAK,EAAE,aAAa,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;CACxD;AAED,qBAAa,yBAAyB;IACpC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAA4B;IACnD,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,kBAAkB,CAAa;IACvC,OAAO,CAAC,SAAS,CAAsB;IACvC,OAAO,CAAC,YAAY,CAAgD;IACpE,OAAO,CAAC,cAAc,CAA8C;IAGpE,OAAO,CAAC,oBAAoB,CAqB1B;IAEF,OAAO;WAIO,WAAW,IAAI,yBAAyB;IAOtD;;OAEG;IACI,mBAAmB,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,qBAAqB;IAqExE;;OAEG;IACU,mBAAmB,CAC9B,IAAI,EAAE,qBAAqB,EAC3B,QAAQ,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK,OAAO,CAAC,WAAW,CAAC,GAClD,OAAO,CAAC,uBAAuB,CAAC;IA2EnC;;OAEG;IACI,6BAA6B,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,GAAG,MAAM,EAAE;IAsDnF;;OAEG;IACH,OAAO,CAAC,cAAc;IAmBtB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAmBxB;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAqB1B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAQ1B;;OAEG;IACI,kBAAkB,CACvB,KAAK,EAAE,aAAa,EACpB,IAAI,EAAE,SAAS,EACf,YAAY,GAAE,aAAa,EAAO,EAClC,QAAQ,GAAE,MAAU,EACpB,iBAAiB,GAAE,MAAc,GAChC,YAAY;IAYf;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAK7B;;OAEG;IACI,iCAAiC,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,EAAE;IA2BzE;;OAEG;IACI,qBAAqB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAK/C;;OAEG;IACI,0BAA0B;;;;;;;;;;;;;CAWlC"}
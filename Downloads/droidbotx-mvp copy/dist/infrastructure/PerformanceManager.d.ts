import { WorkflowPhase } from '../orchestration/Orchestrator';
export interface PerformanceMetrics {
    phase: WorkflowPhase;
    startTime: number;
    endTime?: number;
    duration?: number;
    memoryUsage: {
        heapUsed: number;
        heapTotal: number;
        external: number;
        rss: number;
    };
    cpuUsage?: NodeJS.CpuUsage;
    success: boolean;
    errorCount: number;
    cacheHits: number;
    cacheMisses: number;
}
export interface CacheEntry<T = any> {
    key: string;
    value: T;
    timestamp: number;
    ttl: number;
    accessCount: number;
    lastAccessed: number;
}
export interface PerformanceReport {
    totalDuration: number;
    averagePhaseTime: number;
    slowestPhase: {
        phase: WorkflowPhase;
        duration: number;
    };
    fastestPhase: {
        phase: WorkflowPhase;
        duration: number;
    };
    memoryPeak: number;
    cacheEfficiency: number;
    bottlenecks: string[];
    recommendations: string[];
}
export declare class PerformanceManager {
    private static instance;
    private logger;
    private metrics;
    private cache;
    private cacheMaxSize;
    private defaultTTL;
    private performanceThresholds;
    private constructor();
    static getInstance(): PerformanceManager;
    /**
     * Start performance monitoring for a phase
     */
    startPhaseMonitoring(sessionId: string, phase: WorkflowPhase): string;
    /**
     * End performance monitoring for a phase
     */
    endPhaseMonitoring(sessionId: string, phase: WorkflowPhase, success: boolean, errorCount?: number): PerformanceMetrics | null;
    /**
     * Get cached value
     */
    getFromCache<T>(key: string): T | null;
    /**
     * Set cached value
     */
    setCache<T>(key: string, value: T, ttl?: number): void;
    /**
     * Clear cache
     */
    clearCache(): void;
    /**
     * Get performance report for session
     */
    getPerformanceReport(sessionId: string): PerformanceReport | null;
    /**
     * Get memory usage
     */
    private getMemoryUsage;
    /**
     * Check performance thresholds
     */
    private checkPerformanceThresholds;
    /**
     * Evict least recently used cache entries
     */
    private evictLeastRecentlyUsed;
    /**
     * Increment cache hit counter
     */
    private incrementCacheHit;
    /**
     * Increment cache miss counter
     */
    private incrementCacheMiss;
    /**
     * Identify performance bottlenecks
     */
    private identifyBottlenecks;
    /**
     * Generate performance recommendations
     */
    private generateRecommendations;
    /**
     * Start background performance monitoring
     */
    private startPerformanceMonitoring;
    /**
     * Clean up expired cache entries
     */
    private cleanupExpiredCache;
    /**
     * Get cache statistics
     */
    getCacheStats(): {
        size: number;
        maxSize: number;
        entries: {
            key: string;
            accessCount: number;
            age: number;
            ttl: number;
        }[];
    };
    /**
     * Clear session metrics
     */
    clearSessionMetrics(sessionId: string): void;
}
//# sourceMappingURL=PerformanceManager.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceManager = void 0;
const Logger_1 = require("../core/Logger");
class PerformanceManager {
    constructor() {
        this.metrics = new Map();
        this.cache = new Map();
        this.cacheMaxSize = 1000;
        this.defaultTTL = 300000; // 5 minutes
        this.performanceThresholds = {
            phaseTimeWarning: 60000, // 1 minute
            phaseTimeCritical: 180000, // 3 minutes
            memoryWarning: 500 * 1024 * 1024, // 500MB
            memoryCritical: 1024 * 1024 * 1024, // 1GB
            cacheEfficiencyTarget: 0.7 // 70%
        };
        this.logger = Logger_1.Logger.getInstance();
        this.startPerformanceMonitoring();
    }
    static getInstance() {
        if (!PerformanceManager.instance) {
            PerformanceManager.instance = new PerformanceManager();
        }
        return PerformanceManager.instance;
    }
    /**
     * Start performance monitoring for a phase
     */
    startPhaseMonitoring(sessionId, phase) {
        const metricsId = `${sessionId}-${phase}-${Date.now()}`;
        const metrics = {
            phase,
            startTime: Date.now(),
            memoryUsage: this.getMemoryUsage(),
            cpuUsage: process.cpuUsage(),
            success: false,
            errorCount: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        if (!this.metrics.has(sessionId)) {
            this.metrics.set(sessionId, []);
        }
        this.metrics.get(sessionId).push(metrics);
        this.logger.debug('Performance monitoring started', {
            sessionId,
            phase,
            metricsId,
            memoryUsage: metrics.memoryUsage
        });
        return metricsId;
    }
    /**
     * End performance monitoring for a phase
     */
    endPhaseMonitoring(sessionId, phase, success, errorCount = 0) {
        const sessionMetrics = this.metrics.get(sessionId);
        if (!sessionMetrics)
            return null;
        // Find the most recent metrics for this phase
        const phaseMetrics = sessionMetrics
            .filter(m => m.phase === phase && !m.endTime)
            .pop();
        if (!phaseMetrics)
            return null;
        const endTime = Date.now();
        const duration = endTime - phaseMetrics.startTime;
        const finalMemoryUsage = this.getMemoryUsage();
        const finalCpuUsage = process.cpuUsage(phaseMetrics.cpuUsage);
        phaseMetrics.endTime = endTime;
        phaseMetrics.duration = duration;
        phaseMetrics.success = success;
        phaseMetrics.errorCount = errorCount;
        phaseMetrics.memoryUsage = finalMemoryUsage;
        phaseMetrics.cpuUsage = finalCpuUsage;
        // Check for performance issues
        this.checkPerformanceThresholds(phaseMetrics);
        this.logger.info('Performance monitoring completed', {
            sessionId,
            phase,
            duration,
            success,
            memoryUsage: finalMemoryUsage,
            cpuUsage: finalCpuUsage
        });
        return phaseMetrics;
    }
    /**
     * Get cached value
     */
    getFromCache(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            this.incrementCacheMiss();
            return null;
        }
        // Check TTL
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            this.incrementCacheMiss();
            return null;
        }
        // Update access statistics
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        this.incrementCacheHit();
        this.logger.debug('Cache hit', { key, accessCount: entry.accessCount });
        return entry.value;
    }
    /**
     * Set cached value
     */
    setCache(key, value, ttl) {
        // Evict old entries if cache is full
        if (this.cache.size >= this.cacheMaxSize) {
            this.evictLeastRecentlyUsed();
        }
        const entry = {
            key,
            value,
            timestamp: Date.now(),
            ttl: ttl || this.defaultTTL,
            accessCount: 0,
            lastAccessed: Date.now()
        };
        this.cache.set(key, entry);
        this.logger.debug('Cache set', { key, ttl: entry.ttl, cacheSize: this.cache.size });
    }
    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
        this.logger.info('Cache cleared');
    }
    /**
     * Get performance report for session
     */
    getPerformanceReport(sessionId) {
        const sessionMetrics = this.metrics.get(sessionId);
        if (!sessionMetrics || sessionMetrics.length === 0)
            return null;
        const completedMetrics = sessionMetrics.filter(m => m.duration !== undefined);
        if (completedMetrics.length === 0)
            return null;
        const totalDuration = completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);
        const averagePhaseTime = totalDuration / completedMetrics.length;
        const sortedByDuration = [...completedMetrics].sort((a, b) => (b.duration || 0) - (a.duration || 0));
        const slowestPhase = {
            phase: sortedByDuration[0].phase,
            duration: sortedByDuration[0].duration || 0
        };
        const fastestPhase = {
            phase: sortedByDuration[sortedByDuration.length - 1].phase,
            duration: sortedByDuration[sortedByDuration.length - 1].duration || 0
        };
        const memoryPeak = Math.max(...completedMetrics.map(m => m.memoryUsage.heapUsed));
        const totalCacheRequests = completedMetrics.reduce((sum, m) => sum + m.cacheHits + m.cacheMisses, 0);
        const totalCacheHits = completedMetrics.reduce((sum, m) => sum + m.cacheHits, 0);
        const cacheEfficiency = totalCacheRequests > 0 ? totalCacheHits / totalCacheRequests : 0;
        const bottlenecks = this.identifyBottlenecks(completedMetrics);
        const recommendations = this.generateRecommendations(completedMetrics, cacheEfficiency);
        return {
            totalDuration,
            averagePhaseTime,
            slowestPhase,
            fastestPhase,
            memoryPeak,
            cacheEfficiency,
            bottlenecks,
            recommendations
        };
    }
    /**
     * Get memory usage
     */
    getMemoryUsage() {
        return process.memoryUsage();
    }
    /**
     * Check performance thresholds
     */
    checkPerformanceThresholds(metrics) {
        const duration = metrics.duration || 0;
        const memoryUsed = metrics.memoryUsage.heapUsed;
        if (duration > this.performanceThresholds.phaseTimeCritical) {
            this.logger.warn('Critical phase duration detected', {
                phase: metrics.phase,
                duration,
                threshold: this.performanceThresholds.phaseTimeCritical
            });
        }
        else if (duration > this.performanceThresholds.phaseTimeWarning) {
            this.logger.warn('Slow phase duration detected', {
                phase: metrics.phase,
                duration,
                threshold: this.performanceThresholds.phaseTimeWarning
            });
        }
        if (memoryUsed > this.performanceThresholds.memoryCritical) {
            this.logger.warn('Critical memory usage detected', {
                phase: metrics.phase,
                memoryUsed,
                threshold: this.performanceThresholds.memoryCritical
            });
        }
        else if (memoryUsed > this.performanceThresholds.memoryWarning) {
            this.logger.warn('High memory usage detected', {
                phase: metrics.phase,
                memoryUsed,
                threshold: this.performanceThresholds.memoryWarning
            });
        }
    }
    /**
     * Evict least recently used cache entries
     */
    evictLeastRecentlyUsed() {
        const entries = Array.from(this.cache.entries());
        entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
        // Remove oldest 10% of entries
        const toRemove = Math.max(1, Math.floor(entries.length * 0.1));
        for (let i = 0; i < toRemove; i++) {
            this.cache.delete(entries[i][0]);
        }
        this.logger.debug('Cache eviction completed', {
            removed: toRemove,
            remaining: this.cache.size
        });
    }
    /**
     * Increment cache hit counter
     */
    incrementCacheHit() {
        // This would be tracked per session/phase if needed
    }
    /**
     * Increment cache miss counter
     */
    incrementCacheMiss() {
        // This would be tracked per session/phase if needed
    }
    /**
     * Identify performance bottlenecks
     */
    identifyBottlenecks(metrics) {
        const bottlenecks = [];
        // Find phases that take significantly longer than average
        const avgDuration = metrics.reduce((sum, m) => sum + (m.duration || 0), 0) / metrics.length;
        const slowPhases = metrics.filter(m => (m.duration || 0) > avgDuration * 2);
        for (const phase of slowPhases) {
            bottlenecks.push(`${phase.phase} phase is ${Math.round((phase.duration || 0) / avgDuration)}x slower than average`);
        }
        // Check for memory issues
        const highMemoryPhases = metrics.filter(m => m.memoryUsage.heapUsed > this.performanceThresholds.memoryWarning);
        if (highMemoryPhases.length > 0) {
            bottlenecks.push(`High memory usage detected in ${highMemoryPhases.length} phases`);
        }
        // Check for error patterns
        const errorPhases = metrics.filter(m => m.errorCount > 0);
        if (errorPhases.length > 0) {
            bottlenecks.push(`Errors detected in ${errorPhases.length} phases`);
        }
        return bottlenecks;
    }
    /**
     * Generate performance recommendations
     */
    generateRecommendations(metrics, cacheEfficiency) {
        const recommendations = [];
        // Cache efficiency recommendations
        if (cacheEfficiency < this.performanceThresholds.cacheEfficiencyTarget) {
            recommendations.push('Improve caching strategy - current efficiency is below target');
        }
        // Memory optimization recommendations
        const avgMemory = metrics.reduce((sum, m) => sum + m.memoryUsage.heapUsed, 0) / metrics.length;
        if (avgMemory > this.performanceThresholds.memoryWarning) {
            recommendations.push('Consider memory optimization - average usage is high');
        }
        // Phase duration recommendations
        const longPhases = metrics.filter(m => (m.duration || 0) > this.performanceThresholds.phaseTimeWarning);
        if (longPhases.length > 0) {
            recommendations.push('Optimize slow phases or consider parallel processing');
        }
        // Error handling recommendations
        const totalErrors = metrics.reduce((sum, m) => sum + m.errorCount, 0);
        if (totalErrors > 0) {
            recommendations.push('Improve error handling and retry mechanisms');
        }
        return recommendations;
    }
    /**
     * Start background performance monitoring
     */
    startPerformanceMonitoring() {
        // Monitor overall system performance every 30 seconds
        setInterval(() => {
            const memoryUsage = this.getMemoryUsage();
            const cpuUsage = process.cpuUsage();
            if (memoryUsage.heapUsed > this.performanceThresholds.memoryCritical) {
                this.logger.warn('System memory usage critical', { memoryUsage });
            }
            // Clean up expired cache entries
            this.cleanupExpiredCache();
        }, 30000);
    }
    /**
     * Clean up expired cache entries
     */
    cleanupExpiredCache() {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            this.logger.debug('Cache cleanup completed', { cleaned, remaining: this.cache.size });
        }
    }
    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            maxSize: this.cacheMaxSize,
            entries: Array.from(this.cache.values()).map(entry => ({
                key: entry.key,
                accessCount: entry.accessCount,
                age: Date.now() - entry.timestamp,
                ttl: entry.ttl
            }))
        };
    }
    /**
     * Clear session metrics
     */
    clearSessionMetrics(sessionId) {
        this.metrics.delete(sessionId);
        this.logger.debug('Session metrics cleared', { sessionId });
    }
}
exports.PerformanceManager = PerformanceManager;
//# sourceMappingURL=PerformanceManager.js.map
import { WorkflowPhase } from '../orchestration/Orchestrator';
import { AgentResult, AgentTask } from '../core/BaseAgent';
export interface RecoveryStep {
    id: string;
    name: string;
    description: string;
    action: (context: RecoveryContext) => Promise<RecoveryStepResult>;
    timeout: number;
    retryCount: number;
    priority: number;
}
export interface RecoveryContext {
    sessionId: string;
    phase: WorkflowPhase;
    originalTask: AgentTask;
    failedResult: AgentResult;
    previousAttempts: RecoveryAttempt[];
    metadata: any;
}
export interface RecoveryStepResult {
    success: boolean;
    data?: any;
    error?: string;
    shouldContinue: boolean;
    confidence: number;
    timeSpent: number;
}
export interface RecoveryAttempt {
    stepId: string;
    timestamp: number;
    result: RecoveryStepResult;
    phase: WorkflowPhase;
}
export interface RecoveryWorkflowResult {
    success: boolean;
    recoveredResult?: AgentResult;
    appliedSteps: string[];
    totalTime: number;
    confidence: number;
    recommendedActions: string[];
    requiresManualIntervention: boolean;
}
export declare class ErrorRecoveryWorkflow {
    private static instance;
    private logger;
    private selfHealingManager;
    private recoverySteps;
    private activeRecoveries;
    private maxRecoveryTime;
    private maxRecoveryAttempts;
    private constructor();
    static getInstance(): ErrorRecoveryWorkflow;
    /**
     * Initialize recovery steps
     */
    private initializeRecoverySteps;
    /**
     * Execute error recovery workflow
     */
    executeRecoveryWorkflow(sessionId: string, phase: WorkflowPhase, originalTask: AgentTask, failedResult: AgentResult): Promise<RecoveryWorkflowResult>;
    /**
     * Execute recovery steps in priority order
     */
    private executeRecoverySteps;
    /**
     * Get applicable recovery steps for a phase
     */
    private getApplicableSteps;
    /**
     * Generate recovery recommendations
     */
    private generateRecoveryRecommendations;
    /**
     * Perform self-healing using the SelfHealingManager
     */
    private performSelfHealing;
    /**
     * Adjust task parameters based on error analysis
     */
    private adjustParameters;
    /**
     * Retry with exponential backoff
     */
    private retryWithBackoff;
    /**
     * Apply fallback strategy
     */
    private applyFallbackStrategy;
    /**
     * Enrich context with additional information
     */
    private enrichContext;
    /**
     * Attempt partial recovery
     */
    private attemptPartialRecovery;
    /**
     * Perform graceful degradation
     */
    private performGracefulDegradation;
    /**
     * Get error recovery statistics
     */
    getErrorRecoveryStats(): any;
    /**
     * Set maximum recovery time
     */
    setMaxRecoveryTime(timeMs: number): void;
    /**
     * Set maximum recovery attempts
     */
    setMaxRecoveryAttempts(attempts: number): void;
}
//# sourceMappingURL=ErrorRecoveryWorkflow.d.ts.map
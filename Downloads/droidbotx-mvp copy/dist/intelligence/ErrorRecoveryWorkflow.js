"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorRecoveryWorkflow = void 0;
const Logger_1 = require("../core/Logger");
const Orchestrator_1 = require("../orchestration/Orchestrator");
const SelfHealingManager_1 = require("./SelfHealingManager");
class ErrorRecoveryWorkflow {
    constructor() {
        this.recoverySteps = new Map();
        this.activeRecoveries = new Map();
        this.maxRecoveryTime = 300000; // 5 minutes
        this.maxRecoveryAttempts = 5;
        this.logger = Logger_1.Logger.getInstance();
        this.selfHealingManager = SelfHealingManager_1.SelfHealingManager.getInstance();
        this.initializeRecoverySteps();
    }
    static getInstance() {
        if (!ErrorRecoveryWorkflow.instance) {
            ErrorRecoveryWorkflow.instance = new ErrorRecoveryWorkflow();
        }
        return ErrorRecoveryWorkflow.instance;
    }
    /**
     * Initialize recovery steps
     */
    initializeRecoverySteps() {
        // Step 1: Self-Healing Attempt
        this.recoverySteps.set('self-healing', {
            id: 'self-healing',
            name: 'Self-Healing Attempt',
            description: 'Attempt automatic error fixing using learned patterns',
            action: this.performSelfHealing.bind(this),
            timeout: 30000,
            retryCount: 1,
            priority: 10
        });
        // Step 2: Parameter Adjustment
        this.recoverySteps.set('parameter-adjustment', {
            id: 'parameter-adjustment',
            name: 'Parameter Adjustment',
            description: 'Adjust task parameters based on error analysis',
            action: this.adjustParameters.bind(this),
            timeout: 10000,
            retryCount: 2,
            priority: 9
        });
        // Step 3: Retry with Backoff
        this.recoverySteps.set('retry-backoff', {
            id: 'retry-backoff',
            name: 'Retry with Exponential Backoff',
            description: 'Retry the original task with exponential backoff',
            action: this.retryWithBackoff.bind(this),
            timeout: 60000,
            retryCount: 3,
            priority: 8
        });
        // Step 4: Fallback Strategy
        this.recoverySteps.set('fallback-strategy', {
            id: 'fallback-strategy',
            name: 'Fallback Strategy',
            description: 'Use alternative approach or simplified requirements',
            action: this.applyFallbackStrategy.bind(this),
            timeout: 45000,
            retryCount: 2,
            priority: 7
        });
        // Step 5: Context Enrichment
        this.recoverySteps.set('context-enrichment', {
            id: 'context-enrichment',
            name: 'Context Enrichment',
            description: 'Add additional context or dependencies',
            action: this.enrichContext.bind(this),
            timeout: 20000,
            retryCount: 1,
            priority: 6
        });
        // Step 6: Partial Recovery
        this.recoverySteps.set('partial-recovery', {
            id: 'partial-recovery',
            name: 'Partial Recovery',
            description: 'Attempt to recover partial functionality',
            action: this.attemptPartialRecovery.bind(this),
            timeout: 30000,
            retryCount: 1,
            priority: 5
        });
        // Step 7: Graceful Degradation
        this.recoverySteps.set('graceful-degradation', {
            id: 'graceful-degradation',
            name: 'Graceful Degradation',
            description: 'Provide minimal viable functionality',
            action: this.performGracefulDegradation.bind(this),
            timeout: 15000,
            retryCount: 1,
            priority: 4
        });
        this.logger.info('Error recovery steps initialized', {
            stepsCount: this.recoverySteps.size
        });
    }
    /**
     * Execute error recovery workflow
     */
    async executeRecoveryWorkflow(sessionId, phase, originalTask, failedResult) {
        const startTime = Date.now();
        const recoveryId = `${sessionId}-${phase}-${Date.now()}`;
        this.logger.info('Starting error recovery workflow', {
            recoveryId,
            sessionId,
            phase
        });
        const context = {
            sessionId,
            phase,
            originalTask,
            failedResult,
            previousAttempts: [],
            metadata: {
                startTime,
                recoveryId
            }
        };
        this.activeRecoveries.set(recoveryId, context);
        try {
            const result = await this.executeRecoverySteps(context);
            this.logger.info('Error recovery workflow completed', {
                recoveryId,
                success: result.success,
                appliedSteps: result.appliedSteps.length,
                totalTime: result.totalTime
            });
            return result;
        }
        catch (error) {
            this.logger.error('Error recovery workflow failed', {
                recoveryId,
                error: error instanceof Error ? error.message : String(error)
            });
            return {
                success: false,
                appliedSteps: [],
                totalTime: Date.now() - startTime,
                confidence: 0,
                recommendedActions: ['Manual intervention required'],
                requiresManualIntervention: true
            };
        }
        finally {
            this.activeRecoveries.delete(recoveryId);
        }
    }
    /**
     * Execute recovery steps in priority order
     */
    async executeRecoverySteps(context) {
        const appliedSteps = [];
        const startTime = Date.now();
        let bestResult;
        let bestConfidence = 0;
        let totalTime = 0;
        // Get applicable recovery steps for the phase
        const applicableSteps = this.getApplicableSteps(context.phase);
        // Sort by priority
        applicableSteps.sort((a, b) => b.priority - a.priority);
        for (const step of applicableSteps) {
            if (Date.now() - startTime > this.maxRecoveryTime) {
                this.logger.warn('Recovery workflow timeout reached', {
                    sessionId: context.sessionId,
                    phase: context.phase
                });
                break;
            }
            if (context.previousAttempts.length >= this.maxRecoveryAttempts) {
                this.logger.warn('Maximum recovery attempts reached', {
                    sessionId: context.sessionId,
                    phase: context.phase
                });
                break;
            }
            try {
                this.logger.info(`Executing recovery step: ${step.name}`, {
                    sessionId: context.sessionId,
                    phase: context.phase
                });
                const stepStartTime = Date.now();
                const stepResult = await Promise.race([
                    step.action(context),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Step timeout')), step.timeout))
                ]);
                const stepTime = Date.now() - stepStartTime;
                totalTime += stepTime;
                // Record the attempt
                const attempt = {
                    stepId: step.id,
                    timestamp: Date.now(),
                    result: stepResult,
                    phase: context.phase
                };
                context.previousAttempts.push(attempt);
                if (stepResult.success) {
                    appliedSteps.push(step.name);
                    if (stepResult.confidence > bestConfidence) {
                        bestConfidence = stepResult.confidence;
                        if (stepResult.data) {
                            bestResult = stepResult.data;
                        }
                    }
                    // If we have a high-confidence result, we can stop
                    if (stepResult.confidence > 0.8) {
                        break;
                    }
                }
                if (!stepResult.shouldContinue) {
                    break;
                }
            }
            catch (error) {
                this.logger.warn(`Recovery step failed: ${step.name}`, {
                    sessionId: context.sessionId,
                    phase: context.phase,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        const success = bestResult !== undefined && bestConfidence > 0.5;
        const recommendedActions = this.generateRecoveryRecommendations(context, appliedSteps, success);
        return {
            success,
            recoveredResult: bestResult,
            appliedSteps,
            totalTime,
            confidence: bestConfidence,
            recommendedActions,
            requiresManualIntervention: !success || bestConfidence < 0.7
        };
    }
    /**
     * Get applicable recovery steps for a phase
     */
    getApplicableSteps(phase) {
        // All steps are generally applicable, but some may be more relevant for certain phases
        return Array.from(this.recoverySteps.values());
    }
    /**
     * Generate recovery recommendations
     */
    generateRecoveryRecommendations(context, appliedSteps, success) {
        const recommendations = [];
        if (!success) {
            recommendations.push('Automatic recovery failed - manual intervention required');
            recommendations.push('Review error patterns and consider updating fix strategies');
        }
        if (appliedSteps.length === 0) {
            recommendations.push('No recovery steps were applicable - review error classification');
        }
        if (context.previousAttempts.length >= this.maxRecoveryAttempts) {
            recommendations.push('Maximum recovery attempts reached - consider escalation');
        }
        // Phase-specific recommendations
        switch (context.phase) {
            case Orchestrator_1.WorkflowPhase.PLAN:
                recommendations.push('Review requirements clarity and technical constraints');
                break;
            case Orchestrator_1.WorkflowPhase.GENERATE:
                recommendations.push('Check code generation templates and compilation environment');
                break;
            case Orchestrator_1.WorkflowPhase.TESTING:
                recommendations.push('Verify test environment and dependencies');
                break;
            case Orchestrator_1.WorkflowPhase.DATABASE:
                recommendations.push('Check database connectivity and schema consistency');
                break;
        }
        return recommendations;
    }
    // Recovery Step Implementations
    /**
     * Perform self-healing using the SelfHealingManager
     */
    async performSelfHealing(context) {
        const startTime = Date.now();
        try {
            const healingResult = await this.selfHealingManager.attemptSelfHealing(context.failedResult, context.phase, { sessionId: context.sessionId, originalTask: context.originalTask });
            return {
                success: healingResult.success,
                data: healingResult.errorResolved ? context.failedResult : undefined,
                shouldContinue: !healingResult.errorResolved,
                confidence: healingResult.confidence,
                timeSpent: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                shouldContinue: true,
                confidence: 0,
                timeSpent: Date.now() - startTime
            };
        }
    }
    /**
     * Adjust task parameters based on error analysis
     */
    async adjustParameters(context) {
        const startTime = Date.now();
        try {
            // Create adjusted parameters based on the error
            const adjustedTask = { ...context.originalTask };
            // Add error-specific adjustments
            if (context.failedResult.metadata?.error?.includes('timeout')) {
                adjustedTask.parameters = {
                    ...adjustedTask.parameters,
                    timeout: (adjustedTask.parameters?.timeout || 30000) * 2
                };
            }
            if (context.failedResult.metadata?.error?.includes('memory')) {
                adjustedTask.parameters = {
                    ...adjustedTask.parameters,
                    maxMemory: '2GB',
                    optimizeMemory: true
                };
            }
            return {
                success: true,
                data: adjustedTask,
                shouldContinue: true,
                confidence: 0.6,
                timeSpent: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                shouldContinue: true,
                confidence: 0,
                timeSpent: Date.now() - startTime
            };
        }
    }
    /**
     * Retry with exponential backoff
     */
    async retryWithBackoff(context) {
        const startTime = Date.now();
        const attemptCount = context.previousAttempts.filter(a => a.stepId === 'retry-backoff').length;
        const backoffDelay = Math.min(1000 * Math.pow(2, attemptCount), 10000);
        try {
            // Wait for backoff delay
            await new Promise(resolve => setTimeout(resolve, backoffDelay));
            // For now, just return a simulated retry result
            // In a real implementation, this would re-execute the original task
            return {
                success: attemptCount < 2, // Simulate success after a few retries
                data: attemptCount < 2 ? { ...context.failedResult, success: true } : undefined,
                shouldContinue: attemptCount >= 2,
                confidence: attemptCount < 2 ? 0.7 : 0.3,
                timeSpent: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                shouldContinue: true,
                confidence: 0,
                timeSpent: Date.now() - startTime
            };
        }
    }
    /**
     * Apply fallback strategy
     */
    async applyFallbackStrategy(context) {
        const startTime = Date.now();
        try {
            // Create a fallback result with reduced functionality
            const fallbackResult = {
                success: true,
                data: {
                    fallback: true,
                    originalPhase: context.phase,
                    reducedFunctionality: true,
                    message: 'Fallback strategy applied - limited functionality available'
                },
                metadata: {
                    ...context.failedResult.metadata,
                    fallbackApplied: true,
                    originalError: context.failedResult.metadata?.error
                }
            };
            return {
                success: true,
                data: fallbackResult,
                shouldContinue: false,
                confidence: 0.5,
                timeSpent: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                shouldContinue: true,
                confidence: 0,
                timeSpent: Date.now() - startTime
            };
        }
    }
    /**
     * Enrich context with additional information
     */
    async enrichContext(context) {
        const startTime = Date.now();
        try {
            // Add additional context that might help with recovery
            const enrichedTask = {
                ...context.originalTask,
                parameters: {
                    ...context.originalTask.parameters,
                    enrichedContext: true,
                    previousErrors: context.previousAttempts.map(a => a.result.error).filter(Boolean),
                    recoveryAttempt: true
                }
            };
            return {
                success: true,
                data: enrichedTask,
                shouldContinue: true,
                confidence: 0.4,
                timeSpent: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                shouldContinue: true,
                confidence: 0,
                timeSpent: Date.now() - startTime
            };
        }
    }
    /**
     * Attempt partial recovery
     */
    async attemptPartialRecovery(context) {
        const startTime = Date.now();
        try {
            // Extract any partial success from the failed result
            const partialData = context.failedResult.data || {};
            const partialResult = {
                success: true,
                data: {
                    ...partialData,
                    partial: true,
                    completeness: 0.6,
                    message: 'Partial recovery successful - some functionality available'
                },
                metadata: {
                    ...context.failedResult.metadata,
                    partialRecovery: true,
                    originalError: context.failedResult.metadata?.error
                }
            };
            return {
                success: true,
                data: partialResult,
                shouldContinue: false,
                confidence: 0.6,
                timeSpent: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                shouldContinue: true,
                confidence: 0,
                timeSpent: Date.now() - startTime
            };
        }
    }
    /**
     * Perform graceful degradation
     */
    async performGracefulDegradation(context) {
        const startTime = Date.now();
        try {
            // Provide minimal viable functionality
            const degradedResult = {
                success: true,
                data: {
                    degraded: true,
                    phase: context.phase,
                    minimalFunctionality: true,
                    message: 'Graceful degradation applied - minimal functionality available'
                },
                metadata: {
                    gracefulDegradation: true,
                    originalError: context.failedResult.metadata?.error,
                    degradationLevel: 'minimal'
                }
            };
            return {
                success: true,
                data: degradedResult,
                shouldContinue: false,
                confidence: 0.3,
                timeSpent: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                shouldContinue: false,
                confidence: 0,
                timeSpent: Date.now() - startTime
            };
        }
    }
    /**
     * Get error recovery statistics
     */
    getErrorRecoveryStats() {
        return {
            totalRecoverySteps: this.recoverySteps.size,
            activeRecoveries: this.activeRecoveries.size,
            maxRecoveryTime: this.maxRecoveryTime,
            maxRecoveryAttempts: this.maxRecoveryAttempts,
            recoverySteps: Array.from(this.recoverySteps.values()).map(step => ({
                id: step.id,
                name: step.name,
                priority: step.priority,
                timeout: step.timeout,
                retryCount: step.retryCount
            }))
        };
    }
    /**
     * Set maximum recovery time
     */
    setMaxRecoveryTime(timeMs) {
        this.maxRecoveryTime = Math.max(30000, timeMs); // Minimum 30 seconds
        this.logger.info('Max recovery time updated', { maxRecoveryTime: this.maxRecoveryTime });
    }
    /**
     * Set maximum recovery attempts
     */
    setMaxRecoveryAttempts(attempts) {
        this.maxRecoveryAttempts = Math.max(1, attempts);
        this.logger.info('Max recovery attempts updated', { maxRecoveryAttempts: this.maxRecoveryAttempts });
    }
}
exports.ErrorRecoveryWorkflow = ErrorRecoveryWorkflow;
//# sourceMappingURL=ErrorRecoveryWorkflow.js.map
import { WorkflowPhase } from '../orchestration/Orchestrator';
import { AgentResult } from '../core/BaseAgent';
export interface ErrorPattern {
    id: string;
    pattern: string;
    phase: WorkflowPhase;
    frequency: number;
    successfulFixes: string[];
    failedFixes: string[];
    confidence: number;
    lastSeen: number;
}
export interface FixStrategy {
    id: string;
    name: string;
    description: string;
    applicablePhases: WorkflowPhase[];
    errorPatterns: string[];
    fixFunction: (error: any, context: any) => Promise<any>;
    successRate: number;
    averageTime: number;
    priority: number;
}
export interface SelfHealingResult {
    success: boolean;
    appliedFixes: string[];
    errorResolved: boolean;
    newErrors: string[];
    confidence: number;
    timeSpent: number;
    recommendedActions: string[];
}
export interface AdaptiveLearningData {
    errorPattern: string;
    fixStrategy: string;
    success: boolean;
    context: any;
    timestamp: number;
    phase: WorkflowPhase;
}
export declare class SelfHealingManager {
    private static instance;
    private logger;
    private errorPatterns;
    private fixStrategies;
    private learningData;
    private maxLearningDataSize;
    private autoFixEnabled;
    private maxFixAttempts;
    private fixTimeoutMs;
    private constructor();
    static getInstance(): SelfHealingManager;
    /**
     * Initialize built-in fix strategies
     */
    private initializeFixStrategies;
    /**
     * Attempt to automatically fix errors in agent result
     */
    attemptSelfHealing(result: AgentResult, phase: WorkflowPhase, context?: any): Promise<SelfHealingResult>;
    /**
     * Extract errors from agent result
     */
    private extractErrors;
    /**
     * Find or create error pattern
     */
    private findOrCreateErrorPattern;
    /**
     * Extract error pattern from error message
     */
    private extractErrorPattern;
    /**
     * Get applicable fix strategies for error and phase
     */
    private getApplicableFixStrategies;
    /**
     * Update strategy success rate based on outcome
     */
    private updateStrategySuccessRate;
    /**
     * Record learning data for adaptive improvement
     */
    private recordLearningData;
    /**
     * Generate recommended actions based on errors and applied fixes
     */
    private generateRecommendedActions;
    /**
     * Start adaptive learning process
     */
    private startAdaptiveLearning;
    /**
     * Perform adaptive learning to improve fix strategies
     */
    private performAdaptiveLearning;
    /**
     * Identify new fix opportunities from failure patterns
     */
    private identifyNewFixOpportunities;
    /**
     * Fix JSON parsing errors
     */
    private fixJSONParsingError;
    /**
     * Fix compilation errors
     */
    private fixCompilationError;
    /**
     * Fix database connection errors
     */
    private fixDatabaseConnectionError;
    /**
     * Fix API integration errors
     */
    private fixAPIIntegrationError;
    /**
     * Fix test failure errors
     */
    private fixTestFailureError;
    /**
     * Fix security configuration errors
     */
    private fixSecurityConfigError;
    /**
     * Get self-healing statistics
     */
    getSelfHealingStats(): any;
    /**
     * Enable or disable auto-fixing
     */
    setAutoFixEnabled(enabled: boolean): void;
    /**
     * Clear learning data
     */
    clearLearningData(): void;
    /**
     * Get error patterns for analysis
     */
    getErrorPatterns(): ErrorPattern[];
    /**
     * Get fix strategies for analysis
     */
    getFixStrategies(): FixStrategy[];
    /**
     * Get learning data for analysis
     */
    getLearningData(): AdaptiveLearningData[];
}
//# sourceMappingURL=SelfHealingManager.d.ts.map
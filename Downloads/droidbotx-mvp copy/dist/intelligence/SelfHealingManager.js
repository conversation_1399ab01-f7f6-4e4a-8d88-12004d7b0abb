"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SelfHealingManager = void 0;
const Logger_1 = require("../core/Logger");
const Orchestrator_1 = require("../orchestration/Orchestrator");
class SelfHealingManager {
    constructor() {
        this.errorPatterns = new Map();
        this.fixStrategies = new Map();
        this.learningData = [];
        this.maxLearningDataSize = 1000;
        this.autoFixEnabled = true;
        this.maxFixAttempts = 3;
        this.fixTimeoutMs = 30000; // 30 seconds
        this.logger = Logger_1.Logger.getInstance();
        this.initializeFixStrategies();
        this.startAdaptiveLearning();
    }
    static getInstance() {
        if (!SelfHealingManager.instance) {
            SelfHealingManager.instance = new SelfHealingManager();
        }
        return SelfHealingManager.instance;
    }
    /**
     * Initialize built-in fix strategies
     */
    initializeFixStrategies() {
        // JSON Parsing Fix Strategy
        this.fixStrategies.set('json-parsing-fix', {
            id: 'json-parsing-fix',
            name: 'JSON Parsing Error Fix',
            description: 'Attempts to fix malformed JSON responses',
            applicablePhases: [Orchestrator_1.WorkflowPhase.PLAN, Orchestrator_1.WorkflowPhase.BUSINESS_LOGIC, Orchestrator_1.WorkflowPhase.GENERATE],
            errorPatterns: ['JSON.parse', 'Unexpected token', 'malformed JSON'],
            fixFunction: this.fixJSONParsingError.bind(this),
            successRate: 0.85,
            averageTime: 2000,
            priority: 9
        });
        // Compilation Error Fix Strategy
        this.fixStrategies.set('compilation-fix', {
            id: 'compilation-fix',
            name: 'Compilation Error Fix',
            description: 'Fixes common TypeScript/JavaScript compilation errors',
            applicablePhases: [Orchestrator_1.WorkflowPhase.GENERATE, Orchestrator_1.WorkflowPhase.TESTING],
            errorPatterns: ['Cannot find module', 'Type error', 'Syntax error', 'Import error'],
            fixFunction: this.fixCompilationError.bind(this),
            successRate: 0.78,
            averageTime: 5000,
            priority: 8
        });
        // Database Connection Fix Strategy
        this.fixStrategies.set('database-connection-fix', {
            id: 'database-connection-fix',
            name: 'Database Connection Fix',
            description: 'Resolves database connection and configuration issues',
            applicablePhases: [Orchestrator_1.WorkflowPhase.DATABASE, Orchestrator_1.WorkflowPhase.TESTING],
            errorPatterns: ['connection refused', 'database error', 'ECONNREFUSED', 'authentication failed'],
            fixFunction: this.fixDatabaseConnectionError.bind(this),
            successRate: 0.72,
            averageTime: 8000,
            priority: 7
        });
        // API Integration Fix Strategy
        this.fixStrategies.set('api-integration-fix', {
            id: 'api-integration-fix',
            name: 'API Integration Fix',
            description: 'Fixes API endpoint and integration issues',
            applicablePhases: [Orchestrator_1.WorkflowPhase.GENERATE, Orchestrator_1.WorkflowPhase.TESTING],
            errorPatterns: ['404 Not Found', '500 Internal Server Error', 'API endpoint', 'fetch failed'],
            fixFunction: this.fixAPIIntegrationError.bind(this),
            successRate: 0.68,
            averageTime: 6000,
            priority: 6
        });
        // Test Failure Fix Strategy
        this.fixStrategies.set('test-failure-fix', {
            id: 'test-failure-fix',
            name: 'Test Failure Fix',
            description: 'Automatically fixes common test failures',
            applicablePhases: [Orchestrator_1.WorkflowPhase.TESTING],
            errorPatterns: ['test failed', 'assertion error', 'expect', 'jest'],
            fixFunction: this.fixTestFailureError.bind(this),
            successRate: 0.75,
            averageTime: 4000,
            priority: 5
        });
        // Security Configuration Fix Strategy
        this.fixStrategies.set('security-config-fix', {
            id: 'security-config-fix',
            name: 'Security Configuration Fix',
            description: 'Fixes security configuration and authentication issues',
            applicablePhases: [Orchestrator_1.WorkflowPhase.TESTING],
            errorPatterns: ['authentication', 'authorization', 'JWT', 'security'],
            fixFunction: this.fixSecurityConfigError.bind(this),
            successRate: 0.70,
            averageTime: 7000,
            priority: 8
        });
        this.logger.info('Self-healing fix strategies initialized', {
            strategiesCount: this.fixStrategies.size
        });
    }
    /**
     * Attempt to automatically fix errors in agent result
     */
    async attemptSelfHealing(result, phase, context = {}) {
        if (!this.autoFixEnabled || result.success) {
            return {
                success: true,
                appliedFixes: [],
                errorResolved: false,
                newErrors: [],
                confidence: 1.0,
                timeSpent: 0,
                recommendedActions: []
            };
        }
        const startTime = Date.now();
        const appliedFixes = [];
        const newErrors = [];
        let errorResolved = false;
        let confidence = 0;
        this.logger.info('Starting self-healing process', { phase, hasErrors: !result.success });
        try {
            // Extract errors from result
            const errors = this.extractErrors(result);
            for (const error of errors) {
                // Find matching error patterns
                const errorPattern = this.findOrCreateErrorPattern(error, phase);
                // Get applicable fix strategies
                const strategies = this.getApplicableFixStrategies(error, phase);
                // Sort strategies by success rate and priority
                strategies.sort((a, b) => (b.successRate * b.priority) - (a.successRate * a.priority));
                // Attempt fixes
                for (const strategy of strategies.slice(0, this.maxFixAttempts)) {
                    try {
                        this.logger.info(`Attempting fix strategy: ${strategy.name}`, {
                            phase,
                            errorPattern: errorPattern.pattern
                        });
                        const fixResult = await Promise.race([
                            strategy.fixFunction(error, { ...context, phase, result }),
                            new Promise((_, reject) => setTimeout(() => reject(new Error('Fix timeout')), this.fixTimeoutMs))
                        ]);
                        if (fixResult && fixResult.success) {
                            appliedFixes.push(strategy.name);
                            errorResolved = true;
                            confidence = Math.max(confidence, strategy.successRate);
                            // Update strategy success rate
                            this.updateStrategySuccessRate(strategy.id, true);
                            // Record learning data
                            this.recordLearningData(errorPattern.pattern, strategy.id, true, context, phase);
                            this.logger.info(`Fix strategy successful: ${strategy.name}`, { phase });
                            break; // Move to next error
                        }
                        else {
                            this.updateStrategySuccessRate(strategy.id, false);
                            this.recordLearningData(errorPattern.pattern, strategy.id, false, context, phase);
                        }
                    }
                    catch (fixError) {
                        this.logger.warn(`Fix strategy failed: ${strategy.name}`, {
                            phase,
                            error: fixError instanceof Error ? fixError.message : String(fixError)
                        });
                        newErrors.push(`Fix strategy '${strategy.name}' failed: ${fixError}`);
                        this.updateStrategySuccessRate(strategy.id, false);
                        this.recordLearningData(errorPattern.pattern, strategy.id, false, context, phase);
                    }
                }
                // Update error pattern frequency
                errorPattern.frequency++;
                errorPattern.lastSeen = Date.now();
            }
            const timeSpent = Date.now() - startTime;
            const recommendedActions = this.generateRecommendedActions(errors, phase, appliedFixes);
            const healingResult = {
                success: appliedFixes.length > 0,
                appliedFixes,
                errorResolved,
                newErrors,
                confidence,
                timeSpent,
                recommendedActions
            };
            this.logger.info('Self-healing process completed', {
                phase,
                success: healingResult.success,
                appliedFixes: appliedFixes.length,
                timeSpent,
                confidence
            });
            return healingResult;
        }
        catch (error) {
            this.logger.error('Self-healing process failed', {
                phase,
                error: error instanceof Error ? error.message : String(error)
            });
            return {
                success: false,
                appliedFixes,
                errorResolved: false,
                newErrors: [error instanceof Error ? error.message : String(error)],
                confidence: 0,
                timeSpent: Date.now() - startTime,
                recommendedActions: ['Manual intervention required']
            };
        }
    }
    /**
     * Extract errors from agent result
     */
    extractErrors(result) {
        const errors = [];
        if (!result.success) {
            if (result.metadata?.error) {
                errors.push(result.metadata.error);
            }
            if (result.metadata?.errors && Array.isArray(result.metadata.errors)) {
                errors.push(...result.metadata.errors);
            }
            if (result.metadata?.compilationErrors) {
                errors.push(...result.metadata.compilationErrors);
            }
            if (result.metadata?.testFailures) {
                errors.push(...result.metadata.testFailures);
            }
            // If no specific errors found, use generic failure message
            if (errors.length === 0) {
                errors.push('Agent execution failed without specific error details');
            }
        }
        return errors;
    }
    /**
     * Find or create error pattern
     */
    findOrCreateErrorPattern(error, phase) {
        // Try to find existing pattern
        for (const pattern of this.errorPatterns.values()) {
            if (error.includes(pattern.pattern) && pattern.phase === phase) {
                return pattern;
            }
        }
        // Create new pattern
        const patternId = `${phase}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const newPattern = {
            id: patternId,
            pattern: this.extractErrorPattern(error),
            phase,
            frequency: 1,
            successfulFixes: [],
            failedFixes: [],
            confidence: 0.5,
            lastSeen: Date.now()
        };
        this.errorPatterns.set(patternId, newPattern);
        return newPattern;
    }
    /**
     * Extract error pattern from error message
     */
    extractErrorPattern(error) {
        // Remove specific details and keep the general pattern
        return error
            .replace(/\d+/g, 'NUMBER')
            .replace(/['"]/g, '')
            .replace(/\s+/g, ' ')
            .trim()
            .substring(0, 100); // Limit pattern length
    }
    /**
     * Get applicable fix strategies for error and phase
     */
    getApplicableFixStrategies(error, phase) {
        const applicable = [];
        for (const strategy of this.fixStrategies.values()) {
            if (strategy.applicablePhases.includes(phase)) {
                // Check if error matches any of the strategy's patterns
                const matches = strategy.errorPatterns.some(pattern => error.toLowerCase().includes(pattern.toLowerCase()));
                if (matches) {
                    applicable.push(strategy);
                }
            }
        }
        return applicable;
    }
    /**
     * Update strategy success rate based on outcome
     */
    updateStrategySuccessRate(strategyId, success) {
        const strategy = this.fixStrategies.get(strategyId);
        if (strategy) {
            // Use exponential moving average to update success rate
            const alpha = 0.1; // Learning rate
            strategy.successRate = success
                ? strategy.successRate + alpha * (1 - strategy.successRate)
                : strategy.successRate * (1 - alpha);
        }
    }
    /**
     * Record learning data for adaptive improvement
     */
    recordLearningData(errorPattern, fixStrategy, success, context, phase) {
        const learningEntry = {
            errorPattern,
            fixStrategy,
            success,
            context: { phase, ...context },
            timestamp: Date.now(),
            phase
        };
        this.learningData.push(learningEntry);
        // Maintain maximum size
        if (this.learningData.length > this.maxLearningDataSize) {
            this.learningData.shift();
        }
    }
    /**
     * Generate recommended actions based on errors and applied fixes
     */
    generateRecommendedActions(errors, phase, appliedFixes) {
        const recommendations = [];
        if (appliedFixes.length === 0) {
            recommendations.push('No automatic fixes were applicable');
            recommendations.push('Consider manual review of error patterns');
        }
        if (errors.length > 3) {
            recommendations.push('Multiple errors detected - consider systematic review');
        }
        // Phase-specific recommendations
        switch (phase) {
            case Orchestrator_1.WorkflowPhase.PLAN:
                recommendations.push('Review requirements completeness and technical feasibility');
                break;
            case Orchestrator_1.WorkflowPhase.GENERATE:
                recommendations.push('Check code generation templates and compilation settings');
                break;
            case Orchestrator_1.WorkflowPhase.TESTING:
                recommendations.push('Review test configurations and mock data');
                break;
            case Orchestrator_1.WorkflowPhase.DATABASE:
                recommendations.push('Verify database connection and schema consistency');
                break;
            case Orchestrator_1.WorkflowPhase.TESTING:
                recommendations.push('Check authentication and authorization configurations');
                break;
        }
        return recommendations;
    }
    /**
     * Start adaptive learning process
     */
    startAdaptiveLearning() {
        // Run adaptive learning every 5 minutes
        setInterval(() => {
            this.performAdaptiveLearning();
        }, 300000);
    }
    /**
     * Perform adaptive learning to improve fix strategies
     */
    performAdaptiveLearning() {
        if (this.learningData.length < 10)
            return; // Need minimum data
        this.logger.info('Performing adaptive learning', {
            dataPoints: this.learningData.length
        });
        // Analyze success patterns
        const successPatterns = this.learningData.filter(d => d.success);
        const failurePatterns = this.learningData.filter(d => !d.success);
        // Update error pattern confidence based on fix success rates
        for (const pattern of this.errorPatterns.values()) {
            const relatedData = this.learningData.filter(d => d.errorPattern === pattern.pattern);
            if (relatedData.length > 0) {
                const successRate = relatedData.filter(d => d.success).length / relatedData.length;
                pattern.confidence = successRate;
            }
        }
        // Identify new fix opportunities
        this.identifyNewFixOpportunities(failurePatterns);
        this.logger.info('Adaptive learning completed', {
            successPatterns: successPatterns.length,
            failurePatterns: failurePatterns.length,
            errorPatterns: this.errorPatterns.size
        });
    }
    /**
     * Identify new fix opportunities from failure patterns
     */
    identifyNewFixOpportunities(failurePatterns) {
        // Group failures by error pattern
        const failureGroups = new Map();
        for (const failure of failurePatterns) {
            if (!failureGroups.has(failure.errorPattern)) {
                failureGroups.set(failure.errorPattern, []);
            }
            failureGroups.get(failure.errorPattern).push(failure);
        }
        // Look for patterns that occur frequently but have no successful fixes
        for (const [pattern, failures] of failureGroups.entries()) {
            if (failures.length >= 3) { // Threshold for creating new fix strategy
                this.logger.info('Identified potential new fix opportunity', {
                    pattern,
                    failures: failures.length
                });
                // This could trigger creation of new fix strategies
                // For now, just log the opportunity
            }
        }
    }
    // Fix Strategy Implementations
    /**
     * Fix JSON parsing errors
     */
    async fixJSONParsingError(error, context) {
        // Common JSON parsing fixes
        const fixes = [
            // Remove trailing commas
            (text) => text.replace(/,(\s*[}\]])/g, '$1'),
            // Fix single quotes to double quotes
            (text) => text.replace(/'/g, '"'),
            // Remove comments
            (text) => text.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, ''),
            // Fix unquoted keys
            (text) => text.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":')
        ];
        // Try to extract and fix JSON from error context
        if (context.result?.metadata?.rawResponse) {
            let jsonText = context.result.metadata.rawResponse;
            for (const fix of fixes) {
                try {
                    jsonText = fix(jsonText);
                    JSON.parse(jsonText); // Test if it's valid now
                    return { success: true, fixedJSON: jsonText };
                }
                catch (e) {
                    // Continue to next fix
                }
            }
        }
        return { success: false, reason: 'Could not fix JSON parsing error' };
    }
    /**
     * Fix compilation errors
     */
    async fixCompilationError(error, context) {
        const errorStr = String(error);
        // Common compilation fixes
        if (errorStr.includes('Cannot find module')) {
            return { success: true, fix: 'Add missing import or install dependency' };
        }
        if (errorStr.includes('Type error')) {
            return { success: true, fix: 'Add type annotations or fix type mismatches' };
        }
        if (errorStr.includes('Syntax error')) {
            return { success: true, fix: 'Fix syntax issues in generated code' };
        }
        return { success: false, reason: 'Unknown compilation error pattern' };
    }
    /**
     * Fix database connection errors
     */
    async fixDatabaseConnectionError(error, context) {
        const errorStr = String(error);
        if (errorStr.includes('connection refused')) {
            return { success: true, fix: 'Check database server status and connection parameters' };
        }
        if (errorStr.includes('authentication failed')) {
            return { success: true, fix: 'Verify database credentials and permissions' };
        }
        return { success: false, reason: 'Unknown database error pattern' };
    }
    /**
     * Fix API integration errors
     */
    async fixAPIIntegrationError(error, context) {
        const errorStr = String(error);
        if (errorStr.includes('404 Not Found')) {
            return { success: true, fix: 'Check API endpoint URLs and routing configuration' };
        }
        if (errorStr.includes('500 Internal Server Error')) {
            return { success: true, fix: 'Review server-side error handling and logs' };
        }
        return { success: false, reason: 'Unknown API error pattern' };
    }
    /**
     * Fix test failure errors
     */
    async fixTestFailureError(error, context) {
        const errorStr = String(error);
        if (errorStr.includes('assertion error')) {
            return { success: true, fix: 'Update test expectations or fix implementation' };
        }
        if (errorStr.includes('jest')) {
            return { success: true, fix: 'Check Jest configuration and test setup' };
        }
        return { success: false, reason: 'Unknown test error pattern' };
    }
    /**
     * Fix security configuration errors
     */
    async fixSecurityConfigError(error, context) {
        const errorStr = String(error);
        if (errorStr.includes('JWT')) {
            return { success: true, fix: 'Check JWT secret configuration and token validation' };
        }
        if (errorStr.includes('authentication')) {
            return { success: true, fix: 'Review authentication middleware and configuration' };
        }
        return { success: false, reason: 'Unknown security error pattern' };
    }
    /**
     * Get self-healing statistics
     */
    getSelfHealingStats() {
        const totalAttempts = this.learningData.length;
        const successfulAttempts = this.learningData.filter(d => d.success).length;
        const successRate = totalAttempts > 0 ? successfulAttempts / totalAttempts : 0;
        return {
            autoFixEnabled: this.autoFixEnabled,
            totalFixStrategies: this.fixStrategies.size,
            errorPatternsLearned: this.errorPatterns.size,
            totalFixAttempts: totalAttempts,
            successfulFixes: successfulAttempts,
            overallSuccessRate: Math.round(successRate * 100),
            learningDataSize: this.learningData.length,
            maxFixAttempts: this.maxFixAttempts,
            fixTimeoutMs: this.fixTimeoutMs
        };
    }
    /**
     * Enable or disable auto-fixing
     */
    setAutoFixEnabled(enabled) {
        this.autoFixEnabled = enabled;
        this.logger.info('Auto-fix toggled', { enabled });
    }
    /**
     * Clear learning data
     */
    clearLearningData() {
        this.learningData = [];
        this.errorPatterns.clear();
        this.logger.info('Learning data cleared');
    }
    /**
     * Get error patterns for analysis
     */
    getErrorPatterns() {
        return Array.from(this.errorPatterns.values());
    }
    /**
     * Get fix strategies for analysis
     */
    getFixStrategies() {
        return Array.from(this.fixStrategies.values());
    }
    /**
     * Get learning data for analysis
     */
    getLearningData() {
        return [...this.learningData];
    }
}
exports.SelfHealingManager = SelfHealingManager;
//# sourceMappingURL=SelfHealingManager.js.map
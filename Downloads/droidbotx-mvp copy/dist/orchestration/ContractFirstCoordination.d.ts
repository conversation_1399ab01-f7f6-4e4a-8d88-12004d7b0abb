import { BusinessLogicResult } from '../agents/BusinessLogicAgent';
import { GeneratedCode } from '../agents/CodingAgent';
export interface APIContract {
    endpoint: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    requestSchema: any;
    responseSchema: any;
    authentication: boolean;
    validation: string[];
    errorCodes: number[];
}
export interface DatabaseContract {
    tableName: string;
    fields: DatabaseField[];
    relationships: DatabaseRelationship[];
    indexes: string[];
    constraints: string[];
}
export interface DatabaseField {
    name: string;
    type: string;
    nullable: boolean;
    primaryKey: boolean;
    foreignKey?: {
        table: string;
        field: string;
    };
    validation?: string[];
}
export interface DatabaseRelationship {
    type: 'one-to-one' | 'one-to-many' | 'many-to-many';
    fromTable: string;
    toTable: string;
    fromField: string;
    toField: string;
}
export interface ComponentContract {
    name: string;
    props: ComponentProp[];
    state: ComponentState[];
    apiCalls: string[];
    routes: string[];
}
export interface ComponentProp {
    name: string;
    type: string;
    required: boolean;
    defaultValue?: any;
}
export interface ComponentState {
    name: string;
    type: string;
    initialValue: any;
}
export interface ContractValidationResult {
    success: boolean;
    violations: ContractViolation[];
    warnings: string[];
    score: number;
}
export interface ContractViolation {
    type: 'api-mismatch' | 'database-mismatch' | 'component-mismatch' | 'type-mismatch';
    severity: 'critical' | 'high' | 'medium' | 'low';
    description: string;
    location: string;
    suggestedFix: string;
}
export interface SessionContracts {
    sessionId: string;
    apiContracts: APIContract[];
    databaseContracts: DatabaseContract[];
    componentContracts: ComponentContract[];
    generatedAt: number;
    lastValidated: number;
}
export declare class ContractFirstCoordination {
    private logger;
    private sessionContracts;
    constructor();
    /**
     * Generate contracts from business logic
     */
    generateContracts(sessionId: string, businessLogic: BusinessLogicResult): Promise<SessionContracts>;
    /**
     * Get contracts for a session
     */
    getContracts(sessionId: string): Promise<SessionContracts | null>;
    /**
     * Validate generated code against contracts
     */
    validateCodeAgainstContracts(sessionId: string, generatedCode: GeneratedCode): Promise<ContractValidationResult>;
    /**
     * Generate API contracts from business logic
     */
    private generateAPIContracts;
    /**
     * Extract API contracts from route code
     */
    private extractAPIContractsFromCode;
    /**
     * Generate database contracts from schema
     */
    private generateDatabaseContracts;
    /**
     * Generate component contracts from application flow
     */
    private generateComponentContracts;
    /**
     * Validate API contracts against generated code
     */
    private validateAPIContracts;
    /**
     * Validate database contracts against generated code
     */
    private validateDatabaseContracts;
    /**
     * Validate component contracts against generated code
     */
    private validateComponentContracts;
    private checkBackendEndpointExists;
    private checkFrontendAPICall;
    private checkModelExists;
    private calculateComplianceScore;
    private extractValidationPatterns;
    private inferRequestSchema;
    private inferResponseSchema;
    private convertTableFields;
    private extractRelationships;
    private inferComponentProps;
    private inferComponentState;
    private inferAPICallsForComponent;
    /**
     * Clean up contracts for a session
     */
    cleanupSession(sessionId: string): void;
}
//# sourceMappingURL=ContractFirstCoordination.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractFirstCoordination = void 0;
const Logger_1 = require("../core/Logger");
class ContractFirstCoordination {
    constructor() {
        this.sessionContracts = new Map();
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Generate contracts from business logic
     */
    async generateContracts(sessionId, businessLogic) {
        this.logger.info('Generating contracts from business logic', { sessionId });
        const contracts = {
            sessionId,
            apiContracts: await this.generateAPIContracts(businessLogic),
            databaseContracts: await this.generateDatabaseContracts(businessLogic),
            componentContracts: await this.generateComponentContracts(businessLogic),
            generatedAt: Date.now(),
            lastValidated: 0
        };
        this.sessionContracts.set(sessionId, contracts);
        this.logger.info('Contracts generated successfully', {
            sessionId,
            apiContracts: contracts.apiContracts.length,
            databaseContracts: contracts.databaseContracts.length,
            componentContracts: contracts.componentContracts.length
        });
        return contracts;
    }
    /**
     * Get contracts for a session
     */
    async getContracts(sessionId) {
        return this.sessionContracts.get(sessionId) || null;
    }
    /**
     * Validate generated code against contracts
     */
    async validateCodeAgainstContracts(sessionId, generatedCode) {
        const contracts = this.sessionContracts.get(sessionId);
        if (!contracts) {
            return {
                success: false,
                violations: [{
                        type: 'api-mismatch',
                        severity: 'critical',
                        description: 'No contracts found for session',
                        location: 'session',
                        suggestedFix: 'Generate contracts first'
                    }],
                warnings: [],
                score: 0
            };
        }
        this.logger.info('Validating code against contracts', { sessionId });
        const violations = [];
        const warnings = [];
        // Validate API contracts
        const apiViolations = await this.validateAPIContracts(contracts.apiContracts, generatedCode);
        violations.push(...apiViolations);
        // Validate database contracts
        const dbViolations = await this.validateDatabaseContracts(contracts.databaseContracts, generatedCode);
        violations.push(...dbViolations);
        // Validate component contracts
        const componentViolations = await this.validateComponentContracts(contracts.componentContracts, generatedCode);
        violations.push(...componentViolations);
        // Calculate compliance score
        const score = this.calculateComplianceScore(violations, contracts);
        contracts.lastValidated = Date.now();
        const result = {
            success: violations.filter(v => v.severity === 'critical').length === 0,
            violations,
            warnings,
            score
        };
        this.logger.info('Contract validation completed', {
            sessionId,
            success: result.success,
            violations: violations.length,
            score
        });
        return result;
    }
    /**
     * Generate API contracts from business logic
     */
    async generateAPIContracts(businessLogic) {
        const contracts = [];
        if (!businessLogic.domainAPIs) {
            return contracts;
        }
        for (const [filePath, content] of Object.entries(businessLogic.domainAPIs)) {
            const apiContracts = this.extractAPIContractsFromCode(filePath, content);
            contracts.push(...apiContracts);
        }
        return contracts;
    }
    /**
     * Extract API contracts from route code
     */
    extractAPIContractsFromCode(filePath, content) {
        const contracts = [];
        // Extract route definitions
        const routeRegex = /router\.(get|post|put|delete|patch)\(['"`]([^'"`]+)['"`]/g;
        let match;
        while ((match = routeRegex.exec(content)) !== null) {
            const method = match[1].toUpperCase();
            const endpoint = match[2];
            // Determine if authentication is required
            const authRequired = content.includes('authenticateToken') || content.includes('requireAuth');
            // Extract validation patterns
            const validation = this.extractValidationPatterns(content, endpoint);
            contracts.push({
                endpoint,
                method,
                requestSchema: this.inferRequestSchema(content, endpoint, method),
                responseSchema: this.inferResponseSchema(content, endpoint),
                authentication: authRequired,
                validation,
                errorCodes: [200, 400, 401, 403, 404, 500]
            });
        }
        return contracts;
    }
    /**
     * Generate database contracts from schema
     */
    async generateDatabaseContracts(businessLogic) {
        const contracts = [];
        if (!businessLogic.databaseSchema?.tables) {
            return contracts;
        }
        for (const table of businessLogic.databaseSchema.tables) {
            const contract = {
                tableName: table.name,
                fields: this.convertTableFields(table.fields || []),
                relationships: this.extractRelationships(table),
                indexes: table.indexes || [],
                constraints: table.constraints || []
            };
            contracts.push(contract);
        }
        return contracts;
    }
    /**
     * Generate component contracts from application flow
     */
    async generateComponentContracts(businessLogic) {
        const contracts = [];
        if (!businessLogic.applicationFlow?.routes) {
            return contracts;
        }
        for (const route of businessLogic.applicationFlow.routes) {
            const contract = {
                name: route.component,
                props: this.inferComponentProps(route),
                state: this.inferComponentState(route),
                apiCalls: this.inferAPICallsForComponent(route, businessLogic),
                routes: [route.path]
            };
            contracts.push(contract);
        }
        return contracts;
    }
    /**
     * Validate API contracts against generated code
     */
    async validateAPIContracts(contracts, generatedCode) {
        const violations = [];
        // Find backend route files
        const routeFiles = Object.entries(generatedCode.files).filter(([path]) => path.includes('routes') && path.endsWith('.ts'));
        // Find frontend API call files
        const frontendFiles = Object.entries(generatedCode.files).filter(([path]) => path.includes('frontend/src') && (path.endsWith('.tsx') || path.endsWith('.ts')));
        for (const contract of contracts) {
            // Check if backend endpoint exists
            const backendImplemented = this.checkBackendEndpointExists(contract, routeFiles);
            if (!backendImplemented) {
                violations.push({
                    type: 'api-mismatch',
                    severity: 'critical',
                    description: `Backend endpoint ${contract.method} ${contract.endpoint} not implemented`,
                    location: 'backend/routes',
                    suggestedFix: `Implement ${contract.method} ${contract.endpoint} in backend routes`
                });
            }
            // Check if frontend calls the endpoint
            const frontendCalls = this.checkFrontendAPICall(contract, frontendFiles);
            if (!frontendCalls) {
                violations.push({
                    type: 'api-mismatch',
                    severity: 'medium',
                    description: `Frontend does not call ${contract.method} ${contract.endpoint}`,
                    location: 'frontend/src',
                    suggestedFix: `Add API call to ${contract.endpoint} in appropriate component`
                });
            }
        }
        return violations;
    }
    /**
     * Validate database contracts against generated code
     */
    async validateDatabaseContracts(contracts, generatedCode) {
        const violations = [];
        // Check database initialization file
        const initSql = generatedCode.files['backend/init.sql'] || generatedCode.files['init.sql'];
        if (!initSql) {
            violations.push({
                type: 'database-mismatch',
                severity: 'critical',
                description: 'Database initialization file not found',
                location: 'backend/init.sql',
                suggestedFix: 'Generate database initialization script'
            });
            return violations;
        }
        for (const contract of contracts) {
            // Check if table exists in SQL
            if (!initSql.includes(`CREATE TABLE ${contract.tableName}`)) {
                violations.push({
                    type: 'database-mismatch',
                    severity: 'critical',
                    description: `Table ${contract.tableName} not found in database schema`,
                    location: 'backend/init.sql',
                    suggestedFix: `Add CREATE TABLE statement for ${contract.tableName}`
                });
            }
            // Check if model exists in backend
            const modelExists = this.checkModelExists(contract.tableName, generatedCode);
            if (!modelExists) {
                violations.push({
                    type: 'database-mismatch',
                    severity: 'high',
                    description: `Model for ${contract.tableName} not found in backend`,
                    location: 'backend/src/models',
                    suggestedFix: `Create model file for ${contract.tableName}`
                });
            }
        }
        return violations;
    }
    /**
     * Validate component contracts against generated code
     */
    async validateComponentContracts(contracts, generatedCode) {
        const violations = [];
        for (const contract of contracts) {
            // Check if component file exists
            const componentPath = `frontend/src/components/${contract.name}.tsx`;
            const componentExists = generatedCode.files[componentPath];
            if (!componentExists) {
                violations.push({
                    type: 'component-mismatch',
                    severity: 'high',
                    description: `Component ${contract.name} not found`,
                    location: componentPath,
                    suggestedFix: `Create component file ${contract.name}.tsx`
                });
                continue;
            }
            // Check if component is properly imported in App.tsx
            const appFile = generatedCode.files['frontend/src/App.tsx'];
            if (appFile && !appFile.includes(`import ${contract.name}`)) {
                violations.push({
                    type: 'component-mismatch',
                    severity: 'medium',
                    description: `Component ${contract.name} not imported in App.tsx`,
                    location: 'frontend/src/App.tsx',
                    suggestedFix: `Add import statement for ${contract.name}`
                });
            }
        }
        return violations;
    }
    // Helper methods for contract validation
    checkBackendEndpointExists(contract, routeFiles) {
        for (const [, content] of routeFiles) {
            const methodPattern = new RegExp(`router\\.${contract.method.toLowerCase()}\\(['"\`]${contract.endpoint.replace(/:\w+/g, ':[^/]+')}['"\`]`);
            if (methodPattern.test(content)) {
                return true;
            }
        }
        return false;
    }
    checkFrontendAPICall(contract, frontendFiles) {
        const endpointPattern = contract.endpoint.replace(/:\w+/g, '[^/]+');
        for (const [, content] of frontendFiles) {
            if (content.includes(contract.endpoint) || new RegExp(endpointPattern).test(content)) {
                return true;
            }
        }
        return false;
    }
    checkModelExists(tableName, generatedCode) {
        const modelPath = `backend/src/models/${tableName}.ts`;
        return !!generatedCode.files[modelPath];
    }
    calculateComplianceScore(violations, contracts) {
        const totalContracts = contracts.apiContracts.length + contracts.databaseContracts.length + contracts.componentContracts.length;
        if (totalContracts === 0)
            return 100;
        const severityWeights = { critical: 10, high: 5, medium: 2, low: 1 };
        const totalPenalty = violations.reduce((sum, v) => sum + severityWeights[v.severity], 0);
        const maxPossiblePenalty = totalContracts * 10; // Assuming all could be critical
        return Math.max(0, Math.round(100 - (totalPenalty / maxPossiblePenalty) * 100));
    }
    // Helper methods for contract generation
    extractValidationPatterns(content, endpoint) {
        const patterns = [];
        if (content.includes('body.email'))
            patterns.push('email-validation');
        if (content.includes('body.password'))
            patterns.push('password-validation');
        if (content.includes('params.id'))
            patterns.push('id-validation');
        if (content.includes('validateInput'))
            patterns.push('input-validation');
        return patterns;
    }
    inferRequestSchema(content, endpoint, method) {
        // Simple schema inference based on common patterns
        const schema = { type: 'object', properties: {} };
        if (method === 'POST' || method === 'PUT') {
            if (content.includes('body.email'))
                schema.properties.email = { type: 'string', format: 'email' };
            if (content.includes('body.password'))
                schema.properties.password = { type: 'string', minLength: 6 };
            if (content.includes('body.name'))
                schema.properties.name = { type: 'string' };
        }
        return schema;
    }
    inferResponseSchema(content, endpoint) {
        return {
            type: 'object',
            properties: {
                success: { type: 'boolean' },
                data: { type: 'object' },
                message: { type: 'string' }
            }
        };
    }
    convertTableFields(fields) {
        return fields.map(field => ({
            name: field.name,
            type: field.type,
            nullable: field.nullable !== false,
            primaryKey: field.primaryKey === true,
            foreignKey: field.foreignKey,
            validation: field.validation || []
        }));
    }
    extractRelationships(table) {
        // Extract relationships from table definition
        return table.relationships || [];
    }
    inferComponentProps(route) {
        // Infer props based on route parameters
        const props = [];
        if (route.path.includes(':id')) {
            props.push({
                name: 'id',
                type: 'string',
                required: true
            });
        }
        return props;
    }
    inferComponentState(route) {
        // Infer common state patterns
        return [
            { name: 'loading', type: 'boolean', initialValue: false },
            { name: 'error', type: 'string | null', initialValue: null }
        ];
    }
    inferAPICallsForComponent(route, businessLogic) {
        const apiCalls = [];
        // Map component to likely API endpoints
        const componentName = route.component.toLowerCase();
        if (businessLogic.domainAPIs) {
            for (const apiPath of Object.keys(businessLogic.domainAPIs)) {
                if (apiPath.includes(componentName) || apiPath.includes(route.path.split('/')[1])) {
                    apiCalls.push(apiPath);
                }
            }
        }
        return apiCalls;
    }
    /**
     * Clean up contracts for a session
     */
    cleanupSession(sessionId) {
        this.sessionContracts.delete(sessionId);
        this.logger.debug('Cleaned up contracts for session', { sessionId });
    }
}
exports.ContractFirstCoordination = ContractFirstCoordination;
//# sourceMappingURL=ContractFirstCoordination.js.map
import { WorkflowPhase } from './Orchestrator';
import { AgentResult } from '../core/BaseAgent';
export interface DependencyRule {
    phase: WorkflowPhase;
    requiredPhases: WorkflowPhase[];
    requiredData: string[];
    optionalData: string[];
    validationRules: ValidationRule[];
}
export interface ValidationRule {
    name: string;
    description: string;
    validator: (data: any) => boolean;
    severity: 'critical' | 'high' | 'medium' | 'low';
    errorMessage: string;
}
export interface DependencyValidationResult {
    success: boolean;
    issues: string[];
    warnings: string[];
    missingDependencies: string[];
    score: number;
}
export interface PhaseDataRequirement {
    key: string;
    type: 'required' | 'optional';
    validator?: (value: any) => boolean;
    description: string;
}
export declare class DependencyValidator {
    private logger;
    private dependencyRules;
    constructor();
    /**
     * Validate dependencies for a phase
     */
    validateDependencies(phase: WorkflowPhase, previousResult?: AgentResult, sharedState?: Map<string, any>): Promise<DependencyValidationResult>;
    /**
     * Initialize dependency rules for all phases
     */
    private initializeDependencyRules;
    /**
     * Validate that required phases have completed
     */
    private validateRequiredPhases;
    /**
     * Validate that required data is present
     */
    private validateRequiredData;
    /**
     * Run custom validation rules
     */
    private runCustomValidations;
    /**
     * Check if data has a specific key (supports nested keys)
     */
    private hasDataKey;
    /**
     * Check if shared state has a specific key
     */
    private hasSharedStateKey;
    /**
     * Calculate validation score based on issues and warnings
     */
    private calculateValidationScore;
    /**
     * Add custom dependency rule
     */
    addDependencyRule(rule: DependencyRule): void;
    /**
     * Get dependency rule for a phase
     */
    getDependencyRule(phase: WorkflowPhase): DependencyRule | undefined;
    /**
     * Get all dependency rules
     */
    getAllDependencyRules(): Map<WorkflowPhase, DependencyRule>;
}
//# sourceMappingURL=DependencyValidator.d.ts.map
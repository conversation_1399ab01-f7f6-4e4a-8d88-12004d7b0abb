import { BaseAgent, AgentResult } from '../core/BaseAgent';
import { WorkflowPhase, WorkflowRequest, WorkflowResult } from './Orchestrator';
export interface EnhancedWorkflowMetrics {
    totalDuration: number;
    phaseCompletionRate: number;
    crossLayerConsistencyScore: number;
    compilationSuccessRate: number;
    cacheHitRate: number;
    memoryUsageReduction: number;
    errorRecoverySuccessRate: number;
}
export interface PhaseExecutionContext {
    phase: WorkflowPhase;
    request: WorkflowRequest;
    previousResult?: AgentResult;
    planResult?: AgentResult;
    businessLogicResult?: AgentResult;
    sharedState: Map<string, any>;
    qualityPlan: any;
    contracts: any;
}
export declare class EnhancedSequentialOrchestrator {
    private agents;
    private logger;
    private cacheManager;
    private streamingGenerator;
    private contractCoordination;
    private sharedStateManager;
    private dependencyValidator;
    private proactiveQuality;
    private crossLayerValidator;
    private errorRecovery;
    private llmManager;
    private fileManager;
    private qualityGates;
    private performanceOptimization;
    private metrics;
    constructor();
    private initializeMetrics;
    registerAgent(agent: BaseAgent): void;
    /**
     * Execute enhanced sequential workflow with all optimizations
     */
    executeEnhancedWorkflow(request: WorkflowRequest): Promise<WorkflowResult>;
    private initializePhaseResults;
    /**
     * Execute sequential phases with all optimizations applied
     */
    private executeSequentialPhasesWithOptimizations;
    private calculatePhaseCompletionRate;
    private calculateConsistencyScore;
    /**
     * Execute individual phase with all optimizations
     */
    private executePhaseWithOptimizations;
    private findAgentForPhase;
    /**
     * Get current workflow metrics
     */
    getMetrics(): EnhancedWorkflowMetrics;
    /**
     * Reset metrics for new workflow execution
     */
    resetMetrics(): void;
    /**
     * Adapt data flow between phases to ensure compatibility
     */
    private adaptDataFlow;
    /**
     * Validate prerequisites for critical phases to ensure they have required data
     */
    private validatePhasePrerequisites;
}
//# sourceMappingURL=EnhancedSequentialOrchestrator.d.ts.map
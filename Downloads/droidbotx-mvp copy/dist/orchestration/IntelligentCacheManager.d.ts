import { WorkflowPhase, WorkflowRequest } from './Orchestrator';
import { AgentResult } from '../core/BaseAgent';
export interface CachedResult {
    result: AgentResult;
    timestamp: number;
    cacheKey: string;
    metadata: {
        phase: WorkflowPhase;
        requestHash: string;
        inputHash: string;
        hitCount: number;
        lastAccessed: number;
        aiDomain?: string;
    };
}
export interface CachePattern {
    pattern: string;
    frequency: number;
    successRate: number;
    avgExecutionTime: number;
    lastUsed: number;
}
export interface CacheMetrics {
    totalRequests: number;
    cacheHits: number;
    cacheMisses: number;
    hitRate: number;
    memoryUsage: number;
    evictions: number;
}
export declare class IntelligentCacheManager {
    private cache;
    private patterns;
    private logger;
    private maxCacheSize;
    private maxCacheAge;
    private metrics;
    constructor();
    /**
     * Get cached result if available and valid
     */
    getCachedResult(phase: WorkflowPhase, request: WorkflowRequest, previousResult?: AgentResult): Promise<AgentResult | null>;
    /**
     * Cache a successful result with AI semantic analysis integration
     */
    cacheResult(phase: WorkflowPhase, request: WorkflowRequest, previousResult: AgentResult | undefined, result: AgentResult): Promise<void>;
    /**
     * Generate intelligent cache key based on request characteristics
     */
    private generateCacheKey;
    /**
     * Extract semantic features from request for intelligent caching
     * Now uses AI-driven analysis instead of hardcoded keyword matching
     */
    private extractRequestFeatures;
    /**
     * Extract request type from AI semantic analysis results
     * Falls back to basic classification if AI analysis not available
     */
    private extractRequestTypeFromAI;
    private mapDomainToRequestType;
    private estimateComplexity;
    /**
     * Extract domain from AI semantic analysis results
     * This replaces the old hardcoded keyword matching approach
     */
    private extractDomainFromAI;
    /**
     * Extract tech stack from AI semantic analysis results
     * This replaces the old hardcoded keyword matching approach
     */
    private extractTechStackFromAI;
    private extractInputFeatures;
    private hashRequest;
    private hashInput;
    private isCacheValid;
    private evictLeastUsed;
    private updatePatternFrequency;
    private updateHitRate;
    private deepCopyResult;
    /**
     * Enrich request with AI semantic analysis results for better caching
     */
    private enrichRequestWithAIAnalysis;
    private startCacheMaintenance;
    private performMaintenance;
    /**
     * Get cache metrics
     */
    getMetrics(): CacheMetrics;
    /**
     * Clear all cache entries
     */
    clearCache(): void;
}
//# sourceMappingURL=IntelligentCacheManager.d.ts.map
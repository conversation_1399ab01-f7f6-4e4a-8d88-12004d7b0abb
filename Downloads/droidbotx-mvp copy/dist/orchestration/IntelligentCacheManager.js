"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntelligentCacheManager = void 0;
const Logger_1 = require("../core/Logger");
const crypto = __importStar(require("crypto"));
class IntelligentCacheManager {
    constructor() {
        this.cache = new Map();
        this.patterns = new Map();
        this.maxCacheSize = 1000;
        this.maxCacheAge = 24 * 60 * 60 * 1000; // 24 hours
        this.logger = Logger_1.Logger.getInstance();
        this.metrics = {
            totalRequests: 0,
            cacheHits: 0,
            cacheMisses: 0,
            hitRate: 0,
            memoryUsage: 0,
            evictions: 0
        };
        // Start cache maintenance
        this.startCacheMaintenance();
    }
    /**
     * Get cached result if available and valid
     */
    async getCachedResult(phase, request, previousResult) {
        this.metrics.totalRequests++;
        try {
            const cacheKey = this.generateCacheKey(phase, request, previousResult);
            const cached = this.cache.get(cacheKey);
            if (!cached) {
                this.metrics.cacheMisses++;
                return null;
            }
            // Check if cache entry is still valid
            if (!this.isCacheValid(cached)) {
                this.cache.delete(cacheKey);
                this.metrics.cacheMisses++;
                return null;
            }
            // Update access metadata
            cached.metadata.hitCount++;
            cached.metadata.lastAccessed = Date.now();
            this.metrics.cacheHits++;
            this.updateHitRate();
            // Update pattern frequency
            this.updatePatternFrequency(cacheKey, true);
            this.logger.debug(`Cache hit for ${phase}`, {
                cacheKey,
                hitCount: cached.metadata.hitCount,
                age: Date.now() - cached.timestamp
            });
            // Return a deep copy to prevent mutation
            return this.deepCopyResult(cached.result);
        }
        catch (error) {
            this.logger.warn('Cache retrieval failed', {
                phase,
                error: error instanceof Error ? error.message : String(error)
            });
            this.metrics.cacheMisses++;
            return null;
        }
    }
    /**
     * Cache a successful result with AI semantic analysis integration
     */
    async cacheResult(phase, request, previousResult, result) {
        try {
            // Update request context with AI semantic analysis if available
            const updatedRequest = this.enrichRequestWithAIAnalysis(request, result);
            const cacheKey = this.generateCacheKey(phase, updatedRequest, previousResult);
            // Don't cache failed results
            if (!result.success) {
                return;
            }
            // Check cache size and evict if necessary
            if (this.cache.size >= this.maxCacheSize) {
                await this.evictLeastUsed();
            }
            const cachedResult = {
                result: this.deepCopyResult(result),
                timestamp: Date.now(),
                cacheKey,
                metadata: {
                    phase,
                    requestHash: this.hashRequest(updatedRequest),
                    inputHash: this.hashInput(previousResult),
                    hitCount: 0,
                    lastAccessed: Date.now(),
                    aiDomain: result.metadata?.semanticAnalysis?.domain || 'unknown'
                }
            };
            this.cache.set(cacheKey, cachedResult);
            this.updatePatternFrequency(cacheKey, false);
            this.logger.debug(`Cached result for ${phase}`, {
                cacheKey,
                cacheSize: this.cache.size
            });
        }
        catch (error) {
            this.logger.warn('Cache storage failed', {
                phase,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
    /**
     * Generate intelligent cache key based on request characteristics
     */
    generateCacheKey(phase, request, previousResult) {
        const requestFeatures = this.extractRequestFeatures(request);
        const inputFeatures = this.extractInputFeatures(previousResult);
        const keyData = {
            phase,
            features: requestFeatures,
            input: inputFeatures,
            version: '1.0' // For cache invalidation when logic changes
        };
        return crypto
            .createHash('sha256')
            .update(JSON.stringify(keyData))
            .digest('hex')
            .substring(0, 16);
    }
    /**
     * Extract semantic features from request for intelligent caching
     * Now uses AI-driven analysis instead of hardcoded keyword matching
     */
    extractRequestFeatures(request) {
        const features = {
            type: this.extractRequestTypeFromAI(request),
            complexity: this.estimateComplexity(request.requirements),
            domain: this.extractDomainFromAI(request),
            techStack: this.extractTechStackFromAI(request.requirements)
        };
        return features;
    }
    /**
     * Extract request type from AI semantic analysis results
     * Falls back to basic classification if AI analysis not available
     */
    extractRequestTypeFromAI(request) {
        // Try to extract from AI semantic analysis if available in context
        const aiDomain = request.context?.metadata?.semanticAnalysis?.domain;
        if (aiDomain) {
            return this.mapDomainToRequestType(aiDomain);
        }
        // Fallback to simple classification (no keyword matching)
        return 'general';
    }
    mapDomainToRequestType(domain) {
        const domainMap = {
            'e-commerce': 'ecommerce',
            'ecommerce': 'ecommerce',
            'healthcare': 'healthcare',
            'education': 'education',
            'e-learning': 'education',
            'finance': 'finance',
            'project-management': 'productivity',
            'social': 'social',
            'content-management': 'content'
        };
        return domainMap[domain.toLowerCase()] || 'general';
    }
    estimateComplexity(requirements) {
        const complexityIndicators = [
            'payment', 'integration', 'real-time', 'analytics', 'reporting',
            'multi-tenant', 'microservices', 'oauth', 'websocket', 'notification'
        ];
        const complexFeatures = requirements.filter(req => complexityIndicators.some(indicator => req.toLowerCase().includes(indicator))).length;
        if (complexFeatures >= 3)
            return 'complex';
        if (complexFeatures >= 1)
            return 'medium';
        return 'simple';
    }
    /**
     * Extract domain from AI semantic analysis results
     * This replaces the old hardcoded keyword matching approach
     */
    extractDomainFromAI(request) {
        // Try to extract from AI semantic analysis if available in context
        const aiDomain = request.context?.metadata?.semanticAnalysis?.domain;
        if (aiDomain) {
            return aiDomain;
        }
        // If no AI analysis available, return general (no keyword matching)
        return 'general';
    }
    /**
     * Extract tech stack from AI semantic analysis results
     * This replaces the old hardcoded keyword matching approach
     */
    extractTechStackFromAI(requirements) {
        // For now, return default tech stack since AI semantic analysis
        // focuses on business domain rather than tech stack
        // This could be enhanced to extract from AI analysis in the future
        return ['react', 'node', 'postgres'];
    }
    extractInputFeatures(previousResult) {
        if (!previousResult?.data)
            return null;
        return {
            hasFiles: !!previousResult.data.files,
            fileCount: previousResult.data.files ? Object.keys(previousResult.data.files).length : 0,
            hasDatabase: !!previousResult.data.databaseSchema,
            hasAPIs: !!previousResult.data.domainAPIs,
            success: previousResult.success
        };
    }
    hashRequest(request) {
        return crypto
            .createHash('md5')
            .update(JSON.stringify({
            description: request.description,
            requirements: request.requirements.sort()
        }))
            .digest('hex');
    }
    hashInput(input) {
        if (!input)
            return 'no-input';
        return crypto
            .createHash('md5')
            .update(JSON.stringify({
            success: input.success,
            dataKeys: input.data ? Object.keys(input.data).sort() : []
        }))
            .digest('hex');
    }
    isCacheValid(cached) {
        const age = Date.now() - cached.timestamp;
        return age < this.maxCacheAge;
    }
    async evictLeastUsed() {
        const entries = Array.from(this.cache.entries());
        // Sort by hit count (ascending) and last accessed (ascending)
        entries.sort((a, b) => {
            const aScore = a[1].metadata.hitCount + (a[1].metadata.lastAccessed / 1000000);
            const bScore = b[1].metadata.hitCount + (b[1].metadata.lastAccessed / 1000000);
            return aScore - bScore;
        });
        // Remove bottom 10%
        const toRemove = Math.max(1, Math.floor(entries.length * 0.1));
        for (let i = 0; i < toRemove; i++) {
            this.cache.delete(entries[i][0]);
            this.metrics.evictions++;
        }
        this.logger.debug(`Evicted ${toRemove} cache entries`, {
            remainingSize: this.cache.size
        });
    }
    updatePatternFrequency(cacheKey, isHit) {
        const pattern = cacheKey.substring(0, 8); // Use prefix as pattern
        const existing = this.patterns.get(pattern);
        if (existing) {
            existing.frequency++;
            existing.lastUsed = Date.now();
            if (isHit) {
                existing.successRate = (existing.successRate + 1) / 2; // Moving average
            }
        }
        else {
            this.patterns.set(pattern, {
                pattern,
                frequency: 1,
                successRate: isHit ? 1 : 0,
                avgExecutionTime: 0,
                lastUsed: Date.now()
            });
        }
    }
    updateHitRate() {
        this.metrics.hitRate = (this.metrics.cacheHits / this.metrics.totalRequests) * 100;
    }
    deepCopyResult(result) {
        return JSON.parse(JSON.stringify(result));
    }
    /**
     * Enrich request with AI semantic analysis results for better caching
     */
    enrichRequestWithAIAnalysis(request, result) {
        const enrichedRequest = { ...request };
        // Add AI semantic analysis to request context if available in result
        if (result.metadata?.semanticAnalysis && !enrichedRequest.context.metadata?.semanticAnalysis) {
            enrichedRequest.context = {
                ...enrichedRequest.context,
                metadata: {
                    ...enrichedRequest.context.metadata,
                    semanticAnalysis: result.metadata.semanticAnalysis
                }
            };
        }
        return enrichedRequest;
    }
    startCacheMaintenance() {
        // Run maintenance every 30 minutes
        setInterval(() => {
            this.performMaintenance();
        }, 30 * 60 * 1000);
    }
    performMaintenance() {
        const before = this.cache.size;
        // Remove expired entries
        for (const [key, cached] of this.cache.entries()) {
            if (!this.isCacheValid(cached)) {
                this.cache.delete(key);
            }
        }
        // Clean old patterns
        const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days
        for (const [pattern, data] of this.patterns.entries()) {
            if (data.lastUsed < cutoff) {
                this.patterns.delete(pattern);
            }
        }
        const after = this.cache.size;
        if (before !== after) {
            this.logger.debug(`Cache maintenance completed`, {
                entriesRemoved: before - after,
                currentSize: after
            });
        }
    }
    /**
     * Get cache metrics
     */
    getMetrics() {
        this.metrics.memoryUsage = this.cache.size;
        return { ...this.metrics };
    }
    /**
     * Clear all cache entries
     */
    clearCache() {
        this.cache.clear();
        this.patterns.clear();
        this.logger.info('Cache cleared');
    }
}
exports.IntelligentCacheManager = IntelligentCacheManager;
//# sourceMappingURL=IntelligentCacheManager.js.map
import { WorkflowPhase } from './Orchestrator';
import { AgentResult } from '../core/BaseAgent';
import { PhaseExecutionContext } from './EnhancedSequentialOrchestrator';
export interface ErrorPattern {
    pattern: string;
    frequency: number;
    successfulRecoveries: number;
    recoveryStrategies: RecoveryStrategy[];
    lastSeen: number;
}
export interface RecoveryStrategy {
    name: string;
    description: string;
    applicablePhases: WorkflowPhase[];
    successRate: number;
    recoveryFunction: (error: Error, context: PhaseExecutionContext) => Promise<RecoveryResult>;
    priority: number;
}
export interface RecoveryResult {
    success: boolean;
    result?: AgentResult;
    strategy: string;
    timeToRecover: number;
    additionalAttempts?: number;
}
export interface ErrorClassification {
    type: 'compilation' | 'dependency' | 'schema-mismatch' | 'timeout' | 'api-error' | 'configuration' | 'unknown';
    severity: 'critical' | 'high' | 'medium' | 'low';
    recoverable: boolean;
    confidence: number;
}
export interface RecoveryMetrics {
    totalErrors: number;
    successfulRecoveries: number;
    failedRecoveries: number;
    averageRecoveryTime: number;
    recoverySuccessRate: number;
    patternLearningRate: number;
}
export declare class IntelligentErrorRecovery {
    private logger;
    private errorPatterns;
    private recoveryStrategies;
    private metrics;
    constructor();
    /**
     * Handle phase error with intelligent recovery
     */
    handlePhaseError(phase: WorkflowPhase, error: Error, context: PhaseExecutionContext): Promise<RecoveryResult>;
    /**
     * Classify error type and characteristics
     */
    private classifyError;
    /**
     * Initialize recovery strategies
     */
    private initializeRecoveryStrategies;
    /**
     * Recovery strategy implementations
     */
    private recoverFromCompilationError;
    private recoverFromDependencyError;
    private recoverFromSchemaMismatch;
    private recoverFromTimeout;
    private recoverFromConfigurationError;
    private genericRetryRecovery;
    /**
     * Helper methods
     */
    private findApplicableStrategies;
    private updateErrorPatterns;
    private updateStrategySuccessRate;
    private updateAverageRecoveryTime;
    private updateRecoverySuccessRate;
    /**
     * Get recovery metrics
     */
    getMetrics(): RecoveryMetrics;
    /**
     * Get learned error patterns
     */
    getErrorPatterns(): Map<string, ErrorPattern>;
}
//# sourceMappingURL=IntelligentErrorRecovery.d.ts.map
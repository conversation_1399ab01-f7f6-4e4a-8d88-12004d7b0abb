"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntelligentErrorRecovery = void 0;
const Logger_1 = require("../core/Logger");
const Orchestrator_1 = require("./Orchestrator");
class IntelligentErrorRecovery {
    constructor() {
        this.errorPatterns = new Map();
        this.recoveryStrategies = [];
        this.logger = Logger_1.Logger.getInstance();
        this.metrics = {
            totalErrors: 0,
            successfulRecoveries: 0,
            failedRecoveries: 0,
            averageRecoveryTime: 0,
            recoverySuccessRate: 0,
            patternLearningRate: 0
        };
        this.initializeRecoveryStrategies();
    }
    /**
     * Handle phase error with intelligent recovery
     */
    async handlePhaseError(phase, error, context) {
        const startTime = Date.now();
        this.metrics.totalErrors++;
        this.logger.warn('Attempting intelligent error recovery', {
            phase,
            error: error.message,
            sessionId: context.request.context.sessionId
        });
        try {
            // Classify the error
            const classification = this.classifyError(error, phase, context);
            // Update error patterns
            this.updateErrorPatterns(error, classification);
            // Find applicable recovery strategies
            const strategies = this.findApplicableStrategies(classification, phase);
            if (strategies.length === 0) {
                this.logger.warn('No recovery strategies found for error', {
                    phase,
                    errorType: classification.type,
                    error: error.message
                });
                this.metrics.failedRecoveries++;
                return {
                    success: false,
                    strategy: 'none',
                    timeToRecover: Date.now() - startTime
                };
            }
            // Attempt recovery with strategies in priority order
            for (const strategy of strategies) {
                try {
                    this.logger.info('Attempting recovery strategy', {
                        strategy: strategy.name,
                        phase,
                        successRate: strategy.successRate
                    });
                    const recoveryResult = await strategy.recoveryFunction(error, context);
                    if (recoveryResult.success) {
                        const recoveryTime = Date.now() - startTime;
                        // Update strategy success rate
                        this.updateStrategySuccessRate(strategy, true);
                        // Update metrics
                        this.metrics.successfulRecoveries++;
                        this.updateAverageRecoveryTime(recoveryTime);
                        this.updateRecoverySuccessRate();
                        this.logger.info('Error recovery successful', {
                            strategy: strategy.name,
                            phase,
                            recoveryTime,
                            sessionId: context.request.context.sessionId
                        });
                        return {
                            success: true,
                            result: recoveryResult.result,
                            strategy: strategy.name,
                            timeToRecover: recoveryTime,
                            additionalAttempts: recoveryResult.additionalAttempts
                        };
                    }
                    else {
                        this.updateStrategySuccessRate(strategy, false);
                        this.logger.debug('Recovery strategy failed', {
                            strategy: strategy.name,
                            phase
                        });
                    }
                }
                catch (strategyError) {
                    this.updateStrategySuccessRate(strategy, false);
                    this.logger.error('Recovery strategy threw error', {
                        strategy: strategy.name,
                        phase,
                        error: strategyError instanceof Error ? strategyError.message : String(strategyError)
                    });
                }
            }
            // All strategies failed
            this.metrics.failedRecoveries++;
            this.updateRecoverySuccessRate();
            this.logger.error('All recovery strategies failed', {
                phase,
                strategiesAttempted: strategies.length,
                originalError: error.message
            });
            return {
                success: false,
                strategy: 'all-failed',
                timeToRecover: Date.now() - startTime
            };
        }
        catch (recoveryError) {
            this.metrics.failedRecoveries++;
            this.logger.error('Error recovery system failed', {
                phase,
                originalError: error.message,
                recoveryError: recoveryError instanceof Error ? recoveryError.message : String(recoveryError)
            });
            return {
                success: false,
                strategy: 'system-error',
                timeToRecover: Date.now() - startTime
            };
        }
    }
    /**
     * Classify error type and characteristics
     */
    classifyError(error, phase, context) {
        const errorMessage = error.message.toLowerCase();
        const stackTrace = error.stack?.toLowerCase() || '';
        // Compilation errors
        if (errorMessage.includes('compilation') || errorMessage.includes('syntax') ||
            errorMessage.includes('typescript') || stackTrace.includes('tsc')) {
            return {
                type: 'compilation',
                severity: 'high',
                recoverable: true,
                confidence: 0.9
            };
        }
        // Dependency errors
        if (errorMessage.includes('module not found') || errorMessage.includes('cannot resolve') ||
            errorMessage.includes('dependency') || errorMessage.includes('package')) {
            return {
                type: 'dependency',
                severity: 'high',
                recoverable: true,
                confidence: 0.85
            };
        }
        // Schema mismatch errors
        if (errorMessage.includes('schema') || errorMessage.includes('table') ||
            errorMessage.includes('column') || errorMessage.includes('foreign key')) {
            return {
                type: 'schema-mismatch',
                severity: 'medium',
                recoverable: true,
                confidence: 0.8
            };
        }
        // Timeout errors
        if (errorMessage.includes('timeout') || errorMessage.includes('timed out') ||
            errorMessage.includes('connection') || stackTrace.includes('timeout')) {
            return {
                type: 'timeout',
                severity: 'medium',
                recoverable: true,
                confidence: 0.75
            };
        }
        // API errors
        if (errorMessage.includes('api') || errorMessage.includes('endpoint') ||
            errorMessage.includes('route') || errorMessage.includes('http')) {
            return {
                type: 'api-error',
                severity: 'medium',
                recoverable: true,
                confidence: 0.7
            };
        }
        // Configuration errors
        if (errorMessage.includes('config') || errorMessage.includes('environment') ||
            errorMessage.includes('env') || errorMessage.includes('port')) {
            return {
                type: 'configuration',
                severity: 'low',
                recoverable: true,
                confidence: 0.65
            };
        }
        // Unknown error
        return {
            type: 'unknown',
            severity: 'medium',
            recoverable: false,
            confidence: 0.3
        };
    }
    /**
     * Initialize recovery strategies
     */
    initializeRecoveryStrategies() {
        // Compilation error recovery
        this.recoveryStrategies.push({
            name: 'compilation-fix',
            description: 'Fix common TypeScript compilation errors',
            applicablePhases: [Orchestrator_1.WorkflowPhase.GENERATE, Orchestrator_1.WorkflowPhase.TESTING],
            successRate: 0.8,
            priority: 1,
            recoveryFunction: async (error, context) => {
                return await this.recoverFromCompilationError(error, context);
            }
        });
        // Dependency error recovery
        this.recoveryStrategies.push({
            name: 'dependency-resolution',
            description: 'Resolve missing dependencies and imports',
            applicablePhases: [Orchestrator_1.WorkflowPhase.GENERATE, Orchestrator_1.WorkflowPhase.TESTING],
            successRate: 0.75,
            priority: 2,
            recoveryFunction: async (error, context) => {
                return await this.recoverFromDependencyError(error, context);
            }
        });
        // Schema mismatch recovery
        this.recoveryStrategies.push({
            name: 'schema-alignment',
            description: 'Align database schema with application models',
            applicablePhases: [Orchestrator_1.WorkflowPhase.DATABASE, Orchestrator_1.WorkflowPhase.GENERATE],
            successRate: 0.7,
            priority: 3,
            recoveryFunction: async (error, context) => {
                return await this.recoverFromSchemaMismatch(error, context);
            }
        });
        // Timeout recovery
        this.recoveryStrategies.push({
            name: 'timeout-retry',
            description: 'Retry operation with exponential backoff',
            applicablePhases: Object.values(Orchestrator_1.WorkflowPhase),
            successRate: 0.6,
            priority: 4,
            recoveryFunction: async (error, context) => {
                return await this.recoverFromTimeout(error, context);
            }
        });
        // Configuration recovery
        this.recoveryStrategies.push({
            name: 'configuration-fix',
            description: 'Fix common configuration issues',
            applicablePhases: [Orchestrator_1.WorkflowPhase.GENERATE, Orchestrator_1.WorkflowPhase.DEPLOY],
            successRate: 0.85,
            priority: 5,
            recoveryFunction: async (error, context) => {
                return await this.recoverFromConfigurationError(error, context);
            }
        });
        // Generic retry strategy
        this.recoveryStrategies.push({
            name: 'generic-retry',
            description: 'Generic retry with modified parameters',
            applicablePhases: Object.values(Orchestrator_1.WorkflowPhase),
            successRate: 0.3,
            priority: 10,
            recoveryFunction: async (error, context) => {
                return await this.genericRetryRecovery(error, context);
            }
        });
    }
    /**
     * Recovery strategy implementations
     */
    async recoverFromCompilationError(error, context) {
        this.logger.debug('Attempting compilation error recovery');
        try {
            // Simulate compilation fix by regenerating with stricter type checking
            const modifiedContext = { ...context };
            // Add TypeScript strict mode fixes to the context
            if (modifiedContext.previousResult?.data?.files) {
                const files = { ...modifiedContext.previousResult.data.files };
                // Fix common TypeScript issues
                for (const [filePath, content] of Object.entries(files)) {
                    if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
                        let fixedContent = typeof content === 'string' ? content : String(content);
                        // Add missing imports
                        if (!fixedContent.includes('import React') && filePath.endsWith('.tsx')) {
                            fixedContent = "import React from 'react';\n" + fixedContent;
                        }
                        // Fix any type issues
                        fixedContent = fixedContent.replace(/: any/g, ': unknown');
                        fixedContent = fixedContent.replace(/\bany\b/g, 'unknown');
                        files[filePath] = fixedContent;
                    }
                }
                modifiedContext.previousResult.data.files = files;
            }
            return {
                success: true,
                result: modifiedContext.previousResult,
                strategy: 'compilation-fix',
                timeToRecover: 0
            };
        }
        catch (recoveryError) {
            return {
                success: false,
                strategy: 'compilation-fix',
                timeToRecover: 0
            };
        }
    }
    async recoverFromDependencyError(error, context) {
        this.logger.debug('Attempting dependency error recovery');
        try {
            // Simulate dependency resolution
            const modifiedContext = { ...context };
            if (modifiedContext.previousResult?.data?.files) {
                const files = { ...modifiedContext.previousResult.data.files };
                // Add missing dependencies to package.json files
                for (const [filePath, content] of Object.entries(files)) {
                    if (filePath.endsWith('package.json')) {
                        const packageJson = JSON.parse(typeof content === 'string' ? content : String(content));
                        // Add common missing dependencies
                        if (!packageJson.dependencies)
                            packageJson.dependencies = {};
                        if (!packageJson.devDependencies)
                            packageJson.devDependencies = {};
                        // Add React dependencies if missing
                        if (filePath.includes('frontend') && !packageJson.dependencies.react) {
                            packageJson.dependencies.react = '^18.2.0';
                            packageJson.dependencies['react-dom'] = '^18.2.0';
                            packageJson.devDependencies['@types/react'] = '^18.0.28';
                            packageJson.devDependencies['@types/react-dom'] = '^18.0.11';
                        }
                        // Add Express dependencies if missing
                        if (filePath.includes('backend') && !packageJson.dependencies.express) {
                            packageJson.dependencies.express = '^4.18.2';
                            packageJson.devDependencies['@types/express'] = '^4.17.17';
                        }
                        files[filePath] = JSON.stringify(packageJson, null, 2);
                    }
                }
                modifiedContext.previousResult.data.files = files;
            }
            return {
                success: true,
                result: modifiedContext.previousResult,
                strategy: 'dependency-resolution',
                timeToRecover: 0
            };
        }
        catch (recoveryError) {
            return {
                success: false,
                strategy: 'dependency-resolution',
                timeToRecover: 0
            };
        }
    }
    async recoverFromSchemaMismatch(error, context) {
        this.logger.debug('Attempting schema mismatch recovery');
        try {
            // Simulate schema alignment
            const modifiedContext = { ...context };
            if (modifiedContext.previousResult?.data?.files) {
                const files = { ...modifiedContext.previousResult.data.files };
                // Fix common schema issues in init.sql
                for (const [filePath, content] of Object.entries(files)) {
                    if (filePath.includes('init.sql')) {
                        let fixedSql = typeof content === 'string' ? content : String(content);
                        // Ensure UUID extension is enabled
                        if (!fixedSql.includes('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')) {
                            fixedSql = 'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";\n\n' + fixedSql;
                        }
                        // Fix common foreign key issues
                        fixedSql = fixedSql.replace(/REFERENCES (\w+)\((\w+)\)/g, 'REFERENCES $1($2) ON DELETE CASCADE');
                        files[filePath] = fixedSql;
                    }
                }
                modifiedContext.previousResult.data.files = files;
            }
            return {
                success: true,
                result: modifiedContext.previousResult,
                strategy: 'schema-alignment',
                timeToRecover: 0
            };
        }
        catch (recoveryError) {
            return {
                success: false,
                strategy: 'schema-alignment',
                timeToRecover: 0
            };
        }
    }
    async recoverFromTimeout(error, context) {
        this.logger.debug('Attempting timeout recovery with retry');
        // Implement exponential backoff retry
        const maxRetries = 3;
        let attempt = 0;
        while (attempt < maxRetries) {
            attempt++;
            const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
            this.logger.debug(`Retry attempt ${attempt} after ${delay}ms delay`);
            await new Promise(resolve => setTimeout(resolve, delay));
            try {
                // Simulate retry by returning the previous result
                // In a real implementation, this would re-execute the failed operation
                if (context.previousResult) {
                    return {
                        success: true,
                        result: context.previousResult,
                        strategy: 'timeout-retry',
                        timeToRecover: delay,
                        additionalAttempts: attempt
                    };
                }
            }
            catch (retryError) {
                this.logger.debug(`Retry attempt ${attempt} failed`, {
                    error: retryError instanceof Error ? retryError.message : String(retryError)
                });
            }
        }
        return {
            success: false,
            strategy: 'timeout-retry',
            timeToRecover: 0,
            additionalAttempts: maxRetries
        };
    }
    async recoverFromConfigurationError(error, context) {
        this.logger.debug('Attempting configuration error recovery');
        try {
            const modifiedContext = { ...context };
            if (modifiedContext.previousResult?.data?.files) {
                const files = { ...modifiedContext.previousResult.data.files };
                // Fix common configuration issues
                for (const [filePath, content] of Object.entries(files)) {
                    if (filePath.includes('.env')) {
                        let fixedEnv = typeof content === 'string' ? content : String(content);
                        // Ensure required environment variables are present
                        if (!fixedEnv.includes('PORT=')) {
                            fixedEnv += '\nPORT=5000';
                        }
                        if (!fixedEnv.includes('NODE_ENV=')) {
                            fixedEnv += '\nNODE_ENV=development';
                        }
                        if (!fixedEnv.includes('DATABASE_URL=')) {
                            fixedEnv += '\nDATABASE_URL=postgresql://user:password@localhost:5432/app_db';
                        }
                        files[filePath] = fixedEnv;
                    }
                }
                modifiedContext.previousResult.data.files = files;
            }
            return {
                success: true,
                result: modifiedContext.previousResult,
                strategy: 'configuration-fix',
                timeToRecover: 0
            };
        }
        catch (recoveryError) {
            return {
                success: false,
                strategy: 'configuration-fix',
                timeToRecover: 0
            };
        }
    }
    async genericRetryRecovery(error, context) {
        this.logger.debug('Attempting generic retry recovery');
        // Simple retry with the same context
        try {
            if (context.previousResult) {
                return {
                    success: true,
                    result: context.previousResult,
                    strategy: 'generic-retry',
                    timeToRecover: 0
                };
            }
        }
        catch (retryError) {
            // Retry failed
        }
        return {
            success: false,
            strategy: 'generic-retry',
            timeToRecover: 0
        };
    }
    /**
     * Helper methods
     */
    findApplicableStrategies(classification, phase) {
        return this.recoveryStrategies
            .filter(strategy => strategy.applicablePhases.includes(phase) &&
            (classification.recoverable || strategy.name === 'generic-retry'))
            .sort((a, b) => {
            // Sort by success rate (descending) then by priority (ascending)
            if (a.successRate !== b.successRate) {
                return b.successRate - a.successRate;
            }
            return a.priority - b.priority;
        });
    }
    updateErrorPatterns(error, classification) {
        const patternKey = `${classification.type}-${error.message.substring(0, 50)}`;
        const existing = this.errorPatterns.get(patternKey);
        if (existing) {
            existing.frequency++;
            existing.lastSeen = Date.now();
        }
        else {
            this.errorPatterns.set(patternKey, {
                pattern: patternKey,
                frequency: 1,
                successfulRecoveries: 0,
                recoveryStrategies: [],
                lastSeen: Date.now()
            });
        }
    }
    updateStrategySuccessRate(strategy, success) {
        // Update success rate using exponential moving average
        const alpha = 0.1; // Learning rate
        const newRate = success ? 1 : 0;
        strategy.successRate = (1 - alpha) * strategy.successRate + alpha * newRate;
    }
    updateAverageRecoveryTime(recoveryTime) {
        const alpha = 0.1;
        this.metrics.averageRecoveryTime = (1 - alpha) * this.metrics.averageRecoveryTime + alpha * recoveryTime;
    }
    updateRecoverySuccessRate() {
        const total = this.metrics.successfulRecoveries + this.metrics.failedRecoveries;
        this.metrics.recoverySuccessRate = total > 0 ? (this.metrics.successfulRecoveries / total) * 100 : 0;
    }
    /**
     * Get recovery metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Get learned error patterns
     */
    getErrorPatterns() {
        return new Map(this.errorPatterns);
    }
}
exports.IntelligentErrorRecovery = IntelligentErrorRecovery;
//# sourceMappingURL=IntelligentErrorRecovery.js.map
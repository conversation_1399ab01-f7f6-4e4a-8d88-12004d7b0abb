import { BaseAgent, AgentResult, AgentContext } from '../core/BaseAgent';
export declare enum WorkflowPhase {
    PLAN = "plan",
    BUSINESS_LOGIC = "business_logic",
    GENERATE = "generate",
    DATABASE = "database",
    TESTING = "testing",
    DEPLOY = "deploy"
}
export interface WorkflowRequest {
    description: string;
    requirements: string[];
    context: AgentContext;
}
export interface WorkflowResult {
    success: boolean;
    phases: {
        [key in WorkflowPhase]: {
            completed: boolean;
            result?: AgentResult;
            error?: string;
            attempted?: boolean;
        };
    };
    finalOutput?: any;
    metadata?: Record<string, any>;
}
export interface QualityGate {
    phase: WorkflowPhase;
    validate(result: AgentResult): Promise<boolean>;
    getErrorMessage(): string;
}
export interface ValidationResult {
    success: boolean;
    issues: string[];
    warnings: string[];
    score: number;
    metadata?: Record<string, any>;
}
export interface EnhancedTaskParameters {
    requirements: string[];
    previousResult?: any;
    businessLogic?: any;
    technicalSpec?: any;
    sessionId: string;
    allPhaseResults: Map<WorkflowPhase, AgentResult>;
    crossPhaseContext?: Record<string, any>;
    validationResults?: ValidationResult[];
}
export declare class Orchestrator {
    private static instance;
    private agents;
    private qualityGates;
    private logger;
    private feedbackManager;
    private coordinatedFixingStrategy;
    private businessLogicValidator;
    private enhancedOrchestrator;
    private useEnhancedMode;
    private sessionPhaseResults;
    private crossPhaseValidators;
    private performanceManager;
    private parallelProcessingManager;
    private enableParallelProcessing;
    private advancedQualityGates;
    private enableAdvancedQualityGates;
    private selfHealingManager;
    private errorRecoveryWorkflow;
    private enableZeroManualIntervention;
    private autoFixSuccessRateTarget;
    private constructor();
    static getInstance(): Orchestrator;
    private initializeQualityGates;
    registerAgent(agent: BaseAgent): void;
    getRegisteredAgents(): string[];
    addQualityGate(gate: QualityGate): void;
    executeWorkflow(request: WorkflowRequest): Promise<WorkflowResult>;
    /**
     * Enhanced workflow execution with feedback loops and retry logic
     */
    private executeEnhancedWorkflow;
    private executePhase;
    private findAgentForTask;
    private runQualityGates;
    /**
     * Execute phase with business logic integration validation
     */
    private executePhaseWithIntegrationValidation;
    /**
     * Execute phase with coordinated fixing strategy
     */
    private executePhaseWithCoordination;
    /**
     * Retry workflow from a specific phase
     */
    private retryFromPhase;
    /**
     * Validate pre-phase conditions to prevent known issues
     */
    private validatePrePhaseConditions;
    /**
     * Validate post-phase quality and provide feedback for improvements
     */
    private validatePostPhaseQuality;
    /**
     * Build enhanced parameters with comprehensive data flow
     */
    private buildEnhancedParameters;
    /**
     * Get phase results for a session
     */
    private getPhaseResults;
    /**
     * Store phase result
     */
    private storePhaseResult;
    /**
     * Build cross-phase context for enhanced coordination
     */
    private buildCrossPhaseContext;
    /**
     * Perform cross-phase validation
     */
    private performCrossPhaseValidation;
    /**
     * Get phase-specific parameters based on dependencies
     */
    private getPhaseSpecificParameters;
    /**
     * Validate API contracts between business logic and generated code
     */
    private validateAPIContracts;
    /**
     * Validate database schema consistency
     */
    private validateDatabaseConsistency;
    /**
     * Validate frontend-backend alignment
     */
    private validateFrontendBackendAlignment;
    /**
     * Execute workflow with parallel processing optimization
     */
    executeWorkflowWithParallelOptimization(request: WorkflowRequest): Promise<AgentResult>;
    /**
     * Create parallel tasks from workflow request
     */
    private createParallelTasks;
    /**
     * Get phase dependencies
     */
    private getPhaseDependencies;
    /**
     * Get estimated duration for phase
     */
    private getPhaseEstimatedDuration;
    /**
     * Get phase priority
     */
    private getPhasePriority;
    /**
     * Enable or disable parallel processing
     */
    setParallelProcessingEnabled(enabled: boolean): void;
    /**
     * Enable or disable advanced quality gates
     */
    setAdvancedQualityGatesEnabled(enabled: boolean): void;
    /**
     * Enable or disable zero manual intervention
     */
    setZeroManualInterventionEnabled(enabled: boolean): void;
    /**
     * Set auto-fix success rate target
     */
    setAutoFixSuccessRateTarget(target: number): void;
    /**
     * Get previous phase for rollback purposes
     */
    private getPreviousPhase;
    /**
     * Get performance and parallel processing statistics
     */
    getPerformanceStats(sessionId?: string): any;
    /**
     * Get production readiness assessment
     */
    getProductionReadinessAssessment(sessionId: string): any;
    /**
     * Get comprehensive zero manual intervention assessment
     */
    getZeroManualInterventionAssessment(sessionId: string): any;
    /**
     * Calculate zero intervention confidence level
     */
    private calculateZeroInterventionConfidence;
    /**
     * Generate next steps for zero intervention readiness
     */
    private generateZeroInterventionNextSteps;
}
//# sourceMappingURL=Orchestrator.d.ts.map
import { WorkflowPhase } from './Orchestrator';
export interface StateVersion {
    version: number;
    timestamp: number;
    agentId: string;
    data: any;
    checksum: string;
}
export interface StateSubscriber {
    agentId: string;
    phase: WorkflowPhase;
    callback: (key: string, newValue: any, version: StateVersion) => Promise<void>;
}
export interface StateConflict {
    key: string;
    conflictingVersions: StateVersion[];
    resolution: 'latest' | 'merge' | 'manual';
    resolvedValue: any;
}
export interface SessionState {
    sessionId: string;
    state: Map<string, StateVersion>;
    subscribers: Map<string, Set<StateSubscriber>>;
    conflicts: StateConflict[];
    lastAccessed: number;
    locked: boolean;
}
export interface StateMetrics {
    totalSessions: number;
    totalStateKeys: number;
    conflictsResolved: number;
    subscriptionEvents: number;
    memoryUsage: number;
}
export declare class SharedStateManager {
    private sessions;
    private logger;
    private metrics;
    private cleanupInterval;
    constructor();
    /**
     * Initialize a new session
     */
    initializeSession(sessionId: string): Promise<void>;
    /**
     * Update shared state with conflict detection and resolution
     */
    updateSharedState(key: string, value: any, sessionId: string, agentId?: string): Promise<void>;
    /**
     * Get shared state value
     */
    getSharedState(sessionId: string, key?: string): any;
    /**
     * Get session state for orchestrator
     */
    getSessionState(sessionId: string): Map<string, any>;
    /**
     * Subscribe to state changes
     */
    subscribeToState(sessionId: string, key: string, subscriber: StateSubscriber): Promise<void>;
    /**
     * Unsubscribe from state changes
     */
    unsubscribeFromState(sessionId: string, key: string, agentId: string): Promise<void>;
    /**
     * Lock session to prevent concurrent modifications
     */
    lockSession(sessionId: string): Promise<void>;
    /**
     * Unlock session
     */
    unlockSession(sessionId: string): Promise<void>;
    /**
     * Get state history for a key
     */
    getStateHistory(sessionId: string, key: string): StateVersion[];
    /**
     * Resolve conflicts between state versions
     */
    private resolveConflict;
    /**
     * Check if there's a conflict between versions
     */
    private hasConflict;
    /**
     * Notify subscribers of state changes
     */
    private notifySubscribers;
    /**
     * Get next version number for a key
     */
    private getNextVersion;
    /**
     * Calculate checksum for conflict detection
     */
    private calculateChecksum;
    /**
     * Deep clone data to prevent mutations
     */
    private deepClone;
    /**
     * Merge conflicting values (simple strategy)
     */
    private mergeValues;
    /**
     * Cleanup old sessions
     */
    private performCleanup;
    /**
     * Cleanup specific session
     */
    cleanupSession(sessionId: string): Promise<void>;
    /**
     * Get current metrics
     */
    getMetrics(): StateMetrics;
    /**
     * Get session conflicts
     */
    getSessionConflicts(sessionId: string): StateConflict[];
    /**
     * Clear all conflicts for a session
     */
    clearSessionConflicts(sessionId: string): void;
    /**
     * Destroy the state manager
     */
    destroy(): void;
}
//# sourceMappingURL=SharedStateManager.d.ts.map
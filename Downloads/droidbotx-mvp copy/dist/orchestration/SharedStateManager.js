"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedStateManager = void 0;
const Logger_1 = require("../core/Logger");
class SharedStateManager {
    constructor() {
        this.sessions = new Map();
        this.logger = Logger_1.Logger.getInstance();
        this.metrics = {
            totalSessions: 0,
            totalStateKeys: 0,
            conflictsResolved: 0,
            subscriptionEvents: 0,
            memoryUsage: 0
        };
        // Start cleanup process
        this.cleanupInterval = setInterval(() => {
            this.performCleanup();
        }, 30 * 60 * 1000); // Every 30 minutes
    }
    /**
     * Initialize a new session
     */
    async initializeSession(sessionId) {
        if (this.sessions.has(sessionId)) {
            this.logger.warn('Session already exists, reinitializing', { sessionId });
        }
        const sessionState = {
            sessionId,
            state: new Map(),
            subscribers: new Map(),
            conflicts: [],
            lastAccessed: Date.now(),
            locked: false
        };
        this.sessions.set(sessionId, sessionState);
        this.metrics.totalSessions++;
        this.logger.info('Session initialized', { sessionId });
    }
    /**
     * Update shared state with conflict detection and resolution
     */
    async updateSharedState(key, value, sessionId, agentId = 'unknown') {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Session not found: ${sessionId}`);
        }
        if (session.locked) {
            throw new Error(`Session is locked: ${sessionId}`);
        }
        session.lastAccessed = Date.now();
        // Create new version
        const newVersion = {
            version: this.getNextVersion(session, key),
            timestamp: Date.now(),
            agentId,
            data: this.deepClone(value),
            checksum: this.calculateChecksum(value)
        };
        // Check for conflicts
        const existingVersion = session.state.get(key);
        if (existingVersion && this.hasConflict(existingVersion, newVersion)) {
            await this.resolveConflict(session, key, existingVersion, newVersion);
        }
        else {
            session.state.set(key, newVersion);
            this.metrics.totalStateKeys++;
        }
        // Notify subscribers
        await this.notifySubscribers(session, key, newVersion);
        this.logger.debug('State updated', {
            sessionId,
            key,
            agentId,
            version: newVersion.version
        });
    }
    /**
     * Get shared state value
     */
    getSharedState(sessionId, key) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return null;
        }
        session.lastAccessed = Date.now();
        if (key) {
            const version = session.state.get(key);
            return version ? this.deepClone(version.data) : null;
        }
        // Return all state as a Map
        const allState = new Map();
        for (const [stateKey, version] of session.state.entries()) {
            allState.set(stateKey, this.deepClone(version.data));
        }
        return allState;
    }
    /**
     * Get session state for orchestrator
     */
    getSessionState(sessionId) {
        return this.getSharedState(sessionId) || new Map();
    }
    /**
     * Subscribe to state changes
     */
    async subscribeToState(sessionId, key, subscriber) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Session not found: ${sessionId}`);
        }
        if (!session.subscribers.has(key)) {
            session.subscribers.set(key, new Set());
        }
        session.subscribers.get(key).add(subscriber);
        this.logger.debug('State subscription added', {
            sessionId,
            key,
            agentId: subscriber.agentId,
            phase: subscriber.phase
        });
    }
    /**
     * Unsubscribe from state changes
     */
    async unsubscribeFromState(sessionId, key, agentId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return;
        }
        const subscribers = session.subscribers.get(key);
        if (subscribers) {
            for (const subscriber of subscribers) {
                if (subscriber.agentId === agentId) {
                    subscribers.delete(subscriber);
                    break;
                }
            }
        }
        this.logger.debug('State subscription removed', {
            sessionId,
            key,
            agentId
        });
    }
    /**
     * Lock session to prevent concurrent modifications
     */
    async lockSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (session) {
            session.locked = true;
            this.logger.debug('Session locked', { sessionId });
        }
    }
    /**
     * Unlock session
     */
    async unlockSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (session) {
            session.locked = false;
            this.logger.debug('Session unlocked', { sessionId });
        }
    }
    /**
     * Get state history for a key
     */
    getStateHistory(sessionId, key) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return [];
        }
        // For now, we only keep the latest version
        // In a full implementation, we'd maintain a history
        const currentVersion = session.state.get(key);
        return currentVersion ? [currentVersion] : [];
    }
    /**
     * Resolve conflicts between state versions
     */
    async resolveConflict(session, key, existingVersion, newVersion) {
        this.logger.warn('State conflict detected', {
            sessionId: session.sessionId,
            key,
            existingAgent: existingVersion.agentId,
            newAgent: newVersion.agentId
        });
        const conflict = {
            key,
            conflictingVersions: [existingVersion, newVersion],
            resolution: 'latest', // Default resolution strategy
            resolvedValue: newVersion.data
        };
        // Apply resolution strategy
        switch (conflict.resolution) {
            case 'latest':
                session.state.set(key, newVersion);
                break;
            case 'merge':
                const mergedValue = this.mergeValues(existingVersion.data, newVersion.data);
                const mergedVersion = {
                    ...newVersion,
                    data: mergedValue,
                    checksum: this.calculateChecksum(mergedValue)
                };
                session.state.set(key, mergedVersion);
                break;
            case 'manual':
                // For now, default to latest
                session.state.set(key, newVersion);
                break;
        }
        session.conflicts.push(conflict);
        this.metrics.conflictsResolved++;
    }
    /**
     * Check if there's a conflict between versions
     */
    hasConflict(existing, newVersion) {
        // Conflict if different agents modified within a short time window
        const timeDiff = newVersion.timestamp - existing.timestamp;
        const sameAgent = existing.agentId === newVersion.agentId;
        const quickSuccession = timeDiff < 5000; // 5 seconds
        return !sameAgent && quickSuccession && existing.checksum !== newVersion.checksum;
    }
    /**
     * Notify subscribers of state changes
     */
    async notifySubscribers(session, key, newVersion) {
        const subscribers = session.subscribers.get(key);
        if (!subscribers || subscribers.size === 0) {
            return;
        }
        const notifications = Array.from(subscribers).map(async (subscriber) => {
            try {
                await subscriber.callback(key, newVersion.data, newVersion);
                this.metrics.subscriptionEvents++;
            }
            catch (error) {
                this.logger.error('Subscriber notification failed', {
                    sessionId: session.sessionId,
                    key,
                    agentId: subscriber.agentId,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        });
        await Promise.allSettled(notifications);
    }
    /**
     * Get next version number for a key
     */
    getNextVersion(session, key) {
        const existing = session.state.get(key);
        return existing ? existing.version + 1 : 1;
    }
    /**
     * Calculate checksum for conflict detection
     */
    calculateChecksum(data) {
        const crypto = require('crypto');
        return crypto
            .createHash('md5')
            .update(JSON.stringify(data))
            .digest('hex');
    }
    /**
     * Deep clone data to prevent mutations
     */
    deepClone(data) {
        return JSON.parse(JSON.stringify(data));
    }
    /**
     * Merge conflicting values (simple strategy)
     */
    mergeValues(existing, newValue) {
        if (typeof existing === 'object' && typeof newValue === 'object') {
            return { ...existing, ...newValue };
        }
        return newValue; // Default to new value
    }
    /**
     * Cleanup old sessions
     */
    performCleanup() {
        const cutoff = Date.now() - (2 * 60 * 60 * 1000); // 2 hours
        let cleaned = 0;
        for (const [sessionId, session] of this.sessions.entries()) {
            if (session.lastAccessed < cutoff) {
                this.sessions.delete(sessionId);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            this.logger.info('Cleaned up old sessions', { cleaned });
            this.metrics.totalSessions -= cleaned;
        }
        // Update memory usage metric
        this.metrics.memoryUsage = this.sessions.size;
    }
    /**
     * Cleanup specific session
     */
    async cleanupSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (session) {
            // Clear all subscribers
            session.subscribers.clear();
            // Remove session
            this.sessions.delete(sessionId);
            this.metrics.totalSessions--;
            this.logger.info('Session cleaned up', { sessionId });
        }
    }
    /**
     * Get current metrics
     */
    getMetrics() {
        this.metrics.memoryUsage = this.sessions.size;
        return { ...this.metrics };
    }
    /**
     * Get session conflicts
     */
    getSessionConflicts(sessionId) {
        const session = this.sessions.get(sessionId);
        return session ? [...session.conflicts] : [];
    }
    /**
     * Clear all conflicts for a session
     */
    clearSessionConflicts(sessionId) {
        const session = this.sessions.get(sessionId);
        if (session) {
            session.conflicts = [];
        }
    }
    /**
     * Destroy the state manager
     */
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.sessions.clear();
    }
}
exports.SharedStateManager = SharedStateManager;
//# sourceMappingURL=SharedStateManager.js.map
{"version": 3, "file": "SharedStateManager.js", "sourceRoot": "", "sources": ["../../src/orchestration/SharedStateManager.ts"], "names": [], "mappings": ";;;AAAA,2CAAwC;AAyCxC,MAAa,kBAAkB;IAM7B;QALQ,aAAQ,GAA8B,IAAI,GAAG,EAAE,CAAC;QAMtD,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG;YACb,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,CAAC;YACjB,iBAAiB,EAAE,CAAC;YACpB,kBAAkB,EAAE,CAAC;YACrB,WAAW,EAAE,CAAC;SACf,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,mBAAmB;IACzC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QAC9C,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,YAAY,GAAiB;YACjC,SAAS;YACT,KAAK,EAAE,IAAI,GAAG,EAAE;YAChB,WAAW,EAAE,IAAI,GAAG,EAAE;YACtB,SAAS,EAAE,EAAE;YACb,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;YACxB,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAE7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB,CAC5B,GAAW,EACX,KAAU,EACV,SAAiB,EACjB,UAAkB,SAAS;QAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,qBAAqB;QACrB,MAAM,UAAU,GAAiB;YAC/B,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC;YAC1C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO;YACP,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;YAC3B,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SACxC,CAAC;QAEF,sBAAsB;QACtB,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,eAAe,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE,CAAC;YACrE,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC;QAED,qBAAqB;QACrB,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;QAEvD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE;YACjC,SAAS;YACT,GAAG;YACH,OAAO;YACP,OAAO,EAAE,UAAU,CAAC,OAAO;SAC5B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,SAAiB,EAAE,GAAY;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,IAAI,GAAG,EAAE,CAAC;YACR,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACvC,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvD,CAAC;QAED,4BAA4B;QAC5B,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1D,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,SAAiB;QACtC,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB,CAC3B,SAAiB,EACjB,GAAW,EACX,UAA2B;QAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YAC5C,SAAS;YACT,GAAG;YACH,OAAO,EAAE,UAAU,CAAC,OAAO;YAC3B,KAAK,EAAE,UAAU,CAAC,KAAK;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAC/B,SAAiB,EACjB,GAAW,EACX,OAAe;QAEf,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,WAAW,EAAE,CAAC;YAChB,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;gBACrC,IAAI,UAAU,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;oBACnC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBAC/B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;YAC9C,SAAS;YACT,GAAG;YACH,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,SAAiB;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CAAC,SAAiB;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,SAAiB,EAAE,GAAW;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,2CAA2C;QAC3C,oDAAoD;QACpD,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9C,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,OAAqB,EACrB,GAAW,EACX,eAA6B,EAC7B,UAAwB;QAExB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;YAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,GAAG;YACH,aAAa,EAAE,eAAe,CAAC,OAAO;YACtC,QAAQ,EAAE,UAAU,CAAC,OAAO;SAC7B,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAkB;YAC9B,GAAG;YACH,mBAAmB,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC;YAClD,UAAU,EAAE,QAAQ,EAAE,8BAA8B;YACpD,aAAa,EAAE,UAAU,CAAC,IAAI;SAC/B,CAAC;QAEF,4BAA4B;QAC5B,QAAQ,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC5B,KAAK,QAAQ;gBACX,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;gBACnC,MAAM;YACR,KAAK,OAAO;gBACV,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC5E,MAAM,aAAa,GAAiB;oBAClC,GAAG,UAAU;oBACb,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;iBAC9C,CAAC;gBACF,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,QAAQ;gBACX,6BAA6B;gBAC7B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;gBACnC,MAAM;QACV,CAAC;QAED,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAsB,EAAE,UAAwB;QAClE,mEAAmE;QACnE,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QAC3D,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,CAAC;QAC1D,MAAM,eAAe,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC,YAAY;QAErD,OAAO,CAAC,SAAS,IAAI,eAAe,IAAI,QAAQ,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,OAAqB,EACrB,GAAW,EACX,UAAwB;QAExB,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO;QACT,CAAC;QAED,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YACrE,IAAI,CAAC;gBACH,MAAM,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACpC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBAClD,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,GAAG;oBACH,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAqB,EAAE,GAAW;QACvD,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxC,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAS;QACjC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjC,OAAO,MAAM;aACV,UAAU,CAAC,KAAK,CAAC;aACjB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aAC5B,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,IAAS;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAa,EAAE,QAAa;QAC9C,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjE,OAAO,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,CAAC;QACtC,CAAC;QACD,OAAO,QAAQ,CAAC,CAAC,uBAAuB;IAC1C,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,UAAU;QAC5D,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,OAAO,CAAC,YAAY,GAAG,MAAM,EAAE,CAAC;gBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAChC,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC;QACxC,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CAAC,SAAiB;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,wBAAwB;YACxB,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAE5B,iBAAiB;YACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAE7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,UAAU;QACf,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC9C,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,SAAiB;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,SAAiB;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;CACF;AAnaD,gDAmaC"}
import { GeneratedCode } from '../agents/CodingAgent';
import { TechnicalSpecification } from '../agents/PlanningAgent';
import { BusinessLogicResult } from '../agents/BusinessLogicAgent';
export interface StreamingMetrics {
    totalFiles: number;
    filesGenerated: number;
    memoryPeakUsage: number;
    streamingTime: number;
    compressionRatio: number;
}
export interface FileGenerationTask {
    filePath: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    dependencies: string[];
    generator: () => Promise<string>;
    size?: number;
}
export interface CodeGenerationStream {
    projectPath: string;
    files: Map<string, string>;
    metadata: any;
    metrics: StreamingMetrics;
}
export declare class StreamingCodeGenerator {
    private logger;
    private activeStreams;
    private memoryThreshold;
    private batchSize;
    constructor();
    /**
     * Generate code using streaming approach to reduce memory usage
     */
    generateCodeStreaming(spec: TechnicalSpecification, businessLogic: BusinessLogicResult, sessionId: string): Promise<GeneratedCode>;
    /**
     * Generate prioritized file tasks
     */
    private generateFileTasks;
    /**
     * Sort tasks by priority and resolve dependencies
     */
    private sortTasksByPriorityAndDependencies;
    /**
     * Process tasks in memory-efficient batches
     */
    private processTasksInBatches;
    /**
     * Process individual file task
     */
    private processFileTask;
    /**
     * Estimate current memory usage
     */
    private estimateMemoryUsage;
    /**
     * Flush files to disk and clear memory
     */
    private flushToDisk;
    /**
     * Finalize generation and return result
     */
    private finalizeGeneration;
    private getAllFilesRecursively;
    private extractPackageJsons;
    private generatePackageJson;
    private generateBackendPackageJson;
    private generateFrontendPackageJson;
    private generateServerFile;
    private generateDatabaseInit;
    private generateAppComponent;
    private generateIndexFile;
    private generateComponentFile;
    private generateReadme;
    private generateDockerCompose;
    private generateEnvExample;
    private generateScripts;
    /**
     * Get streaming metrics
     */
    getMetrics(sessionId: string): StreamingMetrics | null;
}
//# sourceMappingURL=StreamingCodeGenerator.d.ts.map
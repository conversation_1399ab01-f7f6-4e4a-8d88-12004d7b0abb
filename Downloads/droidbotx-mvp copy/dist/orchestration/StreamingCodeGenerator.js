"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamingCodeGenerator = void 0;
const Logger_1 = require("../core/Logger");
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
class StreamingCodeGenerator {
    constructor() {
        this.activeStreams = new Map();
        this.memoryThreshold = 50 * 1024 * 1024; // 50MB
        this.batchSize = 10;
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Generate code using streaming approach to reduce memory usage
     */
    async generateCodeStreaming(spec, businessLogic, sessionId) {
        const startTime = Date.now();
        const projectPath = `/tmp/generated-${sessionId}`;
        this.logger.info('Starting streaming code generation', {
            sessionId,
            projectPath,
            memoryThreshold: this.memoryThreshold
        });
        // Initialize stream
        const stream = {
            projectPath,
            files: new Map(),
            metadata: {
                sessionId,
                startTime,
                spec,
                businessLogic
            },
            metrics: {
                totalFiles: 0,
                filesGenerated: 0,
                memoryPeakUsage: 0,
                streamingTime: 0,
                compressionRatio: 0
            }
        };
        this.activeStreams.set(sessionId, stream);
        try {
            // Generate file tasks with priorities and dependencies
            const fileTasks = await this.generateFileTasks(spec, businessLogic);
            stream.metrics.totalFiles = fileTasks.length;
            // Sort tasks by priority and dependencies
            const sortedTasks = this.sortTasksByPriorityAndDependencies(fileTasks);
            // Process tasks in streaming batches
            await this.processTasksInBatches(stream, sortedTasks);
            // Finalize generation
            const result = await this.finalizeGeneration(stream);
            stream.metrics.streamingTime = Date.now() - startTime;
            this.logger.info('Streaming code generation completed', {
                sessionId,
                filesGenerated: stream.metrics.filesGenerated,
                duration: stream.metrics.streamingTime,
                memoryPeak: stream.metrics.memoryPeakUsage,
                compressionRatio: stream.metrics.compressionRatio
            });
            return result;
        }
        catch (error) {
            this.logger.error('Streaming code generation failed', {
                sessionId,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
        finally {
            this.activeStreams.delete(sessionId);
        }
    }
    /**
     * Generate prioritized file tasks
     */
    async generateFileTasks(spec, businessLogic) {
        const tasks = [];
        // Critical files (needed for basic structure)
        tasks.push({
            filePath: 'package.json',
            priority: 'critical',
            dependencies: [],
            generator: () => this.generatePackageJson(spec)
        });
        tasks.push({
            filePath: 'backend/package.json',
            priority: 'critical',
            dependencies: ['package.json'],
            generator: () => this.generateBackendPackageJson(spec)
        });
        tasks.push({
            filePath: 'frontend/package.json',
            priority: 'critical',
            dependencies: ['package.json'],
            generator: () => this.generateFrontendPackageJson(spec)
        });
        // High priority files (core functionality)
        tasks.push({
            filePath: 'backend/src/server.ts',
            priority: 'high',
            dependencies: ['backend/package.json'],
            generator: () => this.generateServerFile(spec, businessLogic)
        });
        tasks.push({
            filePath: 'backend/init.sql',
            priority: 'high',
            dependencies: [],
            generator: () => this.generateDatabaseInit(businessLogic.databaseSchema)
        });
        // Generate API route files
        if (businessLogic.domainAPIs) {
            for (const [apiPath, apiContent] of Object.entries(businessLogic.domainAPIs)) {
                tasks.push({
                    filePath: apiPath,
                    priority: 'high',
                    dependencies: ['backend/src/server.ts'],
                    generator: () => Promise.resolve(apiContent)
                });
            }
        }
        // Medium priority files (frontend components)
        tasks.push({
            filePath: 'frontend/src/App.tsx',
            priority: 'medium',
            dependencies: ['frontend/package.json'],
            generator: () => this.generateAppComponent(businessLogic.applicationFlow)
        });
        tasks.push({
            filePath: 'frontend/src/index.tsx',
            priority: 'medium',
            dependencies: ['frontend/src/App.tsx'],
            generator: () => this.generateIndexFile()
        });
        // Generate component files
        if (businessLogic.applicationFlow?.routes) {
            for (const route of businessLogic.applicationFlow.routes) {
                tasks.push({
                    filePath: `frontend/src/components/${route.component}.tsx`,
                    priority: 'medium',
                    dependencies: ['frontend/src/App.tsx'],
                    generator: () => this.generateComponentFile(route)
                });
            }
        }
        // Low priority files (configuration, documentation)
        tasks.push({
            filePath: 'README.md',
            priority: 'low',
            dependencies: [],
            generator: () => this.generateReadme(spec)
        });
        tasks.push({
            filePath: 'docker-compose.yml',
            priority: 'low',
            dependencies: [],
            generator: () => this.generateDockerCompose(spec)
        });
        tasks.push({
            filePath: '.env.example',
            priority: 'low',
            dependencies: [],
            generator: () => this.generateEnvExample(spec)
        });
        return tasks;
    }
    /**
     * Sort tasks by priority and resolve dependencies
     */
    sortTasksByPriorityAndDependencies(tasks) {
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        // First sort by priority
        tasks.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
        // Then resolve dependencies within each priority group
        const resolved = [];
        const remaining = [...tasks];
        while (remaining.length > 0) {
            const canProcess = remaining.filter(task => task.dependencies.every(dep => resolved.some(r => r.filePath === dep)));
            if (canProcess.length === 0) {
                // Break dependency cycle by processing first remaining task
                resolved.push(remaining.shift());
            }
            else {
                // Process tasks with satisfied dependencies
                canProcess.forEach(task => {
                    resolved.push(task);
                    const index = remaining.indexOf(task);
                    remaining.splice(index, 1);
                });
            }
        }
        return resolved;
    }
    /**
     * Process tasks in memory-efficient batches
     */
    async processTasksInBatches(stream, tasks) {
        for (let i = 0; i < tasks.length; i += this.batchSize) {
            const batch = tasks.slice(i, i + this.batchSize);
            this.logger.debug(`Processing batch ${Math.floor(i / this.batchSize) + 1}`, {
                batchSize: batch.length,
                totalBatches: Math.ceil(tasks.length / this.batchSize)
            });
            // Process batch in parallel
            const batchPromises = batch.map(task => this.processFileTask(stream, task));
            await Promise.all(batchPromises);
            // Check memory usage and flush if needed
            const memoryUsage = this.estimateMemoryUsage(stream);
            stream.metrics.memoryPeakUsage = Math.max(stream.metrics.memoryPeakUsage, memoryUsage);
            if (memoryUsage > this.memoryThreshold) {
                await this.flushToDisk(stream);
            }
        }
        // Final flush
        await this.flushToDisk(stream);
    }
    /**
     * Process individual file task
     */
    async processFileTask(stream, task) {
        try {
            const content = await task.generator();
            stream.files.set(task.filePath, content);
            stream.metrics.filesGenerated++;
            this.logger.debug(`Generated file: ${task.filePath}`, {
                size: content.length,
                priority: task.priority
            });
        }
        catch (error) {
            this.logger.error(`Failed to generate file: ${task.filePath}`, {
                error: error instanceof Error ? error.message : String(error),
                priority: task.priority
            });
            // Continue with other files
        }
    }
    /**
     * Estimate current memory usage
     */
    estimateMemoryUsage(stream) {
        let totalSize = 0;
        for (const content of stream.files.values()) {
            totalSize += Buffer.byteLength(content, 'utf8');
        }
        return totalSize;
    }
    /**
     * Flush files to disk and clear memory
     */
    async flushToDisk(stream) {
        if (stream.files.size === 0)
            return;
        this.logger.debug('Flushing files to disk', {
            fileCount: stream.files.size,
            projectPath: stream.projectPath
        });
        // Ensure project directory exists
        await fs.mkdir(stream.projectPath, { recursive: true });
        // Write files to disk
        const writePromises = Array.from(stream.files.entries()).map(async ([filePath, content]) => {
            const fullPath = path.join(stream.projectPath, filePath);
            const dir = path.dirname(fullPath);
            await fs.mkdir(dir, { recursive: true });
            await fs.writeFile(fullPath, content, 'utf8');
        });
        await Promise.all(writePromises);
        // Clear memory
        stream.files.clear();
    }
    /**
     * Finalize generation and return result
     */
    async finalizeGeneration(stream) {
        // Read all generated files from disk
        const files = {};
        try {
            const allFiles = await this.getAllFilesRecursively(stream.projectPath);
            for (const filePath of allFiles) {
                const relativePath = path.relative(stream.projectPath, filePath);
                const content = await fs.readFile(filePath, 'utf8');
                files[relativePath] = content;
            }
            // Calculate compression ratio
            const totalOriginalSize = Object.values(files).reduce((sum, content) => sum + content.length, 0);
            const totalCompressedSize = stream.metrics.memoryPeakUsage;
            stream.metrics.compressionRatio = totalOriginalSize > 0 ? totalCompressedSize / totalOriginalSize : 1;
        }
        catch (error) {
            this.logger.error('Failed to read generated files', {
                error: error instanceof Error ? error.message : String(error),
                projectPath: stream.projectPath
            });
        }
        return {
            projectPath: stream.projectPath,
            files,
            packageJsons: this.extractPackageJsons(files),
            scripts: this.generateScripts(stream.metadata.spec),
            documentation: files['README.md'] || 'No documentation generated'
        };
    }
    async getAllFilesRecursively(dir) {
        const files = [];
        try {
            const entries = await fs.readdir(dir, { withFileTypes: true });
            for (const entry of entries) {
                const fullPath = path.join(dir, entry.name);
                if (entry.isDirectory()) {
                    const subFiles = await this.getAllFilesRecursively(fullPath);
                    files.push(...subFiles);
                }
                else {
                    files.push(fullPath);
                }
            }
        }
        catch (error) {
            this.logger.warn(`Failed to read directory: ${dir}`, {
                error: error instanceof Error ? error.message : String(error)
            });
        }
        return files;
    }
    extractPackageJsons(files) {
        const packageJsons = {};
        for (const [filePath, content] of Object.entries(files)) {
            if (filePath.endsWith('package.json')) {
                try {
                    packageJsons[filePath] = JSON.parse(content);
                }
                catch (error) {
                    this.logger.warn(`Failed to parse package.json: ${filePath}`, {
                        error: error instanceof Error ? error.message : String(error)
                    });
                }
            }
        }
        return packageJsons;
    }
    // File generation methods (simplified for brevity)
    async generatePackageJson(spec) {
        return JSON.stringify({
            name: spec.projectName || 'generated-app',
            version: '1.0.0',
            description: spec.description || 'Generated application',
            scripts: {
                "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
                "dev:backend": "cd backend && npm run dev",
                "dev:frontend": "cd frontend && npm start",
                "build": "npm run build:backend && npm run build:frontend",
                "build:backend": "cd backend && npm run build",
                "build:frontend": "cd frontend && npm run build"
            },
            devDependencies: {
                "concurrently": "^7.6.0"
            }
        }, null, 2);
    }
    async generateBackendPackageJson(spec) {
        return JSON.stringify({
            name: `${spec.projectName || 'generated-app'}-backend`,
            version: '1.0.0',
            main: "dist/server.js",
            scripts: {
                "dev": "ts-node-dev --respawn --transpile-only src/server.ts",
                "build": "tsc",
                "start": "node dist/server.js"
            },
            dependencies: {
                "express": "^4.18.2",
                "cors": "^2.8.5",
                "helmet": "^6.0.1",
                "jsonwebtoken": "^9.0.0",
                "bcryptjs": "^2.4.3",
                "pg": "^8.8.0",
                "dotenv": "^16.0.3"
            },
            devDependencies: {
                "@types/express": "^4.17.17",
                "@types/cors": "^2.8.13",
                "@types/jsonwebtoken": "^9.0.1",
                "@types/bcryptjs": "^2.4.2",
                "@types/pg": "^8.6.6",
                "@types/node": "^18.14.2",
                "typescript": "^4.9.5",
                "ts-node-dev": "^2.0.0"
            }
        }, null, 2);
    }
    async generateFrontendPackageJson(spec) {
        return JSON.stringify({
            name: `${spec.projectName || 'generated-app'}-frontend`,
            version: '1.0.0',
            dependencies: {
                "react": "^18.2.0",
                "react-dom": "^18.2.0",
                "react-router-dom": "^6.8.1",
                "axios": "^1.3.4",
                "tailwindcss": "^3.2.7"
            },
            devDependencies: {
                "@types/react": "^18.0.28",
                "@types/react-dom": "^18.0.11",
                "typescript": "^4.9.5",
                "react-scripts": "5.0.1"
            },
            scripts: {
                "start": "react-scripts start",
                "build": "react-scripts build",
                "test": "react-scripts test",
                "eject": "react-scripts eject"
            }
        }, null, 2);
    }
    // Additional generation methods would be implemented here...
    async generateServerFile(spec, businessLogic) {
        return `// Generated Express server
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';

const app = express();
const PORT = process.env.PORT || 5000;

app.use(helmet());
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});

export default app;
`;
    }
    async generateDatabaseInit(schema) {
        return `-- Generated database initialization
-- This file contains the database schema and initial data

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Generated tables will be added here
`;
    }
    async generateAppComponent(applicationFlow) {
        return `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

function App() {
  return (
    <Router>
      <div className="App">
        <header className="bg-blue-600 text-white p-4">
          <h1 className="text-2xl font-bold">Generated Application</h1>
        </header>
        <main className="container mx-auto p-4">
          <Routes>
            <Route path="/" element={<div>Welcome to your generated app!</div>} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
`;
    }
    async generateIndexFile() {
        return `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
`;
    }
    async generateComponentFile(route) {
        return `import React from 'react';

interface ${route.component}Props {}

const ${route.component}: React.FC<${route.component}Props> = () => {
  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">${route.component}</h2>
      <p>This is the ${route.component} component.</p>
    </div>
  );
};

export default ${route.component};
`;
    }
    async generateReadme(spec) {
        return `# ${spec.projectName || 'Generated Application'}

${spec.description || 'This is a generated full-stack application.'}

## Getting Started

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Start the development servers:
   \`\`\`bash
   npm run dev
   \`\`\`

3. Open your browser to http://localhost:3000

## Project Structure

- \`frontend/\` - React frontend application
- \`backend/\` - Express backend API
- \`docker-compose.yml\` - Docker configuration

## Features

${spec.features?.map(f => `- ${f}`).join('\n') || '- Generated with DroidBotX'}
`;
    }
    async generateDockerCompose(spec) {
        return `version: '3.8'

services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5000
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=****************************************/${spec.projectName || 'app'}_db
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=${spec.projectName || 'app'}_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
`;
    }
    async generateEnvExample(spec) {
        return `# Environment Configuration
NODE_ENV=development
PORT=5000

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/${spec.projectName || 'app'}_db

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Frontend
REACT_APP_API_URL=http://localhost:5000
`;
    }
    generateScripts(spec) {
        return {
            'setup.sh': `#!/bin/bash
echo "Setting up ${spec.projectName || 'generated application'}..."
npm install
cd backend && npm install
cd ../frontend && npm install
echo "Setup complete!"
`,
            'start.sh': `#!/bin/bash
echo "Starting ${spec.projectName || 'generated application'}..."
npm run dev
`
        };
    }
    /**
     * Get streaming metrics
     */
    getMetrics(sessionId) {
        const stream = this.activeStreams.get(sessionId);
        return stream ? { ...stream.metrics } : null;
    }
}
exports.StreamingCodeGenerator = StreamingCodeGenerator;
//# sourceMappingURL=StreamingCodeGenerator.js.map
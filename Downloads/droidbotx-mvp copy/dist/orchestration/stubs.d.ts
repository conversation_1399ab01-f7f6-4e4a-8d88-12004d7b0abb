import { AgentResult } from '../core/BaseAgent';
export declare class StreamingCodeGenerator {
    private logger;
    constructor();
    generateCodeStreaming(): Promise<any>;
}
export declare class ContractFirstCoordination {
    private logger;
    constructor();
    getContracts(sessionId: string): Promise<any>;
}
export declare class SharedStateManager {
    private logger;
    private state;
    constructor();
    initializeSession(sessionId: string): Promise<void>;
    updateSharedState(key: string, value: any, sessionId: string): Promise<void>;
    getSessionState(sessionId: string): Map<string, any>;
    cleanupSession(sessionId: string): Promise<void>;
}
export declare class DependencyValidator {
    private logger;
    constructor();
    validateDependencies(phase: any, previousResult: any, sharedState: any): Promise<{
        success: boolean;
        issues: string[];
        warnings: string[];
        missingDependencies: string[];
        score: number;
    }>;
}
export declare class ProactiveQualitySystem {
    private logger;
    constructor();
    generateQualityPlan(phase: any, request: any): Promise<any>;
    validateResult(phase: any, result: any, qualityPlan: any): Promise<{
        success: boolean;
        issues: any[];
        recommendations: string[];
    }>;
    applyQualityCorrections(result: AgentResult, validation: any): Promise<{
        success: boolean;
        data: any;
        correctionsApplied: string[];
        remainingIssues: any[];
    }>;
}
export declare class ComprehensiveCrossLayerValidator {
    private logger;
    constructor();
    validateFullStackIntegration(context: any): Promise<{
        success: boolean;
        score: number;
        validations: any[];
        criticalIssues: any[];
        recommendations: string[];
    }>;
}
export declare class IntelligentErrorRecovery {
    private logger;
    constructor();
    handlePhaseError(phase: any, error: Error, context: any): Promise<{
        success: boolean;
        result?: AgentResult;
        strategy: string;
        timeToRecover: number;
    }>;
}
export declare class ParallelLLMManager {
    private logger;
    constructor();
}
export declare class OptimizedFileManager {
    private logger;
    constructor();
}
//# sourceMappingURL=stubs.d.ts.map
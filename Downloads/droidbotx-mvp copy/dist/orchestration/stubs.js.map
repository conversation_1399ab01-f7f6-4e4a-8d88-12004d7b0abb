{"version": 3, "file": "stubs.js", "sourceRoot": "", "sources": ["../../src/orchestration/stubs.ts"], "names": [], "mappings": ";AAAA,8CAA8C;;;AAE9C,2CAAwC;AAGxC,oCAAoC;AACpC,MAAa,sBAAsB;IAGjC;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,qBAAqB;QAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAC7E,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;IACrC,CAAC;CACF;AAXD,wDAWC;AAED,uCAAuC;AACvC,MAAa,yBAAyB;IAGpC;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,SAAiB;QACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAChF,OAAO,EAAE,YAAY,EAAE,EAAE,EAAE,iBAAiB,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE,EAAE,CAAC;IAC7E,CAAC;CACF;AAXD,8DAWC;AAED,gCAAgC;AAChC,MAAa,kBAAkB;IAI7B;QAFQ,UAAK,GAAqB,IAAI,GAAG,EAAE,CAAC;QAG1C,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,SAAS,EAAE,CAAC,CAAC;IAC5E,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,GAAW,EAAE,KAAU,EAAE,SAAiB;QACvE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,SAAS,IAAI,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAEM,eAAe,CAAC,SAAiB;QACtC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAChD,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,SAAiB;QAC3C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;YACpC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAjCD,gDAiCC;AAED,iCAAiC;AACjC,MAAa,mBAAmB;IAG9B;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,KAAU,EAAE,cAAmB,EAAE,WAAgB;QACjF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,mBAAmB,EAAE,EAAE;YACvB,KAAK,EAAE,EAAE;SACV,CAAC;IACJ,CAAC;CACF;AAjBD,kDAiBC;AAED,oCAAoC;AACpC,MAAa,sBAAsB;IAGjC;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,KAAU,EAAE,OAAY;QACvD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAC7E,OAAO;YACL,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,EAAE;YACpB,YAAY,EAAE,EAAE;YAChB,gBAAgB,EAAE,EAAE;SACrB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,KAAU,EAAE,MAAW,EAAE,WAAgB;QACnE,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,EAAE;YACV,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAAC,MAAmB,EAAE,UAAe;QACvE,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;CACF;AAjCD,wDAiCC;AAED,8CAA8C;AAC9C,MAAa,gCAAgC;IAG3C;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,4BAA4B,CAAC,OAAY;QACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mEAAmE,CAAC,CAAC;QACvF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,EAAE;YACf,cAAc,EAAE,EAAE;YAClB,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;CACF;AAjBD,4EAiBC;AAED,sCAAsC;AACtC,MAAa,wBAAwB;IAGnC;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,KAAU,EAAE,KAAY,EAAE,OAAY;QAClE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;QAC/E,OAAO;YACL,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,MAAM;YAChB,aAAa,EAAE,CAAC;SACjB,CAAC;IACJ,CAAC;CACF;AAfD,4DAeC;AAED,8EAA8E;AAE9E,gCAAgC;AAChC,MAAa,kBAAkB;IAG7B;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;CACF;AAND,gDAMC;AAED,kCAAkC;AAClC,MAAa,oBAAoB;IAG/B;QACE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;CACF;AAND,oDAMC"}
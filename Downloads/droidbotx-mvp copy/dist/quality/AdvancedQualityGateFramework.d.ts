import { WorkflowPhase } from '../orchestration/Orchestrator';
import { AgentResult } from '../core/BaseAgent';
export interface QualityMetric {
    name: string;
    weight: number;
    threshold: number;
    critical: boolean;
}
export interface QualityGateResult {
    phase: WorkflowPhase;
    overallScore: number;
    passed: boolean;
    critical: boolean;
    metrics: QualityMetricResult[];
    recommendations: string[];
    rollbackRequired: boolean;
    productionReadiness: ProductionReadinessScore;
}
export interface QualityMetricResult {
    metric: QualityMetric;
    score: number;
    passed: boolean;
    details: any;
    issues: string[];
    suggestions: string[];
}
export interface ProductionReadinessScore {
    overall: number;
    categories: {
        functionality: number;
        reliability: number;
        performance: number;
        security: number;
        maintainability: number;
        scalability: number;
    };
    blockers: string[];
    warnings: string[];
    readyForProduction: boolean;
}
export interface RollbackPoint {
    sessionId: string;
    phase: WorkflowPhase;
    timestamp: number;
    result: AgentResult;
    qualityScore: number;
    snapshot: any;
}
export declare class AdvancedQualityGateFramework {
    private static instance;
    private logger;
    private qualityMetrics;
    private rollbackPoints;
    private productionThreshold;
    private criticalFailureThreshold;
    private constructor();
    static getInstance(): AdvancedQualityGateFramework;
    /**
     * Initialize quality metrics for each phase
     */
    private initializeQualityMetrics;
    /**
     * Evaluate quality gates for a phase
     */
    evaluateQualityGates(phase: WorkflowPhase, result: AgentResult, sessionId: string): Promise<QualityGateResult>;
    /**
     * Evaluate individual metric
     */
    private evaluateMetric;
    /**
     * Evaluate requirements completeness
     */
    private evaluateRequirementsCompleteness;
    /**
     * Evaluate technical feasibility
     */
    private evaluateTechnicalFeasibility;
    /**
     * Evaluate API design quality
     */
    private evaluateAPIDesignQuality;
    /**
     * Evaluate database schema integrity
     */
    private evaluateDatabaseSchemaIntegrity;
    /**
     * Evaluate code compilation
     */
    private evaluateCodeCompilation;
    /**
     * Evaluate API implementation
     */
    private evaluateAPIImplementation;
    /**
     * Evaluate test coverage
     */
    private evaluateTestCoverage;
    /**
     * Evaluate test pass rate
     */
    private evaluateTestPassRate;
    /**
     * Evaluate deployment success
     */
    private evaluateDeploymentSuccess;
    /**
     * Evaluate security implementation
     */
    private evaluateSecurityImplementation;
    /**
     * Generic metric evaluation
     */
    private evaluateGenericMetric;
    /**
     * Generate recommendations based on metric results
     */
    private generateRecommendations;
    /**
     * Calculate production readiness score
     */
    private calculateProductionReadiness;
    /**
     * Calculate category score based on relevant metrics
     */
    private calculateCategoryScore;
    /**
     * Create rollback point
     */
    private createRollbackPoint;
    /**
     * Get available rollback points
     */
    getRollbackPoints(sessionId: string): RollbackPoint[];
    /**
     * Execute rollback to a specific point
     */
    executeRollback(sessionId: string, targetPhase: WorkflowPhase): Promise<RollbackPoint | null>;
    /**
     * Set production readiness threshold
     */
    setProductionThreshold(threshold: number): void;
    /**
     * Get quality gate statistics
     */
    getQualityGateStats(sessionId?: string): any;
    /**
     * Clear session data
     */
    clearSessionData(sessionId: string): void;
}
//# sourceMappingURL=AdvancedQualityGateFramework.d.ts.map
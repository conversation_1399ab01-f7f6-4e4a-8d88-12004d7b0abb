"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedQualityGateFramework = void 0;
const Logger_1 = require("../core/Logger");
const Orchestrator_1 = require("../orchestration/Orchestrator");
class AdvancedQualityGateFramework {
    constructor() {
        this.qualityMetrics = new Map();
        this.rollbackPoints = new Map();
        this.productionThreshold = 80; // Realistic threshold for Phase 3 completion (80-85 initial target)
        this.criticalFailureThreshold = 60; // Realistic threshold to allow progression with fixes
        this.logger = Logger_1.Logger.getInstance();
        this.initializeQualityMetrics();
    }
    static getInstance() {
        if (!AdvancedQualityGateFramework.instance) {
            AdvancedQualityGateFramework.instance = new AdvancedQualityGateFramework();
        }
        return AdvancedQualityGateFramework.instance;
    }
    /**
     * Initialize quality metrics for each phase
     */
    initializeQualityMetrics() {
        // Planning Phase Metrics - Realistic thresholds for 80% success rate
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.PLAN, [
            { name: 'Requirements Completeness', weight: 0.25, threshold: 70, critical: false },
            { name: 'Technical Feasibility', weight: 0.2, threshold: 65, critical: false },
            { name: 'Architecture Clarity', weight: 0.2, threshold: 65, critical: false },
            { name: 'Technology Stack Appropriateness', weight: 0.15, threshold: 70, critical: false },
            { name: 'Blueprint Comprehensiveness', weight: 0.1, threshold: 75, critical: false },
            { name: 'Template Elimination Score', weight: 0.1, threshold: 80, critical: false }
        ]);
        // Business Logic Phase Metrics - Realistic thresholds for progression
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.BUSINESS_LOGIC, [
            { name: 'API Design Quality', weight: 0.25, threshold: 70, critical: false },
            { name: 'Database Schema Integrity', weight: 0.2, threshold: 65, critical: false },
            { name: 'Business Rules Completeness', weight: 0.2, threshold: 65, critical: false },
            { name: 'Data Flow Consistency', weight: 0.15, threshold: 60, critical: false },
            { name: 'Domain Model Accuracy', weight: 0.1, threshold: 60, critical: false },
            { name: 'API Count Adequacy', weight: 0.1, threshold: 70, critical: false }
        ]);
        // Code Generation Phase Metrics - Focus on compilation success
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.GENERATE, [
            { name: 'Code Compilation Success', weight: 0.4, threshold: 80, critical: true }, // Increased weight, lowered threshold
            { name: 'API Implementation Completeness', weight: 0.2, threshold: 70, critical: false },
            { name: 'Code Quality Standards', weight: 0.15, threshold: 60, critical: false },
            { name: 'Error Handling Coverage', weight: 0.1, threshold: 50, critical: false },
            { name: 'Documentation Quality', weight: 0.1, threshold: 50, critical: false },
            { name: 'Type Safety', weight: 0.05, threshold: 60, critical: false }
        ]);
        // UI/UX Phase Metrics
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.TESTING, [
            { name: 'Component Functionality', weight: 0.3, threshold: 85, critical: true },
            { name: 'Design Consistency', weight: 0.2, threshold: 75, critical: false },
            { name: 'Accessibility Compliance', weight: 0.2, threshold: 70, critical: false },
            { name: 'Responsive Design', weight: 0.15, threshold: 75, critical: false },
            { name: 'User Experience Flow', weight: 0.15, threshold: 70, critical: false }
        ]);
        // Security Phase Metrics
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.DATABASE, [
            { name: 'Authentication Implementation', weight: 0.25, threshold: 90, critical: true },
            { name: 'Authorization Controls', weight: 0.25, threshold: 85, critical: true },
            { name: 'Input Validation', weight: 0.2, threshold: 85, critical: true },
            { name: 'Data Encryption', weight: 0.15, threshold: 80, critical: false },
            { name: 'Security Headers', weight: 0.15, threshold: 75, critical: false }
        ]);
        // Database Phase Metrics
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.DATABASE, [
            { name: 'Schema Validation', weight: 0.3, threshold: 90, critical: true },
            { name: 'Migration Integrity', weight: 0.25, threshold: 85, critical: true },
            { name: 'Index Optimization', weight: 0.2, threshold: 75, critical: false },
            { name: 'Constraint Enforcement', weight: 0.15, threshold: 80, critical: false },
            { name: 'Backup Strategy', weight: 0.1, threshold: 70, critical: false }
        ]);
        // Testing Phase Metrics - Enhanced for >80% Test Success Rate Target
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.TESTING, [
            { name: 'Test Coverage', weight: 0.25, threshold: 85, critical: true },
            { name: 'Test Pass Rate', weight: 0.3, threshold: 85, critical: true },
            { name: 'Integration Test Quality', weight: 0.2, threshold: 85, critical: true },
            { name: 'Auto-fixing Success Rate', weight: 0.15, threshold: 80, critical: true },
            { name: 'Performance Test Results', weight: 0.05, threshold: 75, critical: false },
            { name: 'Edge Case Coverage', weight: 0.05, threshold: 70, critical: false }
        ]);
        // DevOps Phase Metrics
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.DEPLOY, [
            { name: 'Deployment Configuration', weight: 0.3, threshold: 85, critical: true },
            { name: 'CI/CD Pipeline Quality', weight: 0.25, threshold: 80, critical: false },
            { name: 'Environment Consistency', weight: 0.2, threshold: 75, critical: false },
            { name: 'Monitoring Setup', weight: 0.15, threshold: 70, critical: false },
            { name: 'Scaling Configuration', weight: 0.1, threshold: 70, critical: false }
        ]);
        // Deployment Phase Metrics
        this.qualityMetrics.set(Orchestrator_1.WorkflowPhase.DEPLOY, [
            { name: 'Deployment Success', weight: 0.4, threshold: 95, critical: true },
            { name: 'Health Check Status', weight: 0.25, threshold: 90, critical: true },
            { name: 'Performance Baseline', weight: 0.2, threshold: 80, critical: false },
            { name: 'Rollback Capability', weight: 0.15, threshold: 85, critical: false }
        ]);
        this.logger.info('Advanced quality metrics initialized for all phases');
    }
    /**
     * Evaluate quality gates for a phase
     */
    async evaluateQualityGates(phase, result, sessionId) {
        this.logger.info(`Evaluating quality gates for phase: ${phase}`, { sessionId });
        const metrics = this.qualityMetrics.get(phase) || [];
        const metricResults = [];
        let totalScore = 0;
        let weightSum = 0;
        let criticalFailures = 0;
        // Evaluate each metric
        for (const metric of metrics) {
            const metricResult = await this.evaluateMetric(metric, result, phase);
            metricResults.push(metricResult);
            totalScore += metricResult.score * metric.weight;
            weightSum += metric.weight;
            if (metric.critical && !metricResult.passed) {
                criticalFailures++;
            }
        }
        const overallScore = weightSum > 0 ? Math.round(totalScore / weightSum) : 0;
        const passed = overallScore >= this.criticalFailureThreshold && criticalFailures === 0;
        const rollbackRequired = overallScore < this.criticalFailureThreshold || criticalFailures > 0;
        // Generate recommendations
        const recommendations = this.generateRecommendations(metricResults, phase);
        // Calculate production readiness
        const productionReadiness = this.calculateProductionReadiness(metricResults, phase, overallScore);
        const qualityResult = {
            phase,
            overallScore,
            passed,
            critical: criticalFailures > 0,
            metrics: metricResults,
            recommendations,
            rollbackRequired,
            productionReadiness
        };
        // Create rollback point if quality is acceptable
        if (passed && overallScore >= 70) {
            this.createRollbackPoint(sessionId, phase, result, overallScore);
        }
        this.logger.info('Quality gate evaluation completed', {
            sessionId,
            phase,
            overallScore,
            passed,
            criticalFailures,
            rollbackRequired
        });
        return qualityResult;
    }
    /**
     * Evaluate individual metric
     */
    async evaluateMetric(metric, result, phase) {
        let score = 0;
        let details = {};
        let issues = [];
        let suggestions = [];
        try {
            switch (metric.name) {
                case 'Requirements Completeness':
                    score = this.evaluateRequirementsCompleteness(result);
                    break;
                case 'Technical Feasibility':
                    score = this.evaluateTechnicalFeasibility(result);
                    break;
                case 'API Design Quality':
                    score = this.evaluateAPIDesignQuality(result);
                    break;
                case 'Database Schema Integrity':
                    score = this.evaluateDatabaseSchemaIntegrity(result);
                    break;
                case 'Code Compilation Success':
                    score = this.evaluateCodeCompilation(result);
                    break;
                case 'API Implementation Completeness':
                    score = this.evaluateAPIImplementation(result);
                    break;
                case 'Test Coverage':
                    score = this.evaluateTestCoverage(result);
                    break;
                case 'Test Pass Rate':
                    score = this.evaluateTestPassRate(result);
                    break;
                case 'Deployment Success':
                    score = this.evaluateDeploymentSuccess(result);
                    break;
                case 'Security Implementation':
                    score = this.evaluateSecurityImplementation(result);
                    break;
                default:
                    // Generic evaluation based on result success and metadata
                    score = this.evaluateGenericMetric(result, metric.name);
            }
            // Generate issues and suggestions based on score
            if (score < metric.threshold) {
                issues.push(`${metric.name} score (${score}) below threshold (${metric.threshold})`);
                suggestions.push(`Improve ${metric.name.toLowerCase()} to meet quality standards`);
            }
            if (score < 50) {
                issues.push(`Critical quality issue in ${metric.name}`);
                suggestions.push(`Immediate attention required for ${metric.name}`);
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`Error evaluating metric ${metric.name}`, { error: errorMessage });
            score = 0;
            issues.push(`Evaluation failed: ${errorMessage}`);
            suggestions.push('Review metric evaluation logic');
        }
        return {
            metric,
            score: Math.max(0, Math.min(100, score)),
            passed: score >= metric.threshold,
            details,
            issues,
            suggestions
        };
    }
    /**
     * Evaluate requirements completeness
     */
    evaluateRequirementsCompleteness(result) {
        if (!result.success || !result.data)
            return 0;
        let score = 50; // Base score for successful execution
        // Check for key planning elements
        if (result.data.projectName)
            score += 10;
        if (result.data.businessDomain)
            score += 10;
        if (result.data.architecture)
            score += 10;
        if (result.data.technicalStack)
            score += 10;
        if (result.data.requirements && result.data.requirements.length > 0)
            score += 10;
        return Math.min(100, score);
    }
    /**
     * Evaluate technical feasibility
     */
    evaluateTechnicalFeasibility(result) {
        if (!result.success)
            return 0;
        let score = 60; // Base score for successful planning
        // Check for technical considerations
        if (result.data?.architecture?.scalability)
            score += 15;
        if (result.data?.architecture?.security)
            score += 15;
        if (result.data?.technicalStack?.database)
            score += 10;
        return Math.min(100, score);
    }
    /**
     * Evaluate API design quality
     */
    evaluateAPIDesignQuality(result) {
        if (!result.success || !result.data?.domainAPIs)
            return 0;
        const apis = result.data.domainAPIs;
        const apiCount = Object.keys(apis).length;
        let score = 40; // Base score
        // Score based on API count and structure
        if (apiCount >= 3)
            score += 20;
        if (apiCount >= 5)
            score += 10;
        // Check for proper API structure
        for (const [apiName, apiDef] of Object.entries(apis)) {
            if (apiDef && typeof apiDef === 'object') {
                score += 5;
            }
        }
        return Math.min(100, score);
    }
    /**
     * Evaluate database schema integrity
     */
    evaluateDatabaseSchemaIntegrity(result) {
        if (!result.success || !result.data?.databaseSchema)
            return 0;
        let score = 50; // Base score
        const schema = result.data.databaseSchema;
        if (schema.tables && schema.tables.length > 0)
            score += 25;
        if (schema.relationships)
            score += 15;
        if (schema.indexes)
            score += 10;
        return Math.min(100, score);
    }
    /**
     * Evaluate code compilation
     */
    evaluateCodeCompilation(result) {
        if (!result.success)
            return 0;
        let score = 70; // Base score for successful generation
        // Check for compilation indicators
        if (result.metadata?.compilationSuccess)
            score += 30;
        if (result.metadata?.syntaxErrors === 0)
            score += 10;
        if (result.data?.fileStructure)
            score += 10;
        // Penalize for errors
        if (result.metadata?.errorCount && result.metadata.errorCount > 0) {
            score -= result.metadata.errorCount * 5;
        }
        return Math.max(0, Math.min(100, score));
    }
    /**
     * Evaluate API implementation
     */
    evaluateAPIImplementation(result) {
        if (!result.success)
            return 0;
        let score = 60; // Base score
        if (result.data?.apis) {
            const implementedAPIs = Object.keys(result.data.apis).length;
            score += Math.min(30, implementedAPIs * 5);
        }
        if (result.data?.routes)
            score += 10;
        return Math.min(100, score);
    }
    /**
     * Evaluate test coverage
     */
    evaluateTestCoverage(result) {
        if (!result.success)
            return 0;
        let score = 40; // Base score
        if (result.metadata?.testCoverage) {
            score += Math.min(50, result.metadata.testCoverage);
        }
        if (result.data?.testFiles) {
            score += Math.min(10, result.data.testFiles.length * 2);
        }
        return Math.min(100, score);
    }
    /**
     * Evaluate test pass rate
     */
    evaluateTestPassRate(result) {
        if (!result.success)
            return 0;
        if (result.metadata?.testResults) {
            const { passed = 0, total = 1 } = result.metadata.testResults;
            return Math.round((passed / total) * 100);
        }
        return 70; // Default if no test results
    }
    /**
     * Evaluate deployment success
     */
    evaluateDeploymentSuccess(result) {
        if (!result.success)
            return 0;
        let score = 80; // Base score for successful deployment
        if (result.metadata?.deploymentStatus === 'success')
            score += 20;
        if (result.metadata?.healthCheck === 'passed')
            score += 10;
        return Math.min(100, score);
    }
    /**
     * Evaluate security implementation
     */
    evaluateSecurityImplementation(result) {
        if (!result.success)
            return 0;
        let score = 50; // Base score
        if (result.data?.securityConfiguration) {
            const config = result.data.securityConfiguration;
            if (config.authentication)
                score += 20;
            if (config.authorization)
                score += 15;
            if (config.encryption)
                score += 15;
        }
        return Math.min(100, score);
    }
    /**
     * Generic metric evaluation
     */
    evaluateGenericMetric(result, metricName) {
        if (!result.success)
            return 0;
        let score = 60; // Base score for successful execution
        // Add score based on result quality indicators
        if (result.metadata?.qualityScore) {
            score = Math.max(score, result.metadata.qualityScore);
        }
        if (result.metadata?.errorCount === 0)
            score += 10;
        if (result.data && Object.keys(result.data).length > 0)
            score += 10;
        return Math.min(100, score);
    }
    /**
     * Generate recommendations based on metric results
     */
    generateRecommendations(metricResults, phase) {
        const recommendations = [];
        const failedMetrics = metricResults.filter(m => !m.passed);
        const lowScoreMetrics = metricResults.filter(m => m.score < 70);
        if (failedMetrics.length > 0) {
            recommendations.push(`Address ${failedMetrics.length} failed quality metrics`);
            failedMetrics.forEach(metric => {
                recommendations.push(`Improve ${metric.metric.name}: ${metric.suggestions.join(', ')}`);
            });
        }
        if (lowScoreMetrics.length > 0) {
            recommendations.push(`Optimize ${lowScoreMetrics.length} metrics with low scores`);
        }
        // Phase-specific recommendations
        switch (phase) {
            case Orchestrator_1.WorkflowPhase.GENERATE:
                if (metricResults.some(m => m.metric.name === 'Code Compilation Success' && m.score < 90)) {
                    recommendations.push('Focus on resolving compilation errors before proceeding');
                }
                break;
            case Orchestrator_1.WorkflowPhase.TESTING:
                if (metricResults.some(m => m.metric.name === 'Test Pass Rate' && m.score < 85)) {
                    recommendations.push('Increase test pass rate before deployment');
                }
                break;
            case Orchestrator_1.WorkflowPhase.DATABASE:
                if (metricResults.some(m => m.metric.name.includes('Authentication') && m.score < 80)) {
                    recommendations.push('Strengthen authentication mechanisms');
                }
                break;
        }
        return recommendations;
    }
    /**
     * Calculate production readiness score
     */
    calculateProductionReadiness(metricResults, phase, overallScore) {
        const categories = {
            functionality: this.calculateCategoryScore(metricResults, ['Completeness', 'Implementation', 'Success']),
            reliability: this.calculateCategoryScore(metricResults, ['Test', 'Error', 'Validation']),
            performance: this.calculateCategoryScore(metricResults, ['Performance', 'Optimization', 'Speed']),
            security: this.calculateCategoryScore(metricResults, ['Security', 'Authentication', 'Authorization']),
            maintainability: this.calculateCategoryScore(metricResults, ['Quality', 'Documentation', 'Standards']),
            scalability: this.calculateCategoryScore(metricResults, ['Scalability', 'Architecture', 'Design'])
        };
        const overall = Math.round((categories.functionality * 0.25 +
            categories.reliability * 0.25 +
            categories.performance * 0.15 +
            categories.security * 0.15 +
            categories.maintainability * 0.1 +
            categories.scalability * 0.1));
        const blockers = [];
        const warnings = [];
        // Identify blockers and warnings
        metricResults.forEach(result => {
            if (result.metric.critical && !result.passed) {
                blockers.push(`Critical failure in ${result.metric.name}`);
            }
            else if (result.score < 60) {
                warnings.push(`Low score in ${result.metric.name} (${result.score})`);
            }
        });
        const readyForProduction = overall >= this.productionThreshold && blockers.length === 0;
        return {
            overall,
            categories,
            blockers,
            warnings,
            readyForProduction
        };
    }
    /**
     * Calculate category score based on relevant metrics
     */
    calculateCategoryScore(metricResults, keywords) {
        const relevantMetrics = metricResults.filter(result => keywords.some(keyword => result.metric.name.includes(keyword)));
        if (relevantMetrics.length === 0)
            return 70; // Default score
        const totalScore = relevantMetrics.reduce((sum, metric) => sum + metric.score, 0);
        return Math.round(totalScore / relevantMetrics.length);
    }
    /**
     * Create rollback point
     */
    createRollbackPoint(sessionId, phase, result, qualityScore) {
        if (!this.rollbackPoints.has(sessionId)) {
            this.rollbackPoints.set(sessionId, []);
        }
        const rollbackPoint = {
            sessionId,
            phase,
            timestamp: Date.now(),
            result: { ...result }, // Deep copy
            qualityScore,
            snapshot: {
                phase,
                data: result.data,
                metadata: result.metadata
            }
        };
        this.rollbackPoints.get(sessionId).push(rollbackPoint);
        // Keep only last 5 rollback points per session
        const points = this.rollbackPoints.get(sessionId);
        if (points.length > 5) {
            points.splice(0, points.length - 5);
        }
        this.logger.info('Rollback point created', {
            sessionId,
            phase,
            qualityScore,
            totalRollbackPoints: points.length
        });
    }
    /**
     * Get available rollback points
     */
    getRollbackPoints(sessionId) {
        return this.rollbackPoints.get(sessionId) || [];
    }
    /**
     * Execute rollback to a specific point
     */
    async executeRollback(sessionId, targetPhase) {
        const points = this.rollbackPoints.get(sessionId) || [];
        const targetPoint = points.find(point => point.phase === targetPhase);
        if (!targetPoint) {
            this.logger.warn('Rollback point not found', { sessionId, targetPhase });
            return null;
        }
        this.logger.info('Executing rollback', {
            sessionId,
            targetPhase,
            rollbackTimestamp: targetPoint.timestamp
        });
        // Remove rollback points after the target
        const targetIndex = points.indexOf(targetPoint);
        points.splice(targetIndex + 1);
        return targetPoint;
    }
    /**
     * Set production readiness threshold
     */
    setProductionThreshold(threshold) {
        this.productionThreshold = Math.max(0, Math.min(100, threshold));
        this.logger.info('Production threshold updated', { threshold: this.productionThreshold });
    }
    /**
     * Get quality gate statistics
     */
    getQualityGateStats(sessionId) {
        const stats = {
            productionThreshold: this.productionThreshold,
            criticalFailureThreshold: this.criticalFailureThreshold,
            totalPhases: this.qualityMetrics.size,
            totalMetrics: Array.from(this.qualityMetrics.values()).reduce((sum, metrics) => sum + metrics.length, 0)
        };
        if (sessionId) {
            const rollbackPoints = this.getRollbackPoints(sessionId);
            stats.rollbackPoints = rollbackPoints.length;
            stats.lastRollbackPoint = rollbackPoints[rollbackPoints.length - 1]?.phase;
        }
        return stats;
    }
    /**
     * Clear session data
     */
    clearSessionData(sessionId) {
        this.rollbackPoints.delete(sessionId);
        this.logger.debug('Session quality gate data cleared', { sessionId });
    }
}
exports.AdvancedQualityGateFramework = AdvancedQualityGateFramework;
//# sourceMappingURL=AdvancedQualityGateFramework.js.map
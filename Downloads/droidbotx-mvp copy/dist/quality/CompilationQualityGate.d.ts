import { QualityGate, WorkflowPhase } from '../orchestration/Orchestrator';
import { AgentResult } from '../core/BaseAgent';
export interface CompilationValidationResult {
    syntaxValid: boolean;
    importsValid: boolean;
    servicesComplete: boolean;
    issues: string[];
    warnings: string[];
    score: number;
}
/**
 * Enhanced quality gate that performs strict compilation validation
 * Prevents broken code from passing between phases
 */
export declare class CompilationQualityGate implements QualityGate {
    phase: WorkflowPhase;
    private importExportManager;
    private preCompilationValidator;
    private servicePatternStandardizer;
    constructor();
    validate(result: AgentResult): Promise<boolean>;
    getErrorMessage(): string;
    /**
     * Perform comprehensive validation of generated code
     */
    private performComprehensiveValidation;
    /**
     * Validate TypeScript syntax without full compilation
     */
    private validateTypeScriptSyntax;
    /**
     * Check for basic syntax issues without compilation
     */
    private checkBasicSyntaxIssues;
    /**
     * Validate import/export consistency
     */
    private validateImportExportConsistency;
    /**
     * Find duplicate imports in a file
     */
    private findDuplicateImports;
    /**
     * Find .js extensions in TypeScript imports
     * Note: .js extensions are actually required for ES modules in modern Node.js/TypeScript setups
     */
    private findJsExtensionsInImports;
    /**
     * Validate service implementation completeness
     */
    private validateServiceCompleteness;
    /**
     * Check if service class is empty or placeholder
     */
    private isEmptyServiceClass;
    /**
     * Check if service has proper error handling
     */
    private hasProperErrorHandling;
    /**
     * Check if service has proper TypeScript typing
     */
    private hasProperTyping;
    /**
     * Perform additional validations
     */
    private performAdditionalValidations;
    /**
     * Calculate validation score
     */
    private calculateValidationScore;
}
export default CompilationQualityGate;
//# sourceMappingURL=CompilationQualityGate.d.ts.map
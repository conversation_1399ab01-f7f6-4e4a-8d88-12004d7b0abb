import { GeneratedCode } from '../agents/CodingAgent';
export interface CrossLayerValidationContext {
    generatedCode: GeneratedCode;
    sessionId: string;
    requirements?: string[];
}
export interface CrossLayerValidationResult {
    success: boolean;
    score: number;
    validations: LayerValidation[];
    criticalIssues: ValidationIssue[];
    recommendations: string[];
}
export interface LayerValidation {
    layer: 'frontend' | 'backend' | 'database' | 'integration';
    score: number;
    issues: ValidationIssue[];
    passed: boolean;
}
export interface ValidationIssue {
    type: 'api-contract' | 'data-model' | 'authentication' | 'configuration' | 'dependency';
    severity: 'critical' | 'high' | 'medium' | 'low';
    description: string;
    location: string;
    impact: string;
    suggestedFix: string;
    autoFixable: boolean;
}
export interface APIContractValidation {
    endpoint: string;
    method: string;
    frontendCalls: boolean;
    backendImplemented: boolean;
    consistent: boolean;
    issues: string[];
}
export interface DataModelValidation {
    model: string;
    databaseTable: boolean;
    backendModel: boolean;
    frontendInterface: boolean;
    consistent: boolean;
    issues: string[];
}
export interface AuthenticationValidation {
    frontendAuth: boolean;
    backendAuth: boolean;
    consistent: boolean;
    secure: boolean;
    issues: string[];
}
export interface ConfigurationValidation {
    environmentConsistency: boolean;
    databaseConfig: boolean;
    apiEndpoints: boolean;
    issues: string[];
}
export declare class ComprehensiveCrossLayerValidator {
    private logger;
    constructor();
    /**
     * Validate full-stack integration across all layers
     */
    validateFullStackIntegration(context: CrossLayerValidationContext): Promise<CrossLayerValidationResult>;
    /**
     * Validate frontend layer
     */
    private validateFrontendLayer;
    /**
     * Validate backend layer
     */
    private validateBackendLayer;
    /**
     * Validate database layer
     */
    private validateDatabaseLayer;
    /**
     * Validate integration layer (cross-layer consistency)
     */
    private validateIntegrationLayer;
    /**
     * Validate API contracts between frontend and backend
     */
    private validateAPIContracts;
    /**
     * Validate data model consistency between database and application layers
     */
    private validateDataModelConsistency;
    /**
     * Validate authentication flow consistency
     */
    private validateAuthenticationFlow;
    /**
     * Validate configuration consistency
     */
    private validateConfigurationConsistency;
    private extractBackendEndpoints;
    private extractFrontendAPICalls;
    private extractDatabaseTables;
    private extractBackendModels;
    private extractFrontendInterfaces;
    private endpointsMatch;
    private checkFrontendAuthentication;
    private checkBackendAuthentication;
    private validateSQLSyntax;
    private calculateOverallScore;
    private generateRecommendations;
}
//# sourceMappingURL=ComprehensiveCrossLayerValidator.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComprehensiveCrossLayerValidator = void 0;
const Logger_1 = require("../core/Logger");
class ComprehensiveCrossLayerValidator {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Validate full-stack integration across all layers
     */
    async validateFullStackIntegration(context) {
        this.logger.info('Starting comprehensive cross-layer validation', {
            sessionId: context.sessionId,
            fileCount: Object.keys(context.generatedCode.files).length
        });
        const validations = [];
        // Validate each layer
        validations.push(await this.validateFrontendLayer(context));
        validations.push(await this.validateBackendLayer(context));
        validations.push(await this.validateDatabaseLayer(context));
        validations.push(await this.validateIntegrationLayer(context));
        // Collect all issues
        const allIssues = validations.flatMap(v => v.issues);
        const criticalIssues = allIssues.filter(i => i.severity === 'critical');
        // Calculate overall score
        const score = this.calculateOverallScore(validations);
        // Generate recommendations
        const recommendations = this.generateRecommendations(validations);
        const result = {
            success: criticalIssues.length === 0 && score >= 80,
            score,
            validations,
            criticalIssues,
            recommendations
        };
        this.logger.info('Cross-layer validation completed', {
            sessionId: context.sessionId,
            success: result.success,
            score: result.score,
            criticalIssues: criticalIssues.length,
            totalIssues: allIssues.length
        });
        return result;
    }
    /**
     * Validate frontend layer
     */
    async validateFrontendLayer(context) {
        const issues = [];
        const files = context.generatedCode.files;
        // Check if frontend files exist
        const frontendFiles = Object.keys(files).filter(path => path.includes('frontend/src'));
        if (frontendFiles.length === 0) {
            issues.push({
                type: 'dependency',
                severity: 'critical',
                description: 'No frontend files generated',
                location: 'frontend/',
                impact: 'Application cannot run without frontend',
                suggestedFix: 'Generate React frontend components',
                autoFixable: false
            });
        }
        // Check for React components
        const componentFiles = frontendFiles.filter(path => path.endsWith('.tsx'));
        if (componentFiles.length === 0) {
            issues.push({
                type: 'dependency',
                severity: 'high',
                description: 'No React components found',
                location: 'frontend/src/components/',
                impact: 'No UI components available',
                suggestedFix: 'Generate React components',
                autoFixable: false
            });
        }
        // Check for API service files
        const hasApiService = frontendFiles.some(path => path.includes('service') || path.includes('api') || files[path].includes('axios'));
        if (!hasApiService) {
            issues.push({
                type: 'api-contract',
                severity: 'medium',
                description: 'No API service layer found in frontend',
                location: 'frontend/src/services/',
                impact: 'Frontend cannot communicate with backend',
                suggestedFix: 'Create API service layer with axios',
                autoFixable: true
            });
        }
        // Check for authentication handling
        const hasAuthHandling = frontendFiles.some(path => files[path].includes('token') || files[path].includes('auth') || files[path].includes('login'));
        if (!hasAuthHandling) {
            issues.push({
                type: 'authentication',
                severity: 'medium',
                description: 'No authentication handling in frontend',
                location: 'frontend/src/',
                impact: 'Authentication features not implemented',
                suggestedFix: 'Add authentication context and token management',
                autoFixable: true
            });
        }
        const score = Math.max(0, 100 - (issues.length * 15));
        return {
            layer: 'frontend',
            score,
            issues,
            passed: issues.filter(i => i.severity === 'critical').length === 0
        };
    }
    /**
     * Validate backend layer
     */
    async validateBackendLayer(context) {
        const issues = [];
        const files = context.generatedCode.files;
        // Check if backend files exist
        const backendFiles = Object.keys(files).filter(path => path.includes('backend/src'));
        if (backendFiles.length === 0) {
            issues.push({
                type: 'dependency',
                severity: 'critical',
                description: 'No backend files generated',
                location: 'backend/',
                impact: 'Application cannot function without backend API',
                suggestedFix: 'Generate Express.js backend',
                autoFixable: false
            });
        }
        // Check for server file
        const hasServerFile = Object.keys(files).some(path => path.includes('server.ts') || path.includes('app.ts') || path.includes('index.ts'));
        if (!hasServerFile) {
            issues.push({
                type: 'dependency',
                severity: 'critical',
                description: 'No main server file found',
                location: 'backend/src/',
                impact: 'Backend cannot start',
                suggestedFix: 'Create server.ts with Express setup',
                autoFixable: true
            });
        }
        // Check for route files
        const routeFiles = Object.keys(files).filter(path => path.includes('routes'));
        if (routeFiles.length === 0) {
            issues.push({
                type: 'api-contract',
                severity: 'high',
                description: 'No route files found',
                location: 'backend/src/routes/',
                impact: 'No API endpoints available',
                suggestedFix: 'Generate route files for API endpoints',
                autoFixable: true
            });
        }
        // Check for middleware
        const hasMiddleware = backendFiles.some(path => files[path].includes('middleware') || files[path].includes('cors') || files[path].includes('helmet'));
        if (!hasMiddleware) {
            issues.push({
                type: 'configuration',
                severity: 'medium',
                description: 'No security middleware found',
                location: 'backend/src/',
                impact: 'Backend lacks security protections',
                suggestedFix: 'Add CORS, helmet, and other security middleware',
                autoFixable: true
            });
        }
        // Check for database connection
        const hasDatabaseConnection = backendFiles.some(path => files[path].includes('pg') || files[path].includes('Pool') || files[path].includes('database'));
        if (!hasDatabaseConnection) {
            issues.push({
                type: 'data-model',
                severity: 'high',
                description: 'No database connection found',
                location: 'backend/src/',
                impact: 'Backend cannot access database',
                suggestedFix: 'Add PostgreSQL connection setup',
                autoFixable: true
            });
        }
        const score = Math.max(0, 100 - (issues.length * 12));
        return {
            layer: 'backend',
            score,
            issues,
            passed: issues.filter(i => i.severity === 'critical').length === 0
        };
    }
    /**
     * Validate database layer
     */
    async validateDatabaseLayer(context) {
        const issues = [];
        const files = context.generatedCode.files;
        // Check for database initialization file
        const hasInitSql = Object.keys(files).some(path => path.includes('init.sql') || path.includes('schema.sql'));
        if (!hasInitSql) {
            issues.push({
                type: 'data-model',
                severity: 'critical',
                description: 'No database initialization file found',
                location: 'backend/',
                impact: 'Database cannot be set up',
                suggestedFix: 'Create init.sql with table definitions',
                autoFixable: true
            });
        }
        // Check for Docker database setup
        const hasDockerDb = Object.keys(files).some(path => path.includes('docker-compose') && files[path].includes('postgres'));
        if (!hasDockerDb) {
            issues.push({
                type: 'configuration',
                severity: 'medium',
                description: 'No Docker database configuration found',
                location: 'docker-compose.yml',
                impact: 'Database setup not automated',
                suggestedFix: 'Add PostgreSQL service to docker-compose.yml',
                autoFixable: true
            });
        }
        // Validate SQL syntax if init.sql exists
        if (hasInitSql) {
            const initSqlPath = Object.keys(files).find(path => path.includes('init.sql') || path.includes('schema.sql'));
            if (initSqlPath) {
                const sqlContent = files[initSqlPath];
                const sqlIssues = this.validateSQLSyntax(sqlContent);
                issues.push(...sqlIssues);
            }
        }
        const score = Math.max(0, 100 - (issues.length * 20));
        return {
            layer: 'database',
            score,
            issues,
            passed: issues.filter(i => i.severity === 'critical').length === 0
        };
    }
    /**
     * Validate integration layer (cross-layer consistency)
     */
    async validateIntegrationLayer(context) {
        const issues = [];
        // Validate API contracts
        const apiValidation = await this.validateAPIContracts(context);
        issues.push(...apiValidation);
        // Validate data model consistency
        const dataValidation = await this.validateDataModelConsistency(context);
        issues.push(...dataValidation);
        // Validate authentication flow
        const authValidation = await this.validateAuthenticationFlow(context);
        issues.push(...authValidation);
        // Validate configuration consistency
        const configValidation = await this.validateConfigurationConsistency(context);
        issues.push(...configValidation);
        const score = Math.max(0, 100 - (issues.length * 10));
        return {
            layer: 'integration',
            score,
            issues,
            passed: issues.filter(i => i.severity === 'critical').length === 0
        };
    }
    /**
     * Validate API contracts between frontend and backend
     */
    async validateAPIContracts(context) {
        const issues = [];
        const files = context.generatedCode.files;
        // Extract API endpoints from backend routes
        const backendEndpoints = this.extractBackendEndpoints(files);
        // Extract API calls from frontend
        const frontendAPICalls = this.extractFrontendAPICalls(files);
        // Check for missing backend implementations
        for (const apiCall of frontendAPICalls) {
            const matchingEndpoint = backendEndpoints.find(endpoint => this.endpointsMatch(apiCall, endpoint));
            if (!matchingEndpoint) {
                issues.push({
                    type: 'api-contract',
                    severity: 'high',
                    description: `Frontend calls ${apiCall.method} ${apiCall.path} but backend endpoint not found`,
                    location: `backend/src/routes/`,
                    impact: 'API call will fail at runtime',
                    suggestedFix: `Implement ${apiCall.method} ${apiCall.path} in backend routes`,
                    autoFixable: true
                });
            }
        }
        // Check for unused backend endpoints
        for (const endpoint of backendEndpoints) {
            const matchingCall = frontendAPICalls.find(call => this.endpointsMatch(call, endpoint));
            if (!matchingCall) {
                issues.push({
                    type: 'api-contract',
                    severity: 'low',
                    description: `Backend endpoint ${endpoint.method} ${endpoint.path} not used by frontend`,
                    location: `frontend/src/`,
                    impact: 'Unused code, potential maintenance burden',
                    suggestedFix: `Add frontend call to ${endpoint.path} or remove unused endpoint`,
                    autoFixable: false
                });
            }
        }
        return issues;
    }
    /**
     * Validate data model consistency between database and application layers
     */
    async validateDataModelConsistency(context) {
        const issues = [];
        const files = context.generatedCode.files;
        // Extract database tables
        const databaseTables = this.extractDatabaseTables(files);
        // Extract backend models
        const backendModels = this.extractBackendModels(files);
        // Extract frontend interfaces
        const frontendInterfaces = this.extractFrontendInterfaces(files);
        // Check database-backend consistency
        for (const table of databaseTables) {
            const matchingModel = backendModels.find(model => model.name.toLowerCase() === table.name.toLowerCase());
            if (!matchingModel) {
                issues.push({
                    type: 'data-model',
                    severity: 'high',
                    description: `Database table '${table.name}' has no corresponding backend model`,
                    location: 'backend/src/models/',
                    impact: 'Cannot perform database operations for this entity',
                    suggestedFix: `Create model file for ${table.name}`,
                    autoFixable: true
                });
            }
        }
        // Check backend-frontend consistency
        for (const model of backendModels) {
            const matchingInterface = frontendInterfaces.find(iface => iface.name.toLowerCase().includes(model.name.toLowerCase()));
            if (!matchingInterface) {
                issues.push({
                    type: 'data-model',
                    severity: 'medium',
                    description: `Backend model '${model.name}' has no corresponding frontend interface`,
                    location: 'frontend/src/types/',
                    impact: 'Frontend lacks type safety for this entity',
                    suggestedFix: `Create TypeScript interface for ${model.name}`,
                    autoFixable: true
                });
            }
        }
        return issues;
    }
    /**
     * Validate authentication flow consistency
     */
    async validateAuthenticationFlow(context) {
        const issues = [];
        const files = context.generatedCode.files;
        const frontendAuth = this.checkFrontendAuthentication(files);
        const backendAuth = this.checkBackendAuthentication(files);
        if (frontendAuth.hasAuth && !backendAuth.hasAuth) {
            issues.push({
                type: 'authentication',
                severity: 'critical',
                description: 'Frontend has authentication but backend does not',
                location: 'backend/src/',
                impact: 'Authentication will not work',
                suggestedFix: 'Implement JWT authentication in backend',
                autoFixable: true
            });
        }
        if (!frontendAuth.hasAuth && backendAuth.hasAuth) {
            issues.push({
                type: 'authentication',
                severity: 'high',
                description: 'Backend has authentication but frontend does not',
                location: 'frontend/src/',
                impact: 'Users cannot authenticate',
                suggestedFix: 'Implement authentication UI and token management',
                autoFixable: true
            });
        }
        if (frontendAuth.hasAuth && backendAuth.hasAuth) {
            // Check for token consistency
            if (frontendAuth.tokenType !== backendAuth.tokenType) {
                issues.push({
                    type: 'authentication',
                    severity: 'medium',
                    description: 'Frontend and backend use different token types',
                    location: 'frontend/src/ and backend/src/',
                    impact: 'Authentication may fail due to token mismatch',
                    suggestedFix: 'Standardize on JWT tokens across frontend and backend',
                    autoFixable: true
                });
            }
        }
        return issues;
    }
    /**
     * Validate configuration consistency
     */
    async validateConfigurationConsistency(context) {
        const issues = [];
        const files = context.generatedCode.files;
        // Check environment files
        const envFiles = Object.keys(files).filter(path => path.includes('.env'));
        if (envFiles.length === 0) {
            issues.push({
                type: 'configuration',
                severity: 'medium',
                description: 'No environment configuration files found',
                location: 'project root',
                impact: 'Configuration not externalized',
                suggestedFix: 'Create .env.example with required variables',
                autoFixable: true
            });
        }
        // Check Docker configuration
        const hasDockerCompose = Object.keys(files).some(path => path.includes('docker-compose'));
        if (!hasDockerCompose) {
            issues.push({
                type: 'configuration',
                severity: 'low',
                description: 'No Docker Compose configuration found',
                location: 'project root',
                impact: 'Deployment not containerized',
                suggestedFix: 'Create docker-compose.yml for easy deployment',
                autoFixable: true
            });
        }
        return issues;
    }
    // Helper methods for extraction and validation
    extractBackendEndpoints(files) {
        const endpoints = [];
        for (const [filePath, content] of Object.entries(files)) {
            if (filePath.includes('routes') && filePath.endsWith('.ts')) {
                const routeRegex = /router\.(get|post|put|delete|patch)\(['"`]([^'"`]+)['"`]/g;
                let match;
                while ((match = routeRegex.exec(content)) !== null) {
                    endpoints.push({
                        method: match[1].toUpperCase(),
                        path: match[2],
                        file: filePath
                    });
                }
            }
        }
        return endpoints;
    }
    extractFrontendAPICalls(files) {
        const apiCalls = [];
        for (const [filePath, content] of Object.entries(files)) {
            if (filePath.includes('frontend/src') && (filePath.endsWith('.ts') || filePath.endsWith('.tsx'))) {
                // Look for axios calls or fetch calls
                const axiosRegex = /axios\.(get|post|put|delete|patch)\(['"`]([^'"`]+)['"`]/g;
                const fetchRegex = /fetch\(['"`]([^'"`]+)['"`][^)]*\{[^}]*method:\s*['"`](GET|POST|PUT|DELETE|PATCH)['"`]/g;
                let match;
                while ((match = axiosRegex.exec(content)) !== null) {
                    apiCalls.push({
                        method: match[1].toUpperCase(),
                        path: match[2],
                        file: filePath
                    });
                }
                while ((match = fetchRegex.exec(content)) !== null) {
                    apiCalls.push({
                        method: match[2].toUpperCase(),
                        path: match[1],
                        file: filePath
                    });
                }
            }
        }
        return apiCalls;
    }
    extractDatabaseTables(files) {
        const tables = [];
        for (const [filePath, content] of Object.entries(files)) {
            if (filePath.includes('init.sql') || filePath.includes('schema.sql')) {
                const tableRegex = /CREATE TABLE\s+(\w+)\s*\(([\s\S]*?)\);/g;
                let match;
                while ((match = tableRegex.exec(content)) !== null) {
                    const tableName = match[1];
                    const fieldsText = match[2];
                    const fields = fieldsText.split(',').map(field => field.trim().split(' ')[0]);
                    tables.push({
                        name: tableName,
                        fields
                    });
                }
            }
        }
        return tables;
    }
    extractBackendModels(files) {
        const models = [];
        for (const [filePath, content] of Object.entries(files)) {
            if (filePath.includes('models') && filePath.endsWith('.ts')) {
                const interfaceRegex = /interface\s+(\w+)/g;
                const classRegex = /class\s+(\w+)/g;
                let match;
                while ((match = interfaceRegex.exec(content)) !== null) {
                    models.push({ name: match[1], file: filePath });
                }
                while ((match = classRegex.exec(content)) !== null) {
                    models.push({ name: match[1], file: filePath });
                }
            }
        }
        return models;
    }
    extractFrontendInterfaces(files) {
        const interfaces = [];
        for (const [filePath, content] of Object.entries(files)) {
            if (filePath.includes('frontend/src') && (filePath.endsWith('.ts') || filePath.endsWith('.tsx'))) {
                const interfaceRegex = /interface\s+(\w+)/g;
                let match;
                while ((match = interfaceRegex.exec(content)) !== null) {
                    interfaces.push({ name: match[1], file: filePath });
                }
            }
        }
        return interfaces;
    }
    endpointsMatch(call, endpoint) {
        if (call.method !== endpoint.method)
            return false;
        // Simple path matching - could be enhanced for parameter matching
        const callPath = call.path.replace(/\/\d+/g, '/:id'); // Replace numbers with :id
        const endpointPath = endpoint.path;
        return callPath === endpointPath || call.path === endpointPath;
    }
    checkFrontendAuthentication(files) {
        for (const [filePath, content] of Object.entries(files)) {
            if (filePath.includes('frontend/src')) {
                if (content.includes('token') || content.includes('auth') || content.includes('login')) {
                    const tokenType = content.includes('jwt') || content.includes('JWT') ? 'jwt' : 'token';
                    return { hasAuth: true, tokenType };
                }
            }
        }
        return { hasAuth: false, tokenType: '' };
    }
    checkBackendAuthentication(files) {
        for (const [filePath, content] of Object.entries(files)) {
            if (filePath.includes('backend/src')) {
                if (content.includes('jsonwebtoken') || content.includes('jwt') || content.includes('authenticateToken')) {
                    return { hasAuth: true, tokenType: 'jwt' };
                }
                if (content.includes('token') || content.includes('auth')) {
                    return { hasAuth: true, tokenType: 'token' };
                }
            }
        }
        return { hasAuth: false, tokenType: '' };
    }
    validateSQLSyntax(sqlContent) {
        const issues = [];
        // Basic SQL syntax validation
        if (!sqlContent.includes('CREATE TABLE')) {
            issues.push({
                type: 'data-model',
                severity: 'high',
                description: 'No CREATE TABLE statements found in SQL file',
                location: 'init.sql',
                impact: 'Database tables will not be created',
                suggestedFix: 'Add CREATE TABLE statements for required entities',
                autoFixable: true
            });
        }
        // Check for missing semicolons
        const statements = sqlContent.split('\n').filter(line => line.trim().length > 0 && !line.trim().startsWith('--'));
        for (const statement of statements) {
            if (statement.trim().toUpperCase().startsWith('CREATE') && !statement.trim().endsWith(';')) {
                issues.push({
                    type: 'data-model',
                    severity: 'medium',
                    description: 'SQL statement missing semicolon',
                    location: 'init.sql',
                    impact: 'SQL execution may fail',
                    suggestedFix: 'Add semicolon at end of SQL statements',
                    autoFixable: true
                });
            }
        }
        return issues;
    }
    calculateOverallScore(validations) {
        if (validations.length === 0)
            return 0;
        const totalScore = validations.reduce((sum, validation) => sum + validation.score, 0);
        return Math.round(totalScore / validations.length);
    }
    generateRecommendations(validations) {
        const recommendations = [];
        const criticalIssues = validations.flatMap(v => v.issues).filter(i => i.severity === 'critical');
        const highIssues = validations.flatMap(v => v.issues).filter(i => i.severity === 'high');
        if (criticalIssues.length > 0) {
            recommendations.push('Address critical issues immediately to ensure application functionality');
        }
        if (highIssues.length > 0) {
            recommendations.push('Resolve high-priority issues to improve application reliability');
        }
        const autoFixableIssues = validations.flatMap(v => v.issues).filter(i => i.autoFixable);
        if (autoFixableIssues.length > 0) {
            recommendations.push(`${autoFixableIssues.length} issues can be automatically fixed`);
        }
        // Layer-specific recommendations
        const failedLayers = validations.filter(v => !v.passed);
        if (failedLayers.length > 0) {
            recommendations.push(`Focus on improving: ${failedLayers.map(l => l.layer).join(', ')}`);
        }
        return recommendations;
    }
}
exports.ComprehensiveCrossLayerValidator = ComprehensiveCrossLayerValidator;
//# sourceMappingURL=ComprehensiveCrossLayerValidator.js.map
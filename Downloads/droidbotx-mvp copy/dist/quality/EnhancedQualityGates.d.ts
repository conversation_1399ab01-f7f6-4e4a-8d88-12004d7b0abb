import { GeneratedCode } from '../agents/CodingAgent';
export interface ValidationResult {
    passed: boolean;
    issues: string[];
    warnings?: string[];
    score?: number;
}
export interface QualityMetrics {
    apiCoverage: number;
    componentIntegration: number;
    databaseCompleteness: number;
    businessLogicCompleteness: number;
    overallScore: number;
}
export declare class EnhancedQualityGates {
    /**
     * Validates that all frontend API calls have corresponding backend endpoints
     */
    validateAPIAlignment(generatedCode: GeneratedCode): ValidationResult;
    /**
     * Validates that all generated components are accessible via routes
     */
    validateComponentIntegration(generatedCode: GeneratedCode): ValidationResult;
    /**
     * Validates database schema completeness for business requirements
     */
    validateDatabaseSchema(generatedCode: GeneratedCode, requirements: string[]): ValidationResult;
    /**
     * Validates that business logic is implemented, not just placeholders
     */
    validateBusinessLogicCompleteness(generatedCode: GeneratedCode): ValidationResult;
    /**
     * Comprehensive quality assessment
     */
    assessOverallQuality(generatedCode: GeneratedCode, requirements: string[]): QualityMetrics;
    private extractFrontendAPICalls;
    private extractBackendRoutes;
    private extractComponents;
    private extractRoutes;
    private routeMatches;
    private extractDatabaseSchema;
    private getRequiredTablesFromRequirements;
    private validateTableRelationships;
    private validateIndexes;
    private findPlaceholderComponents;
    private findIncompleteServices;
    private findTodoComments;
}
//# sourceMappingURL=EnhancedQualityGates.d.ts.map
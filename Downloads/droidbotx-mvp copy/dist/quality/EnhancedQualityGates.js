"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedQualityGates = void 0;
class EnhancedQualityGates {
    /**
     * Validates that all frontend API calls have corresponding backend endpoints
     */
    validateAPIAlignment(generatedCode) {
        const issues = [];
        const warnings = [];
        try {
            // Extract API calls from frontend code
            const frontendAPICalls = this.extractFrontendAPICalls(generatedCode);
            // Extract backend routes
            const backendRoutes = this.extractBackendRoutes(generatedCode);
            // Check alignment
            frontendAPICalls.forEach(apiCall => {
                const matchingRoute = backendRoutes.find(route => this.routeMatches(apiCall.endpoint, route.path, apiCall.method, route.method));
                if (!matchingRoute) {
                    issues.push(`Frontend calls ${apiCall.method} ${apiCall.endpoint} but no matching backend route exists`);
                }
            });
            // Check for unused backend routes
            backendRoutes.forEach(route => {
                const isUsed = frontendAPICalls.some(apiCall => this.routeMatches(apiCall.endpoint, route.path, apiCall.method, route.method));
                if (!isUsed && !route.path.includes('/auth/') && !route.path.includes('/users/')) {
                    warnings.push(`Backend route ${route.method} ${route.path} is not used by frontend`);
                }
            });
            const score = Math.max(0, 100 - (issues.length * 20) - (warnings.length * 5));
            return {
                passed: issues.length === 0,
                issues,
                warnings,
                score
            };
        }
        catch (error) {
            return {
                passed: false,
                issues: [`Error validating API alignment: ${error instanceof Error ? error.message : 'Unknown error'}`],
                score: 0
            };
        }
    }
    /**
     * Validates that all generated components are accessible via routes
     */
    validateComponentIntegration(generatedCode) {
        const issues = [];
        const warnings = [];
        try {
            // Extract components from generated code
            const components = this.extractComponents(generatedCode);
            // Extract routes from App.tsx
            const routes = this.extractRoutes(generatedCode);
            // Find orphaned components
            const orphanedComponents = components.filter(component => {
                // Skip base components that don't need routes
                if (['Header', 'Sidebar', 'PrivateRoute', 'AuthContext'].includes(component.name)) {
                    return false;
                }
                return !routes.some(route => route.component === component.name);
            });
            orphanedComponents.forEach(component => {
                issues.push(`Component ${component.name} is not accessible via any route`);
            });
            // Check for missing component imports
            routes.forEach(route => {
                const componentExists = components.some(comp => comp.name === route.component);
                if (!componentExists) {
                    issues.push(`Route references component ${route.component} but component file doesn't exist`);
                }
            });
            const score = Math.max(0, 100 - (issues.length * 15) - (warnings.length * 5));
            return {
                passed: issues.length === 0,
                issues,
                warnings,
                score
            };
        }
        catch (error) {
            return {
                passed: false,
                issues: [`Error validating component integration: ${error instanceof Error ? error.message : 'Unknown error'}`],
                score: 0
            };
        }
    }
    /**
     * Validates database schema completeness for business requirements
     */
    validateDatabaseSchema(generatedCode, requirements) {
        const issues = [];
        const warnings = [];
        try {
            // Extract database schema from init.sql or migration files
            const schema = this.extractDatabaseSchema(generatedCode);
            // Check for required tables based on requirements
            const requiredTables = this.getRequiredTablesFromRequirements(requirements);
            requiredTables.forEach(tableName => {
                if (!schema.tables.includes(tableName)) {
                    issues.push(`Required table '${tableName}' is missing from database schema`);
                }
            });
            // Check for proper relationships
            const relationshipIssues = this.validateTableRelationships(schema, requirements);
            issues.push(...relationshipIssues);
            // Check for indexes on commonly queried fields
            const indexWarnings = this.validateIndexes(schema);
            warnings.push(...indexWarnings);
            const score = Math.max(0, 100 - (issues.length * 20) - (warnings.length * 5));
            return {
                passed: issues.length === 0,
                issues,
                warnings,
                score
            };
        }
        catch (error) {
            return {
                passed: false,
                issues: [`Error validating database schema: ${error instanceof Error ? error.message : 'Unknown error'}`],
                score: 0
            };
        }
    }
    /**
     * Validates that business logic is implemented, not just placeholders
     */
    validateBusinessLogicCompleteness(generatedCode) {
        const issues = [];
        const warnings = [];
        try {
            // Check for placeholder components
            const placeholderComponents = this.findPlaceholderComponents(generatedCode);
            placeholderComponents.forEach(component => {
                issues.push(`Component ${component.name} contains only placeholder content`);
            });
            // Check for empty service methods
            const incompleteServices = this.findIncompleteServices(generatedCode);
            incompleteServices.forEach(service => {
                warnings.push(`Service ${service.name} has incomplete or missing business logic`);
            });
            // Check for TODO comments indicating incomplete implementation
            const todoComments = this.findTodoComments(generatedCode);
            todoComments.forEach(todo => {
                warnings.push(`TODO found in ${todo.file}: ${todo.comment}`);
            });
            const score = Math.max(0, 100 - (issues.length * 25) - (warnings.length * 10));
            return {
                passed: issues.length === 0,
                issues,
                warnings,
                score
            };
        }
        catch (error) {
            return {
                passed: false,
                issues: [`Error validating business logic completeness: ${error instanceof Error ? error.message : 'Unknown error'}`],
                score: 0
            };
        }
    }
    /**
     * Comprehensive quality assessment
     */
    assessOverallQuality(generatedCode, requirements) {
        const apiAlignment = this.validateAPIAlignment(generatedCode);
        const componentIntegration = this.validateComponentIntegration(generatedCode);
        const databaseCompleteness = this.validateDatabaseSchema(generatedCode, requirements);
        const businessLogicCompleteness = this.validateBusinessLogicCompleteness(generatedCode);
        const overallScore = Math.round((apiAlignment.score + componentIntegration.score + databaseCompleteness.score + businessLogicCompleteness.score) / 4);
        return {
            apiCoverage: apiAlignment.score,
            componentIntegration: componentIntegration.score,
            databaseCompleteness: databaseCompleteness.score,
            businessLogicCompleteness: businessLogicCompleteness.score,
            overallScore
        };
    }
    // Helper methods for extraction and validation
    extractFrontendAPICalls(generatedCode) {
        const apiCalls = [];
        Object.entries(generatedCode.files).forEach(([filePath, content]) => {
            if (filePath.includes('frontend/') && (filePath.endsWith('.ts') || filePath.endsWith('.tsx'))) {
                // Look for API calls patterns
                const apiCallPatterns = [
                    /api\.get\(['"`]([^'"`]+)['"`]\)/g,
                    /api\.post\(['"`]([^'"`]+)['"`]\)/g,
                    /api\.put\(['"`]([^'"`]+)['"`]\)/g,
                    /api\.delete\(['"`]([^'"`]+)['"`]\)/g,
                    /fetch\(['"`]([^'"`]*\/api\/[^'"`]+)['"`]\)/g,
                    /axios\.get\(['"`]([^'"`]+)['"`]\)/g,
                    /axios\.post\(['"`]([^'"`]+)['"`]\)/g,
                ];
                apiCallPatterns.forEach((pattern, index) => {
                    const method = ['GET', 'POST', 'PUT', 'DELETE', 'GET', 'GET', 'POST'][index];
                    let match;
                    while ((match = pattern.exec(content)) !== null) {
                        apiCalls.push({
                            endpoint: match[1],
                            method,
                            file: filePath
                        });
                    }
                });
            }
        });
        return apiCalls;
    }
    extractBackendRoutes(generatedCode) {
        const routes = [];
        Object.entries(generatedCode.files).forEach(([filePath, content]) => {
            if (filePath.includes('backend/') && filePath.includes('routes/') && filePath.endsWith('.ts')) {
                // Look for Express route patterns
                const routePatterns = [
                    /router\.get\(['"`]([^'"`]+)['"`]/g,
                    /router\.post\(['"`]([^'"`]+)['"`]/g,
                    /router\.put\(['"`]([^'"`]+)['"`]/g,
                    /router\.delete\(['"`]([^'"`]+)['"`]/g,
                ];
                routePatterns.forEach((pattern, index) => {
                    const method = ['GET', 'POST', 'PUT', 'DELETE'][index];
                    let match;
                    while ((match = pattern.exec(content)) !== null) {
                        routes.push({
                            path: match[1],
                            method,
                            file: filePath
                        });
                    }
                });
            }
        });
        return routes;
    }
    extractComponents(generatedCode) {
        const components = [];
        Object.entries(generatedCode.files).forEach(([filePath, content]) => {
            if (filePath.includes('frontend/') && filePath.endsWith('.tsx')) {
                // Extract component name from file path
                const fileName = filePath.split('/').pop()?.replace('.tsx', '');
                if (fileName) {
                    components.push({
                        name: fileName,
                        file: filePath
                    });
                }
            }
        });
        return components;
    }
    extractRoutes(generatedCode) {
        const routes = [];
        // Look for App.tsx or routing configuration
        const appFile = generatedCode.files['frontend/src/App.tsx'];
        if (appFile) {
            // Extract Route components
            const routePattern = /<Route[^>]+path=['"`]([^'"`]+)['"`][^>]+element={[^}]*<([^>\s]+)[^}]*}[^>]*>/g;
            let match;
            while ((match = routePattern.exec(appFile)) !== null) {
                routes.push({
                    path: match[1],
                    component: match[2]
                });
            }
        }
        return routes;
    }
    routeMatches(frontendEndpoint, backendPath, frontendMethod, backendMethod) {
        if (frontendMethod !== backendMethod)
            return false;
        // Normalize paths
        const normalizedFrontend = frontendEndpoint.replace(/^\/api/, '');
        const normalizedBackend = backendPath;
        // Handle parameter matching (:id, etc.)
        const backendRegex = normalizedBackend.replace(/:[\w]+/g, '[^/]+');
        const regex = new RegExp(`^${backendRegex}$`);
        return regex.test(normalizedFrontend);
    }
    extractDatabaseSchema(generatedCode) {
        const schema = { tables: [], relationships: [] };
        // Look for init.sql or migration files
        const initFile = generatedCode.files['init.sql'] || generatedCode.files['backend/init.sql'];
        if (initFile) {
            // Extract table names
            const tablePattern = /CREATE TABLE[^(]*\s+(\w+)\s*\(/gi;
            let match;
            while ((match = tablePattern.exec(initFile)) !== null) {
                schema.tables.push(match[1]);
            }
            // Extract relationships
            const relationshipPattern = /REFERENCES\s+(\w+)\s*\(/gi;
            while ((match = relationshipPattern.exec(initFile)) !== null) {
                schema.relationships.push(match[1]);
            }
        }
        return schema;
    }
    getRequiredTablesFromRequirements(requirements) {
        const tables = [];
        requirements.forEach(req => {
            const reqLower = req.toLowerCase();
            if (reqLower.includes('product') || reqLower.includes('barcode')) {
                tables.push('products');
            }
            if (reqLower.includes('inventory') || reqLower.includes('stock')) {
                tables.push('inventory');
            }
            if (reqLower.includes('sale') || reqLower.includes('pos')) {
                tables.push('sales');
            }
            if (reqLower.includes('customer')) {
                tables.push('customers');
            }
            if (reqLower.includes('vehicle')) {
                tables.push('vehicles');
            }
            if (reqLower.includes('supplier')) {
                tables.push('suppliers');
            }
            if (reqLower.includes('warranty')) {
                tables.push('warranty_claims');
            }
        });
        return [...new Set(tables)]; // Remove duplicates
    }
    validateTableRelationships(schema, requirements) {
        const issues = [];
        // Check for missing foreign key relationships
        if (schema.tables.includes('sales') && schema.tables.includes('customers')) {
            if (!schema.relationships.includes('customers')) {
                issues.push('Sales table should reference customers table');
            }
        }
        if (schema.tables.includes('inventory') && schema.tables.includes('products')) {
            if (!schema.relationships.includes('products')) {
                issues.push('Inventory table should reference products table');
            }
        }
        return issues;
    }
    validateIndexes(schema) {
        const warnings = [];
        // This is a simplified check - in a real implementation, 
        // you'd parse the actual index definitions
        if (schema.tables.includes('products')) {
            warnings.push('Consider adding indexes on products.barcode and products.category for better performance');
        }
        return warnings;
    }
    findPlaceholderComponents(generatedCode) {
        const placeholders = [];
        Object.entries(generatedCode.files).forEach(([filePath, content]) => {
            if (filePath.includes('frontend/') && filePath.endsWith('.tsx')) {
                // Look for placeholder patterns
                const placeholderPatterns = [
                    /Track warranty information for parts/,
                    /Manage inventory and stock levels/,
                    /Search vehicles and check compatibility/,
                    /<p>.*placeholder.*<\/p>/i,
                    /<div>.*coming soon.*<\/div>/i
                ];
                const hasPlaceholder = placeholderPatterns.some(pattern => pattern.test(content));
                const hasMinimalContent = content.split('\n').length < 20; // Very short components
                if (hasPlaceholder || hasMinimalContent) {
                    const fileName = filePath.split('/').pop()?.replace('.tsx', '') || 'Unknown';
                    placeholders.push({
                        name: fileName,
                        file: filePath
                    });
                }
            }
        });
        return placeholders;
    }
    findIncompleteServices(generatedCode) {
        const incomplete = [];
        Object.entries(generatedCode.files).forEach(([filePath, content]) => {
            if (filePath.includes('backend/') && filePath.includes('services/') && filePath.endsWith('.ts')) {
                // Look for empty methods or TODO comments
                if (content.includes('// TODO') || content.includes('throw new Error') || content.split('\n').length < 30) {
                    const fileName = filePath.split('/').pop()?.replace('.ts', '') || 'Unknown';
                    incomplete.push({
                        name: fileName,
                        file: filePath
                    });
                }
            }
        });
        return incomplete;
    }
    findTodoComments(generatedCode) {
        const todos = [];
        Object.entries(generatedCode.files).forEach(([filePath, content]) => {
            const lines = content.split('\n');
            lines.forEach((line, index) => {
                if (line.includes('TODO') || line.includes('FIXME')) {
                    todos.push({
                        file: `${filePath}:${index + 1}`,
                        comment: line.trim()
                    });
                }
            });
        });
        return todos;
    }
}
exports.EnhancedQualityGates = EnhancedQualityGates;
//# sourceMappingURL=EnhancedQualityGates.js.map
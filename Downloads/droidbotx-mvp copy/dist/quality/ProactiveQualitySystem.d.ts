import { WorkflowPhase, WorkflowRequest } from '../orchestration/Orchestrator';
import { AgentResult } from '../core/BaseAgent';
export interface QualityPlan {
    phase: WorkflowPhase;
    qualityTargets: QualityTarget[];
    preventiveChecks: PreventiveCheck[];
    qualityGates: QualityGate[];
    expectedOutcomes: ExpectedOutcome[];
    riskMitigation: RiskMitigation[];
}
export interface QualityTarget {
    name: string;
    description: string;
    metric: string;
    targetValue: number;
    priority: 'critical' | 'high' | 'medium' | 'low';
}
export interface PreventiveCheck {
    name: string;
    description: string;
    checkFunction: (context: any) => Promise<boolean>;
    failureAction: 'abort' | 'warn' | 'fix';
    fixFunction?: (context: any) => Promise<any>;
}
export interface QualityGate {
    name: string;
    description: string;
    validator: (result: AgentResult) => Promise<QualityValidationResult>;
    threshold: number;
    blocking: boolean;
}
export interface ExpectedOutcome {
    name: string;
    description: string;
    validator: (result: AgentResult) => boolean;
    importance: 'must-have' | 'should-have' | 'nice-to-have';
}
export interface RiskMitigation {
    risk: string;
    probability: 'high' | 'medium' | 'low';
    impact: 'critical' | 'high' | 'medium' | 'low';
    mitigation: string;
    preventiveAction: () => Promise<void>;
}
export interface QualityValidationResult {
    success: boolean;
    score: number;
    issues: QualityIssue[];
    recommendations: string[];
    metrics: QualityMetrics;
}
export interface QualityIssue {
    type: 'error' | 'warning' | 'info';
    category: 'compilation' | 'structure' | 'consistency' | 'performance' | 'security';
    description: string;
    location?: string;
    severity: 'critical' | 'high' | 'medium' | 'low';
    autoFixable: boolean;
    suggestedFix?: string;
}
export interface QualityMetrics {
    compilationScore: number;
    structureScore: number;
    consistencyScore: number;
    performanceScore: number;
    securityScore: number;
    overallScore: number;
}
export interface QualityCorrectionResult {
    success: boolean;
    data: any;
    correctionsApplied: string[];
    remainingIssues: QualityIssue[];
}
export declare class ProactiveQualitySystem {
    private logger;
    private qualityPlans;
    constructor();
    /**
     * Generate quality plan for a phase
     */
    generateQualityPlan(phase: WorkflowPhase, request: WorkflowRequest): Promise<QualityPlan>;
    /**
     * Validate result against quality plan
     */
    validateResult(phase: WorkflowPhase, result: AgentResult, qualityPlan: QualityPlan): Promise<QualityValidationResult>;
    /**
     * Apply quality corrections to result
     */
    applyQualityCorrections(result: AgentResult, validation: QualityValidationResult): Promise<QualityCorrectionResult>;
    /**
     * Initialize quality plans for all phases
     */
    private initializeQualityPlans;
    private addRemainingQualityPlans;
    /**
     * Customize quality plan based on request characteristics
     */
    private customizeQualityPlan;
    private assessRequestComplexity;
    private validateTechnicalSpecification;
    private validateBusinessLogic;
    private validateGeneratedCode;
    private validateTestingResults;
    private updateMetrics;
    private calculateOverallScore;
    private applyFix;
}
//# sourceMappingURL=ProactiveQualitySystem.d.ts.map
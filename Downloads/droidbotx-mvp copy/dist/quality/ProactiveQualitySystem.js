"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProactiveQualitySystem = void 0;
const Logger_1 = require("../core/Logger");
const Orchestrator_1 = require("../orchestration/Orchestrator");
class ProactiveQualitySystem {
    constructor() {
        this.qualityPlans = new Map();
        this.logger = Logger_1.Logger.getInstance();
        this.initializeQualityPlans();
    }
    /**
     * Generate quality plan for a phase
     */
    async generateQualityPlan(phase, request) {
        this.logger.debug('Generating quality plan', { phase });
        const basePlan = this.qualityPlans.get(phase);
        if (!basePlan) {
            throw new Error(`No quality plan template found for phase: ${phase}`);
        }
        // Customize plan based on request characteristics
        const customizedPlan = await this.customizeQualityPlan(basePlan, request);
        this.logger.debug('Quality plan generated', {
            phase,
            targets: customizedPlan.qualityTargets.length,
            checks: customizedPlan.preventiveChecks.length,
            gates: customizedPlan.qualityGates.length
        });
        return customizedPlan;
    }
    /**
     * Validate result against quality plan
     */
    async validateResult(phase, result, qualityPlan) {
        this.logger.debug('Validating result against quality plan', { phase });
        const issues = [];
        const recommendations = [];
        const metrics = {
            compilationScore: 0,
            structureScore: 0,
            consistencyScore: 0,
            performanceScore: 0,
            securityScore: 0,
            overallScore: 0
        };
        // Run quality gates
        for (const gate of qualityPlan.qualityGates) {
            try {
                const gateResult = await gate.validator(result);
                if (!gateResult.success) {
                    issues.push(...gateResult.issues);
                    recommendations.push(...gateResult.recommendations);
                }
                // Update metrics
                this.updateMetrics(metrics, gateResult.metrics);
            }
            catch (error) {
                issues.push({
                    type: 'error',
                    category: 'structure',
                    description: `Quality gate '${gate.name}' failed: ${error instanceof Error ? error.message : String(error)}`,
                    severity: 'high',
                    autoFixable: false
                });
            }
        }
        // Check expected outcomes
        for (const outcome of qualityPlan.expectedOutcomes) {
            try {
                const isAchieved = outcome.validator(result);
                if (!isAchieved) {
                    const severity = outcome.importance === 'must-have' ? 'critical' :
                        outcome.importance === 'should-have' ? 'high' : 'medium';
                    issues.push({
                        type: 'warning',
                        category: 'structure',
                        description: `Expected outcome not achieved: ${outcome.description}`,
                        severity,
                        autoFixable: false
                    });
                }
            }
            catch (error) {
                issues.push({
                    type: 'error',
                    category: 'structure',
                    description: `Outcome validation failed for '${outcome.name}': ${error instanceof Error ? error.message : String(error)}`,
                    severity: 'medium',
                    autoFixable: false
                });
            }
        }
        // Calculate overall score
        metrics.overallScore = this.calculateOverallScore(metrics);
        const validationResult = {
            success: issues.filter(i => i.severity === 'critical').length === 0,
            score: metrics.overallScore,
            issues,
            recommendations,
            metrics
        };
        this.logger.debug('Quality validation completed', {
            phase,
            success: validationResult.success,
            score: validationResult.score,
            issues: issues.length
        });
        return validationResult;
    }
    /**
     * Apply quality corrections to result
     */
    async applyQualityCorrections(result, validation) {
        this.logger.debug('Applying quality corrections', {
            issues: validation.issues.length
        });
        const correctionsApplied = [];
        const remainingIssues = [];
        let correctedData = { ...result.data };
        for (const issue of validation.issues) {
            if (issue.autoFixable && issue.suggestedFix) {
                try {
                    correctedData = await this.applyFix(correctedData, issue);
                    correctionsApplied.push(issue.description);
                }
                catch (error) {
                    this.logger.warn('Failed to apply quality correction', {
                        issue: issue.description,
                        error: error instanceof Error ? error.message : String(error)
                    });
                    remainingIssues.push(issue);
                }
            }
            else {
                remainingIssues.push(issue);
            }
        }
        const correctionResult = {
            success: correctionsApplied.length > 0,
            data: correctedData,
            correctionsApplied,
            remainingIssues
        };
        this.logger.debug('Quality corrections applied', {
            applied: correctionsApplied.length,
            remaining: remainingIssues.length
        });
        return correctionResult;
    }
    /**
     * Initialize quality plans for all phases
     */
    initializeQualityPlans() {
        // PLAN phase quality plan
        this.qualityPlans.set(Orchestrator_1.WorkflowPhase.PLAN, {
            phase: Orchestrator_1.WorkflowPhase.PLAN,
            qualityTargets: [
                {
                    name: 'specification-completeness',
                    description: 'Technical specification should be complete',
                    metric: 'completeness_score',
                    targetValue: 90,
                    priority: 'critical'
                }
            ],
            preventiveChecks: [
                {
                    name: 'requirements-validation',
                    description: 'Validate requirements are specific and actionable',
                    checkFunction: async (context) => {
                        const requirements = context.request?.requirements || [];
                        return requirements.length >= 3 && requirements.every((req) => req.length > 10);
                    },
                    failureAction: 'warn'
                }
            ],
            qualityGates: [
                {
                    name: 'technical-spec-gate',
                    description: 'Validate technical specification quality',
                    validator: async (result) => this.validateTechnicalSpecification(result),
                    threshold: 80,
                    blocking: true
                }
            ],
            expectedOutcomes: [
                {
                    name: 'project-name-defined',
                    description: 'Project should have a clear name',
                    validator: (result) => !!result.data?.projectName,
                    importance: 'must-have'
                },
                {
                    name: 'business-domain-identified',
                    description: 'Business domain should be identified',
                    validator: (result) => !!result.data?.businessDomain,
                    importance: 'should-have'
                }
            ],
            riskMitigation: []
        });
        // BUSINESS_LOGIC phase quality plan
        this.qualityPlans.set(Orchestrator_1.WorkflowPhase.BUSINESS_LOGIC, {
            phase: Orchestrator_1.WorkflowPhase.BUSINESS_LOGIC,
            qualityTargets: [
                {
                    name: 'api-coverage',
                    description: 'Should generate comprehensive API coverage',
                    metric: 'api_count',
                    targetValue: 5,
                    priority: 'high'
                },
                {
                    name: 'database-completeness',
                    description: 'Database schema should be complete',
                    metric: 'table_count',
                    targetValue: 3,
                    priority: 'high'
                }
            ],
            preventiveChecks: [
                {
                    name: 'domain-consistency',
                    description: 'Check for domain consistency in generated APIs',
                    checkFunction: async (context) => {
                        const domainAPIs = context.domainAPIs || {};
                        return Object.keys(domainAPIs).length > 0;
                    },
                    failureAction: 'warn'
                }
            ],
            qualityGates: [
                {
                    name: 'business-logic-gate',
                    description: 'Validate business logic completeness',
                    validator: async (result) => this.validateBusinessLogic(result),
                    threshold: 85,
                    blocking: true
                }
            ],
            expectedOutcomes: [
                {
                    name: 'apis-generated',
                    description: 'Domain APIs should be generated',
                    validator: (result) => {
                        const apis = result.data?.domainAPIs || {};
                        return Object.keys(apis).length > 0;
                    },
                    importance: 'must-have'
                },
                {
                    name: 'database-schema-defined',
                    description: 'Database schema should be defined',
                    validator: (result) => {
                        const schema = result.data?.databaseSchema;
                        return schema?.tables?.length > 0;
                    },
                    importance: 'must-have'
                }
            ],
            riskMitigation: []
        });
        // GENERATE phase quality plan
        this.qualityPlans.set(Orchestrator_1.WorkflowPhase.GENERATE, {
            phase: Orchestrator_1.WorkflowPhase.GENERATE,
            qualityTargets: [
                {
                    name: 'file-generation',
                    description: 'Should generate comprehensive file set',
                    metric: 'file_count',
                    targetValue: 20,
                    priority: 'high'
                },
                {
                    name: 'compilation-readiness',
                    description: 'Generated code should be compilation-ready',
                    metric: 'compilation_score',
                    targetValue: 95,
                    priority: 'critical'
                }
            ],
            preventiveChecks: [
                {
                    name: 'file-structure-validation',
                    description: 'Validate proper file structure',
                    checkFunction: async (context) => {
                        const files = context.files || {};
                        const hasBackend = Object.keys(files).some(path => path.includes('backend/'));
                        const hasFrontend = Object.keys(files).some(path => path.includes('frontend/'));
                        return hasBackend && hasFrontend;
                    },
                    failureAction: 'abort'
                }
            ],
            qualityGates: [
                {
                    name: 'code-generation-gate',
                    description: 'Validate generated code quality',
                    validator: async (result) => this.validateGeneratedCode(result),
                    threshold: 90,
                    blocking: true
                }
            ],
            expectedOutcomes: [
                {
                    name: 'full-stack-structure',
                    description: 'Should have complete full-stack structure',
                    validator: (result) => {
                        const files = result.data?.files || {};
                        const paths = Object.keys(files);
                        return paths.some(p => p.includes('backend/')) && paths.some(p => p.includes('frontend/'));
                    },
                    importance: 'must-have'
                }
            ],
            riskMitigation: []
        });
        // Add quality plans for other phases...
        this.addRemainingQualityPlans();
    }
    addRemainingQualityPlans() {
        // TESTING phase quality plan
        this.qualityPlans.set(Orchestrator_1.WorkflowPhase.TESTING, {
            phase: Orchestrator_1.WorkflowPhase.TESTING,
            qualityTargets: [
                {
                    name: 'test-pass-rate',
                    description: 'Tests should have high pass rate',
                    metric: 'test_pass_rate',
                    targetValue: 80,
                    priority: 'critical'
                }
            ],
            preventiveChecks: [],
            qualityGates: [
                {
                    name: 'testing-gate',
                    description: 'Validate testing results',
                    validator: async (result) => this.validateTestingResults(result),
                    threshold: 75,
                    blocking: false
                }
            ],
            expectedOutcomes: [
                {
                    name: 'compilation-success',
                    description: 'Code should compile successfully',
                    validator: (result) => {
                        const testResult = result.data?.testingResult;
                        return testResult?.compilationErrors?.length === 0;
                    },
                    importance: 'must-have'
                }
            ],
            riskMitigation: []
        });
    }
    /**
     * Customize quality plan based on request characteristics
     */
    async customizeQualityPlan(basePlan, request) {
        // Clone the base plan
        const customPlan = JSON.parse(JSON.stringify(basePlan));
        // Adjust targets based on request complexity
        const complexity = this.assessRequestComplexity(request);
        if (complexity === 'high') {
            // Increase targets for complex requests
            customPlan.qualityTargets.forEach(target => {
                target.targetValue = Math.min(100, target.targetValue + 10);
            });
        }
        else if (complexity === 'low') {
            // Relax targets for simple requests
            customPlan.qualityTargets.forEach(target => {
                target.targetValue = Math.max(50, target.targetValue - 10);
            });
        }
        return customPlan;
    }
    assessRequestComplexity(request) {
        const requirements = request.requirements || [];
        const complexityIndicators = [
            'payment', 'integration', 'real-time', 'analytics', 'reporting',
            'multi-tenant', 'microservices', 'oauth', 'websocket'
        ];
        const complexFeatures = requirements.filter(req => complexityIndicators.some(indicator => req.toLowerCase().includes(indicator))).length;
        if (complexFeatures >= 3)
            return 'high';
        if (complexFeatures >= 1)
            return 'medium';
        return 'low';
    }
    // Quality validation methods
    async validateTechnicalSpecification(result) {
        const issues = [];
        const recommendations = [];
        const spec = result.data;
        let score = 100;
        if (!spec?.projectName) {
            issues.push({
                type: 'error',
                category: 'structure',
                description: 'Project name is missing',
                severity: 'critical',
                autoFixable: false
            });
            score -= 20;
        }
        if (!spec?.description) {
            issues.push({
                type: 'error',
                category: 'structure',
                description: 'Project description is missing',
                severity: 'high',
                autoFixable: false
            });
            score -= 15;
        }
        return {
            success: issues.filter(i => i.severity === 'critical').length === 0,
            score: Math.max(0, score),
            issues,
            recommendations,
            metrics: {
                compilationScore: score,
                structureScore: score,
                consistencyScore: score,
                performanceScore: score,
                securityScore: score,
                overallScore: score
            }
        };
    }
    async validateBusinessLogic(result) {
        const issues = [];
        const recommendations = [];
        const data = result.data;
        let score = 100;
        if (!data?.domainAPIs || Object.keys(data.domainAPIs).length === 0) {
            issues.push({
                type: 'error',
                category: 'structure',
                description: 'No domain APIs generated',
                severity: 'critical',
                autoFixable: false
            });
            score -= 30;
        }
        if (!data?.databaseSchema?.tables || data.databaseSchema.tables.length === 0) {
            issues.push({
                type: 'error',
                category: 'structure',
                description: 'No database tables defined',
                severity: 'critical',
                autoFixable: false
            });
            score -= 30;
        }
        return {
            success: issues.filter(i => i.severity === 'critical').length === 0,
            score: Math.max(0, score),
            issues,
            recommendations,
            metrics: {
                compilationScore: score,
                structureScore: score,
                consistencyScore: score,
                performanceScore: score,
                securityScore: score,
                overallScore: score
            }
        };
    }
    async validateGeneratedCode(result) {
        const issues = [];
        const recommendations = [];
        const files = result.data?.files || {};
        let score = 100;
        if (Object.keys(files).length < 10) {
            issues.push({
                type: 'warning',
                category: 'structure',
                description: 'Low number of generated files',
                severity: 'medium',
                autoFixable: false
            });
            score -= 10;
        }
        const hasBackend = Object.keys(files).some(path => path.includes('backend/'));
        const hasFrontend = Object.keys(files).some(path => path.includes('frontend/'));
        if (!hasBackend) {
            issues.push({
                type: 'error',
                category: 'structure',
                description: 'No backend files generated',
                severity: 'critical',
                autoFixable: false
            });
            score -= 40;
        }
        if (!hasFrontend) {
            issues.push({
                type: 'error',
                category: 'structure',
                description: 'No frontend files generated',
                severity: 'critical',
                autoFixable: false
            });
            score -= 40;
        }
        return {
            success: issues.filter(i => i.severity === 'critical').length === 0,
            score: Math.max(0, score),
            issues,
            recommendations,
            metrics: {
                compilationScore: score,
                structureScore: score,
                consistencyScore: score,
                performanceScore: score,
                securityScore: score,
                overallScore: score
            }
        };
    }
    async validateTestingResults(result) {
        const issues = [];
        const recommendations = [];
        const testResult = result.data?.testingResult;
        let score = 100;
        if (testResult?.compilationErrors?.length > 0) {
            issues.push({
                type: 'error',
                category: 'compilation',
                description: `${testResult.compilationErrors.length} compilation errors found`,
                severity: 'critical',
                autoFixable: false
            });
            score -= 50;
        }
        const passRate = testResult?.testsPassed / Math.max(testResult?.testsRun, 1) * 100;
        if (passRate < 80) {
            issues.push({
                type: 'warning',
                category: 'structure',
                description: `Low test pass rate: ${passRate.toFixed(1)}%`,
                severity: 'medium',
                autoFixable: false
            });
            score -= 20;
        }
        return {
            success: issues.filter(i => i.severity === 'critical').length === 0,
            score: Math.max(0, score),
            issues,
            recommendations,
            metrics: {
                compilationScore: testResult?.compilationErrors?.length === 0 ? 100 : 0,
                structureScore: score,
                consistencyScore: score,
                performanceScore: score,
                securityScore: score,
                overallScore: score
            }
        };
    }
    updateMetrics(target, source) {
        target.compilationScore = Math.max(target.compilationScore, source.compilationScore);
        target.structureScore = Math.max(target.structureScore, source.structureScore);
        target.consistencyScore = Math.max(target.consistencyScore, source.consistencyScore);
        target.performanceScore = Math.max(target.performanceScore, source.performanceScore);
        target.securityScore = Math.max(target.securityScore, source.securityScore);
    }
    calculateOverallScore(metrics) {
        return Math.round((metrics.compilationScore + metrics.structureScore + metrics.consistencyScore +
            metrics.performanceScore + metrics.securityScore) / 5);
    }
    async applyFix(data, issue) {
        // Simple fix application - in a real implementation, this would be more sophisticated
        return data;
    }
}
exports.ProactiveQualitySystem = ProactiveQualitySystem;
//# sourceMappingURL=ProactiveQualitySystem.js.map
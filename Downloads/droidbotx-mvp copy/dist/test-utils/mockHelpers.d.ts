/**
 * Mock Helpers for DroidBotX Phase 3 Testing
 * Provides utility functions for creating mock data in tests
 */
import { BusinessEntity, BusinessDomainAnalysis } from '../core/SemanticAnalyzer';
export interface MockTechnicalSpec {
    projectName: string;
    businessDomain: string;
    architecture: any;
}
/**
 * Create a mock business entity for testing
 */
export declare function createMockEntity(overrides?: Partial<BusinessEntity>): BusinessEntity;
/**
 * Create a mock semantic analysis for testing
 */
export declare function createMockSemanticAnalysis(overrides?: Partial<BusinessDomainAnalysis>): BusinessDomainAnalysis;
/**
 * Create a mock technical specification for testing
 */
export declare function createMockTechnicalSpec(overrides?: Partial<MockTechnicalSpec>): MockTechnicalSpec;
/**
 * Create mock test data for various scenarios
 */
export declare const mockTestData: {
    ecommerce: {
        entity: BusinessEntity;
        semanticAnalysis: BusinessDomainAnalysis;
    };
    education: {
        entity: BusinessEntity;
        semanticAnalysis: BusinessDomainAnalysis;
    };
    healthcare: {
        entity: BusinessEntity;
        semanticAnalysis: BusinessDomainAnalysis;
    };
};
/**
 * Create mock Jest functions for testing
 */
export declare const createMockJestFunctions: () => {
    fn: typeof jest.fn;
    mock: typeof jest.mock;
    spyOn: typeof jest.spyOn;
    clearAllMocks: typeof jest.clearAllMocks;
    resetAllMocks: typeof jest.resetAllMocks;
    restoreAllMocks: typeof jest.restoreAllMocks;
};
/**
 * Create mock HTTP response for testing
 */
export declare function createMockResponse(data?: any, status?: number): {
    status: number;
    data: any;
    headers: {};
    config: {};
    statusText: string;
};
/**
 * Create mock database query result
 */
export declare function createMockQueryResult(rows?: any[], rowCount?: number): {
    rows: any[];
    rowCount: number;
    command: string;
    oid: number;
    fields: never[];
};
/**
 * Create mock file system operations
 */
export declare const createMockFS: () => {
    writeFile: jest.Mock<any, any, any>;
    readFile: jest.Mock<any, any, any>;
    mkdir: jest.Mock<any, any, any>;
    access: jest.Mock<any, any, any>;
    existsSync: jest.Mock<any, any, any>;
    writeFileSync: jest.Mock<any, any, any>;
    readFileSync: jest.Mock<any, any, any>;
    mkdirSync: jest.Mock<any, any, any>;
};
/**
 * Utility function to wait for async operations in tests
 */
export declare function waitFor(ms: number): Promise<void>;
/**
 * Create mock test environment
 */
export declare function createTestEnvironment(): {
    mockEntity: BusinessEntity;
    mockSemanticAnalysis: BusinessDomainAnalysis;
    mockTechnicalSpec: MockTechnicalSpec;
    mockJest: {
        fn: typeof jest.fn;
        mock: typeof jest.mock;
        spyOn: typeof jest.spyOn;
        clearAllMocks: typeof jest.clearAllMocks;
        resetAllMocks: typeof jest.resetAllMocks;
        restoreAllMocks: typeof jest.restoreAllMocks;
    };
    mockFS: {
        writeFile: jest.Mock<any, any, any>;
        readFile: jest.Mock<any, any, any>;
        mkdir: jest.Mock<any, any, any>;
        access: jest.Mock<any, any, any>;
        existsSync: jest.Mock<any, any, any>;
        writeFileSync: jest.Mock<any, any, any>;
        readFileSync: jest.Mock<any, any, any>;
        mkdirSync: jest.Mock<any, any, any>;
    };
    waitFor: typeof waitFor;
};
//# sourceMappingURL=mockHelpers.d.ts.map
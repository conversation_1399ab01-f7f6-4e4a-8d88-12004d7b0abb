"use strict";
/**
 * Mock Helpers for DroidBotX Phase 3 Testing
 * Provides utility functions for creating mock data in tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMockFS = exports.createMockJestFunctions = exports.mockTestData = void 0;
exports.createMockEntity = createMockEntity;
exports.createMockSemanticAnalysis = createMockSemanticAnalysis;
exports.createMockTechnicalSpec = createMockTechnicalSpec;
exports.createMockResponse = createMockResponse;
exports.createMockQueryResult = createMockQueryResult;
exports.waitFor = waitFor;
exports.createTestEnvironment = createTestEnvironment;
/**
 * Create a mock business entity for testing
 */
function createMockEntity(overrides = {}) {
    return {
        name: 'TestEntity',
        description: 'Test entity for unit testing',
        fields: [
            { name: 'id', type: 'uuid', required: true, description: 'Entity ID' },
            { name: 'name', type: 'string', required: true, description: 'Entity name' },
            { name: 'email', type: 'string', required: false, description: 'Entity email' }
        ],
        relationships: [
            { type: 'oneToMany', target: 'RelatedEntity', description: 'Related entities' }
        ],
        operations: [
            { name: 'create', type: 'create', description: 'Create entity', parameters: [], businessRules: [] },
            { name: 'findById', type: 'read', description: 'Find by ID', parameters: [], businessRules: [] },
            { name: 'update', type: 'update', description: 'Update entity', parameters: [], businessRules: [] },
            { name: 'delete', type: 'delete', description: 'Delete entity', parameters: [], businessRules: [] }
        ],
        validations: [
            { field: 'name', rule: 'required', message: 'Name is required' },
            { field: 'email', rule: 'email', message: 'Valid email required' }
        ],
        ...overrides
    };
}
/**
 * Create a mock semantic analysis for testing
 */
function createMockSemanticAnalysis(overrides = {}) {
    return {
        domain: 'test-domain',
        confidence: 0.9,
        entities: [createMockEntity()],
        workflows: [
            {
                name: 'TestWorkflow',
                description: 'Test workflow for testing',
                steps: [
                    { name: 'Step1', description: 'First step', action: 'create', conditions: [], nextSteps: ['Step2'] },
                    { name: 'Step2', description: 'Second step', action: 'validate', conditions: ['Step1'], nextSteps: [] }
                ],
                entities: ['TestEntity'],
                userRoles: ['user', 'admin']
            }
        ],
        userRoles: [
            { name: 'user', description: 'Regular user', permissions: [], accessLevel: 'user' },
            { name: 'admin', description: 'Administrator', permissions: [], accessLevel: 'admin' }
        ],
        technicalRequirements: [
            { category: 'security', requirement: 'Authentication required', priority: 'critical', implementation: 'JWT tokens' },
            { category: 'backend', requirement: 'Data validation needed', priority: 'high', implementation: 'Input validation middleware' },
            { category: 'backend', requirement: 'Performance optimization', priority: 'medium', implementation: 'Caching and indexing' }
        ],
        codeGenerationContext: {
            primaryFrameworks: {
                frontend: 'React',
                backend: 'Express',
                database: 'PostgreSQL'
            },
            architecturalPatterns: ['MVC', 'Repository Pattern'],
            securityRequirements: ['JWT', 'RBAC', 'Input Validation'],
            performanceRequirements: ['Caching', 'Database Indexing'],
            integrationRequirements: ['REST API', 'WebSocket']
        },
        ...overrides
    };
}
/**
 * Create a mock technical specification for testing
 */
function createMockTechnicalSpec(overrides = {}) {
    return {
        projectName: 'test-project',
        businessDomain: 'test-domain',
        architecture: {
            type: 'microservices',
            database: 'PostgreSQL',
            frontend: 'React',
            backend: 'Express'
        },
        ...overrides
    };
}
/**
 * Create mock test data for various scenarios
 */
exports.mockTestData = {
    // E-commerce domain
    ecommerce: {
        entity: createMockEntity({
            name: 'Product',
            description: 'E-commerce product entity',
            fields: [
                { name: 'id', type: 'uuid', required: true, description: 'Product ID' },
                { name: 'name', type: 'string', required: true, description: 'Product name' },
                { name: 'price', type: 'decimal', required: true, description: 'Product price' },
                { name: 'category', type: 'string', required: true, description: 'Product category' }
            ],
            relationships: [
                { type: 'manyToOne', target: 'Category', description: 'Product category' },
                { type: 'oneToMany', target: 'OrderItem', description: 'Order items' }
            ]
        }),
        semanticAnalysis: createMockSemanticAnalysis({
            domain: 'e-commerce',
            entities: [
                createMockEntity({ name: 'Product' }),
                createMockEntity({ name: 'Category' }),
                createMockEntity({ name: 'Order' })
            ]
        })
    },
    // Education domain
    education: {
        entity: createMockEntity({
            name: 'Course',
            description: 'Educational course entity',
            fields: [
                { name: 'id', type: 'uuid', required: true, description: 'Course ID' },
                { name: 'title', type: 'string', required: true, description: 'Course title' },
                { name: 'description', type: 'text', required: false, description: 'Course description' },
                { name: 'duration', type: 'number', required: true, description: 'Course duration in hours' }
            ],
            relationships: [
                { type: 'manyToOne', target: 'Instructor', description: 'Course instructor' },
                { type: 'oneToMany', target: 'Lesson', description: 'Course lessons' }
            ]
        }),
        semanticAnalysis: createMockSemanticAnalysis({
            domain: 'education',
            entities: [
                createMockEntity({ name: 'Course' }),
                createMockEntity({ name: 'Instructor' }),
                createMockEntity({ name: 'Student' })
            ]
        })
    },
    // Healthcare domain
    healthcare: {
        entity: createMockEntity({
            name: 'Patient',
            description: 'Healthcare patient entity',
            fields: [
                { name: 'id', type: 'uuid', required: true, description: 'Patient ID' },
                { name: 'firstName', type: 'string', required: true, description: 'Patient first name' },
                { name: 'lastName', type: 'string', required: true, description: 'Patient last name' },
                { name: 'dateOfBirth', type: 'date', required: true, description: 'Patient date of birth' }
            ],
            relationships: [
                { type: 'oneToMany', target: 'Appointment', description: 'Patient appointments' },
                { type: 'oneToMany', target: 'MedicalRecord', description: 'Patient medical records' }
            ]
        }),
        semanticAnalysis: createMockSemanticAnalysis({
            domain: 'healthcare',
            entities: [
                createMockEntity({ name: 'Patient' }),
                createMockEntity({ name: 'Doctor' }),
                createMockEntity({ name: 'Appointment' })
            ]
        })
    }
};
/**
 * Create mock Jest functions for testing
 */
const createMockJestFunctions = () => ({
    fn: jest.fn,
    mock: jest.mock,
    spyOn: jest.spyOn,
    clearAllMocks: jest.clearAllMocks,
    resetAllMocks: jest.resetAllMocks,
    restoreAllMocks: jest.restoreAllMocks
});
exports.createMockJestFunctions = createMockJestFunctions;
/**
 * Create mock HTTP response for testing
 */
function createMockResponse(data = {}, status = 200) {
    return {
        status,
        data,
        headers: {},
        config: {},
        statusText: status === 200 ? 'OK' : 'Error'
    };
}
/**
 * Create mock database query result
 */
function createMockQueryResult(rows = [], rowCount) {
    return {
        rows,
        rowCount: rowCount !== undefined ? rowCount : rows.length,
        command: 'SELECT',
        oid: 0,
        fields: []
    };
}
/**
 * Create mock file system operations
 */
const createMockFS = () => ({
    writeFile: jest.fn().mockResolvedValue(undefined),
    readFile: jest.fn().mockResolvedValue(''),
    mkdir: jest.fn().mockResolvedValue(undefined),
    access: jest.fn().mockResolvedValue(undefined),
    existsSync: jest.fn().mockReturnValue(true),
    writeFileSync: jest.fn(),
    readFileSync: jest.fn().mockReturnValue(''),
    mkdirSync: jest.fn()
});
exports.createMockFS = createMockFS;
/**
 * Utility function to wait for async operations in tests
 */
function waitFor(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * Create mock test environment
 */
function createTestEnvironment() {
    return {
        mockEntity: createMockEntity(),
        mockSemanticAnalysis: createMockSemanticAnalysis(),
        mockTechnicalSpec: createMockTechnicalSpec(),
        mockJest: (0, exports.createMockJestFunctions)(),
        mockFS: (0, exports.createMockFS)(),
        waitFor
    };
}
//# sourceMappingURL=mockHelpers.js.map
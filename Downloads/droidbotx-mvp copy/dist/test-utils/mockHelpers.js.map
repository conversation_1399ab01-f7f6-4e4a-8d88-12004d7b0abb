{"version": 3, "file": "mockHelpers.js", "sourceRoot": "", "sources": ["../../src/test-utils/mockHelpers.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAaH,4CAwBC;AAKD,gEAuCC;AAKD,0DAYC;AAoGD,gDAQC;AAKD,sDAQC;AAmBD,0BAEC;AAKD,sDASC;AApPD;;GAEG;AACH,SAAgB,gBAAgB,CAAC,YAAqC,EAAE;IACtE,OAAO;QACL,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,8BAA8B;QAC3C,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE;YACtE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,aAAa,EAAE;YAC5E,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE;SAChF;QACD,aAAa,EAAE;YACb,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE;SAChF;QACD,UAAU,EAAE;YACV,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE;YACnG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE;YAChG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE;YACnG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE;SACpG;QACD,WAAW,EAAE;YACX,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,kBAAkB,EAAE;YAChE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,sBAAsB,EAAE;SACnE;QACD,GAAG,SAAS;KACb,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CAAC,YAA6C,EAAE;IACxF,OAAO;QACL,MAAM,EAAE,aAAa;QACrB,UAAU,EAAE,GAAG;QACf,QAAQ,EAAE,CAAC,gBAAgB,EAAE,CAAC;QAC9B,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,2BAA2B;gBACxC,KAAK,EAAE;oBACL,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC,EAAE;oBACpG,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;iBACxG;gBACD,QAAQ,EAAE,CAAC,YAAY,CAAC;gBACxB,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;aAC7B;SACF;QACD,SAAS,EAAE;YACT,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;YACnF,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE;SACvF;QACD,qBAAqB,EAAE;YACrB,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,yBAAyB,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,EAAE;YACpH,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,wBAAwB,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,6BAA6B,EAAE;YAC/H,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,0BAA0B,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,sBAAsB,EAAE;SAC7H;QACD,qBAAqB,EAAE;YACrB,iBAAiB,EAAE;gBACjB,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE,SAAS;gBAClB,QAAQ,EAAE,YAAY;aACvB;YACD,qBAAqB,EAAE,CAAC,KAAK,EAAE,oBAAoB,CAAC;YACpD,oBAAoB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,kBAAkB,CAAC;YACzD,uBAAuB,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC;YACzD,uBAAuB,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;SACnD;QACD,GAAG,SAAS;KACb,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CAAC,YAAwC,EAAE;IAChF,OAAO;QACL,WAAW,EAAE,cAAc;QAC3B,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE;YACZ,IAAI,EAAE,eAAe;YACrB,QAAQ,EAAE,YAAY;YACtB,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,SAAS;SACnB;QACD,GAAG,SAAS;KACb,CAAC;AACJ,CAAC;AAED;;GAEG;AACU,QAAA,YAAY,GAAG;IAC1B,oBAAoB;IACpB,SAAS,EAAE;QACT,MAAM,EAAE,gBAAgB,CAAC;YACvB,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,2BAA2B;YACxC,MAAM,EAAE;gBACN,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE;gBACvE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE;gBAC7E,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE;gBAChF,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE;aACtF;YACD,aAAa,EAAE;gBACb,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAE;gBAC1E,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE;aACvE;SACF,CAAC;QACF,gBAAgB,EAAE,0BAA0B,CAAC;YAC3C,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE;gBACR,gBAAgB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gBACrC,gBAAgB,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;gBACtC,gBAAgB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;aACpC;SACF,CAAC;KACH;IAED,mBAAmB;IACnB,SAAS,EAAE;QACT,MAAM,EAAE,gBAAgB,CAAC;YACvB,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,2BAA2B;YACxC,MAAM,EAAE;gBACN,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE;gBACtE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE;gBAC9E,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,oBAAoB,EAAE;gBACzF,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,0BAA0B,EAAE;aAC9F;YACD,aAAa,EAAE;gBACb,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,mBAAmB,EAAE;gBAC7E,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE;aACvE;SACF,CAAC;QACF,gBAAgB,EAAE,0BAA0B,CAAC;YAC3C,MAAM,EAAE,WAAW;YACnB,QAAQ,EAAE;gBACR,gBAAgB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;gBACpC,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;gBACxC,gBAAgB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;aACtC;SACF,CAAC;KACH;IAED,oBAAoB;IACpB,UAAU,EAAE;QACV,MAAM,EAAE,gBAAgB,CAAC;YACvB,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,2BAA2B;YACxC,MAAM,EAAE;gBACN,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE;gBACvE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,oBAAoB,EAAE;gBACxF,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,mBAAmB,EAAE;gBACtF,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,uBAAuB,EAAE;aAC5F;YACD,aAAa,EAAE;gBACb,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,sBAAsB,EAAE;gBACjF,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,yBAAyB,EAAE;aACvF;SACF,CAAC;QACF,gBAAgB,EAAE,0BAA0B,CAAC;YAC3C,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE;gBACR,gBAAgB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gBACrC,gBAAgB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;gBACpC,gBAAgB,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;aAC1C;SACF,CAAC;KACH;CACF,CAAC;AAEF;;GAEG;AACI,MAAM,uBAAuB,GAAG,GAAG,EAAE,CAAC,CAAC;IAC5C,EAAE,EAAE,IAAI,CAAC,EAAE;IACX,IAAI,EAAE,IAAI,CAAC,IAAI;IACf,KAAK,EAAE,IAAI,CAAC,KAAK;IACjB,aAAa,EAAE,IAAI,CAAC,aAAa;IACjC,aAAa,EAAE,IAAI,CAAC,aAAa;IACjC,eAAe,EAAE,IAAI,CAAC,eAAe;CACtC,CAAC,CAAC;AAPU,QAAA,uBAAuB,2BAOjC;AAEH;;GAEG;AACH,SAAgB,kBAAkB,CAAC,OAAY,EAAE,EAAE,SAAiB,GAAG;IACrE,OAAO;QACL,MAAM;QACN,IAAI;QACJ,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,EAAE;QACV,UAAU,EAAE,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO;KAC5C,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,OAAc,EAAE,EAAE,QAAiB;IACvE,OAAO;QACL,IAAI;QACJ,QAAQ,EAAE,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM;QACzD,OAAO,EAAE,QAAQ;QACjB,GAAG,EAAE,CAAC;QACN,MAAM,EAAE,EAAE;KACX,CAAC;AACJ,CAAC;AAED;;GAEG;AACI,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,CAAC;IACjC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IACjD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;IACzC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IAC7C,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IAC9C,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;IAC3C,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;IACxB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;IAC3C,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;CACrB,CAAC,CAAC;AATU,QAAA,YAAY,gBAStB;AAEH;;GAEG;AACH,SAAgB,OAAO,CAAC,EAAU;IAChC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB;IACnC,OAAO;QACL,UAAU,EAAE,gBAAgB,EAAE;QAC9B,oBAAoB,EAAE,0BAA0B,EAAE;QAClD,iBAAiB,EAAE,uBAAuB,EAAE;QAC5C,QAAQ,EAAE,IAAA,+BAAuB,GAAE;QACnC,MAAM,EAAE,IAAA,oBAAY,GAAE;QACtB,OAAO;KACR,CAAC;AACJ,CAAC"}
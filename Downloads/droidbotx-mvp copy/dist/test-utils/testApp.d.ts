/**
 * Test App Utility for Integration Testing
 * Creates a mock Express app for testing API endpoints
 */
export declare function createTestApp(): {
    app: {
        use: jest.<PERSON>ck<any, any, any>;
        get: jest.<PERSON>ck<any, any, any>;
        post: jest.<PERSON>ck<any, any, any>;
        put: jest.Mock<any, any, any>;
        delete: jest.Mock<any, any, any>;
        listen: jest.Mock<any, any, any>;
        set: jest.Mock<any, any, any>;
    };
    request: {
        body: {};
        params: {};
        query: {};
        headers: {};
        user: {
            id: string;
            role: string;
        };
    };
    response: {
        status: jest.Mock<any, any, any>;
        json: jest.Mock<any, any, any>;
        send: jest.Mock<any, any, any>;
        cookie: jest.Mock<any, any, any>;
        header: jest.Mock<any, any, any>;
    };
};
/**
 * Mock supertest for integration testing
 */
export declare function createMockSupertest(app: any): jest.Mock<{
    get: jest.<PERSON>ck<any, any, any>;
    post: jest.<PERSON>ck<any, any, any>;
    put: jest.<PERSON>ck<any, any, any>;
    delete: jest.<PERSON>ck<any, any, any>;
    send: jest.<PERSON>ck<any, any, any>;
    set: jest.Mock<any, any, any>;
    expect: jest.Mock<any, any, any>;
    end: jest.Mock<void, [callback: any], any>;
}, [], any>;
/**
 * Create mock authentication middleware
 */
export declare function createMockAuthMiddleware(): jest.Mock<void, [req: any, res: any, next: any], any>;
/**
 * Create mock validation middleware
 */
export declare function createMockValidationMiddleware(): jest.Mock<void, [req: any, res: any, next: any], any>;
//# sourceMappingURL=testApp.d.ts.map
"use strict";
/**
 * Test App Utility for Integration Testing
 * Creates a mock Express app for testing API endpoints
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTestApp = createTestApp;
exports.createMockSupertest = createMockSupertest;
exports.createMockAuthMiddleware = createMockAuthMiddleware;
exports.createMockValidationMiddleware = createMockValidationMiddleware;
// Mock Express app for testing
function createTestApp() {
    const mockApp = {
        use: jest.fn(),
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        listen: jest.fn(),
        set: jest.fn()
    };
    // Mock request and response objects
    const mockRequest = {
        body: {},
        params: {},
        query: {},
        headers: {},
        user: { id: 'test-user-id', role: 'user' }
    };
    const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis(),
        cookie: jest.fn().mockReturnThis(),
        header: jest.fn().mockReturnThis()
    };
    return {
        app: mockApp,
        request: mockRequest,
        response: mockResponse
    };
}
/**
 * Mock supertest for integration testing
 */
function createMockSupertest(app) {
    const mockRequest = {
        get: jest.fn().mockReturnThis(),
        post: jest.fn().mockReturnThis(),
        put: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        expect: jest.fn().mockReturnThis(),
        end: jest.fn((callback) => {
            if (callback) {
                callback(null, {
                    status: 200,
                    body: { success: true, data: {} }
                });
            }
        })
    };
    return jest.fn(() => mockRequest);
}
/**
 * Create mock authentication middleware
 */
function createMockAuthMiddleware() {
    return jest.fn((req, res, next) => {
        req.user = { id: 'test-user-id', role: 'user' };
        next();
    });
}
/**
 * Create mock validation middleware
 */
function createMockValidationMiddleware() {
    return jest.fn((req, res, next) => {
        // Mock validation always passes
        next();
    });
}
//# sourceMappingURL=testApp.js.map
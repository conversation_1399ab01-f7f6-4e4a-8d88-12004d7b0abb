export interface GeneratedCode {
    projectPath: string;
    projectName: string;
    files: GeneratedFile[];
    structure: ProjectStructure;
    metadata?: {
        generatedAt: string;
        generatedBy: string;
        version: string;
        [key: string]: any;
    };
}
export interface GeneratedFile {
    path: string;
    content: string;
    type: 'typescript' | 'javascript' | 'css' | 'html' | 'json' | 'markdown' | 'dockerfile' | 'yaml' | 'sql' | 'other';
    size: number;
}
export interface ProjectStructure {
    backend: {
        src: {
            models: string[];
            services: string[];
            routes: string[];
            controllers: string[];
            middleware: string[];
            utils: string[];
            config: string[];
        };
        tests: string[];
        config: string[];
    };
    frontend: {
        src: {
            components: string[];
            pages: string[];
            services: string[];
            utils: string[];
            types: string[];
            styles: string[];
        };
        public: string[];
        tests: string[];
        config: string[];
    };
    database: {
        migrations: string[];
        seeds: string[];
        schema: string[];
    };
    deployment: {
        docker: string[];
        kubernetes: string[];
        scripts: string[];
    };
    documentation: string[];
}
//# sourceMappingURL=GeneratedCode.d.ts.map
export interface ImportStatement {
    importName: string;
    fromPath: string;
    isDefault: boolean;
    isNamespace: boolean;
    filePath: string;
}
export interface ExportStatement {
    exportName: string;
    isDefault: boolean;
    filePath: string;
}
export interface ImportExportValidationResult {
    success: boolean;
    issues: string[];
    warnings: string[];
    duplicateImports: ImportStatement[];
    missingExports: string[];
    unusedImports: string[];
}
/**
 * Manages import/export consistency across the generated codebase
 * Prevents duplicate imports and ensures export/import alignment
 */
export declare class ImportExportManager {
    private registeredImports;
    private registeredExports;
    private importStatements;
    private exportStatements;
    private logger;
    constructor();
    /**
     * Register an import to prevent duplicates
     */
    registerImport(filePath: string, importName: string, fromPath: string, isDefault?: boolean): void;
    /**
     * Register an export
     */
    registerExport(filePath: string, exportName: string, isDefault?: boolean): void;
    /**
     * Check if an import already exists
     */
    hasImport(filePath: string, importName: string, fromPath: string, isDefault?: boolean): boolean;
    /**
     * Generate clean import statements for a file
     */
    generateImportStatements(filePath: string): string[];
    /**
     * Validate import/export consistency across the project
     */
    validateConsistency(projectPath: string): ImportExportValidationResult;
    /**
     * Fix duplicate imports in a file
     */
    fixDuplicateImports(filePath: string, content: string): string;
    /**
     * Clear all registered imports and exports
     */
    clear(): void;
    /**
     * Normalize file paths for consistent comparison
     */
    private normalizePath;
    /**
     * Check if an export exists
     */
    private hasExport;
    /**
     * Resolve import path to actual file path
     */
    private resolveImportPath;
    /**
     * Scan project files to extract imports and exports
     */
    private scanProjectFiles;
}
export default ImportExportManager;
//# sourceMappingURL=ImportExportManager.d.ts.map
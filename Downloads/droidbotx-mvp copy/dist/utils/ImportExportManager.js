"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImportExportManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const Logger_1 = require("../core/Logger");
/**
 * Manages import/export consistency across the generated codebase
 * Prevents duplicate imports and ensures export/import alignment
 */
class ImportExportManager {
    constructor() {
        this.registeredImports = new Map();
        this.registeredExports = new Map();
        this.importStatements = new Map();
        this.exportStatements = new Map();
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Register an import to prevent duplicates
     */
    registerImport(filePath, importName, fromPath, isDefault = false) {
        const normalizedFilePath = this.normalizePath(filePath);
        const normalizedFromPath = this.normalizePath(fromPath);
        if (!this.registeredImports.has(normalizedFilePath)) {
            this.registeredImports.set(normalizedFilePath, new Set());
        }
        const importKey = `${importName}:${normalizedFromPath}:${isDefault}`;
        const imports = this.registeredImports.get(normalizedFilePath);
        if (imports.has(importKey)) {
            this.logger.warn(`Duplicate import detected: ${importName} from ${fromPath} in ${filePath}`);
            return;
        }
        imports.add(importKey);
        // Store detailed import information
        if (!this.importStatements.has(normalizedFilePath)) {
            this.importStatements.set(normalizedFilePath, []);
        }
        this.importStatements.get(normalizedFilePath).push({
            importName,
            fromPath: normalizedFromPath,
            isDefault,
            isNamespace: false,
            filePath: normalizedFilePath
        });
    }
    /**
     * Register an export
     */
    registerExport(filePath, exportName, isDefault = false) {
        const normalizedFilePath = this.normalizePath(filePath);
        if (!this.registeredExports.has(normalizedFilePath)) {
            this.registeredExports.set(normalizedFilePath, new Set());
        }
        const exportKey = `${exportName}:${isDefault}`;
        this.registeredExports.get(normalizedFilePath).add(exportKey);
        // Store detailed export information
        if (!this.exportStatements.has(normalizedFilePath)) {
            this.exportStatements.set(normalizedFilePath, []);
        }
        this.exportStatements.get(normalizedFilePath).push({
            exportName,
            isDefault,
            filePath: normalizedFilePath
        });
    }
    /**
     * Check if an import already exists
     */
    hasImport(filePath, importName, fromPath, isDefault = false) {
        const normalizedFilePath = this.normalizePath(filePath);
        const normalizedFromPath = this.normalizePath(fromPath);
        const imports = this.registeredImports.get(normalizedFilePath);
        if (!imports) {
            return false;
        }
        const importKey = `${importName}:${normalizedFromPath}:${isDefault}`;
        return imports.has(importKey);
    }
    /**
     * Generate clean import statements for a file
     */
    generateImportStatements(filePath) {
        const normalizedFilePath = this.normalizePath(filePath);
        const imports = this.importStatements.get(normalizedFilePath) || [];
        // Group imports by source path
        const importGroups = new Map();
        imports.forEach(imp => {
            if (!importGroups.has(imp.fromPath)) {
                importGroups.set(imp.fromPath, []);
            }
            importGroups.get(imp.fromPath).push(imp);
        });
        const importLines = [];
        // Generate import statements
        importGroups.forEach((importsFromPath, fromPath) => {
            const defaultImports = importsFromPath.filter(imp => imp.isDefault);
            const namedImports = importsFromPath.filter(imp => !imp.isDefault && !imp.isNamespace);
            const namespaceImports = importsFromPath.filter(imp => imp.isNamespace);
            let importLine = 'import ';
            const parts = [];
            // Add default imports
            if (defaultImports.length > 0) {
                parts.push(defaultImports[0].importName);
            }
            // Add named imports
            if (namedImports.length > 0) {
                const namedImportNames = namedImports.map(imp => imp.importName).join(', ');
                parts.push(`{ ${namedImportNames} }`);
            }
            // Add namespace imports
            if (namespaceImports.length > 0) {
                parts.push(`* as ${namespaceImports[0].importName}`);
            }
            importLine += parts.join(', ');
            importLine += ` from '${fromPath}';`;
            importLines.push(importLine);
        });
        return importLines.sort();
    }
    /**
     * Validate import/export consistency across the project
     */
    validateConsistency(projectPath) {
        const issues = [];
        const warnings = [];
        const duplicateImports = [];
        const missingExports = [];
        const unusedImports = [];
        try {
            // Scan all TypeScript files in the project
            this.scanProjectFiles(projectPath);
            // Check for duplicate imports within files
            this.importStatements.forEach((imports, filePath) => {
                const seen = new Set();
                imports.forEach(imp => {
                    const key = `${imp.importName}:${imp.fromPath}`;
                    if (seen.has(key)) {
                        duplicateImports.push(imp);
                        issues.push(`Duplicate import '${imp.importName}' from '${imp.fromPath}' in ${filePath}`);
                    }
                    seen.add(key);
                });
            });
            // Check for missing exports
            this.importStatements.forEach((imports, filePath) => {
                imports.forEach(imp => {
                    const exportFilePath = this.resolveImportPath(imp.fromPath, filePath, projectPath);
                    if (exportFilePath && !this.hasExport(exportFilePath, imp.importName, imp.isDefault)) {
                        missingExports.push(`${imp.importName} from ${imp.fromPath}`);
                        issues.push(`Import '${imp.importName}' from '${imp.fromPath}' in ${filePath} has no corresponding export`);
                    }
                });
            });
            return {
                success: issues.length === 0,
                issues,
                warnings,
                duplicateImports,
                missingExports,
                unusedImports
            };
        }
        catch (error) {
            return {
                success: false,
                issues: [`Error validating import/export consistency: ${error instanceof Error ? error.message : String(error)}`],
                warnings,
                duplicateImports,
                missingExports,
                unusedImports
            };
        }
    }
    /**
     * Fix duplicate imports in a file
     */
    fixDuplicateImports(filePath, content) {
        try {
            // Extract existing imports
            const importRegex = /^import\s+(?:(\w+)(?:\s*,\s*)?)?(?:\{\s*([^}]+)\s*\})?(?:\s*,\s*(\w+))?\s+from\s+['"]([^'"]+)['"];?$/gm;
            const imports = new Map();
            const defaultImports = new Map();
            let match;
            const importLines = [];
            while ((match = importRegex.exec(content)) !== null) {
                const [fullMatch, defaultImport, namedImports, , fromPath] = match;
                importLines.push(fullMatch);
                if (defaultImport) {
                    defaultImports.set(fromPath, defaultImport);
                }
                if (namedImports) {
                    if (!imports.has(fromPath)) {
                        imports.set(fromPath, new Set());
                    }
                    namedImports.split(',').forEach(imp => {
                        imports.get(fromPath).add(imp.trim());
                    });
                }
            }
            // Remove old import lines
            let fixedContent = content;
            importLines.forEach(line => {
                fixedContent = fixedContent.replace(line, '');
            });
            // Generate new consolidated import lines
            const newImportLines = [];
            imports.forEach((namedImports, fromPath) => {
                const defaultImport = defaultImports.get(fromPath);
                let importLine = 'import ';
                if (defaultImport && namedImports.size > 0) {
                    importLine += `${defaultImport}, { ${Array.from(namedImports).join(', ')} }`;
                }
                else if (defaultImport) {
                    importLine += defaultImport;
                }
                else if (namedImports.size > 0) {
                    importLine += `{ ${Array.from(namedImports).join(', ')} }`;
                }
                importLine += ` from '${fromPath}';`;
                newImportLines.push(importLine);
            });
            // Add remaining default-only imports
            defaultImports.forEach((defaultImport, fromPath) => {
                if (!imports.has(fromPath)) {
                    newImportLines.push(`import ${defaultImport} from '${fromPath}';`);
                }
            });
            // Insert new imports at the top
            const lines = fixedContent.split('\n');
            const firstNonImportLine = lines.findIndex(line => line.trim() &&
                !line.trim().startsWith('import ') &&
                !line.trim().startsWith('//') &&
                !line.trim().startsWith('/*'));
            if (firstNonImportLine !== -1) {
                lines.splice(firstNonImportLine, 0, ...newImportLines, '');
            }
            else {
                lines.unshift(...newImportLines, '');
            }
            return lines.join('\n');
        }
        catch (error) {
            this.logger.error(`Error fixing duplicate imports in ${filePath}`, { error: error instanceof Error ? error.message : String(error) });
            return content;
        }
    }
    /**
     * Clear all registered imports and exports
     */
    clear() {
        this.registeredImports.clear();
        this.registeredExports.clear();
        this.importStatements.clear();
        this.exportStatements.clear();
    }
    /**
     * Normalize file paths for consistent comparison
     */
    normalizePath(filePath) {
        return path.normalize(filePath).replace(/\\/g, '/');
    }
    /**
     * Check if an export exists
     */
    hasExport(filePath, exportName, isDefault) {
        const exports = this.registeredExports.get(this.normalizePath(filePath));
        if (!exports) {
            return false;
        }
        const exportKey = `${exportName}:${isDefault}`;
        return exports.has(exportKey);
    }
    /**
     * Resolve import path to actual file path
     */
    resolveImportPath(importPath, fromFile, projectPath) {
        try {
            if (importPath.startsWith('.')) {
                // Relative import
                const fromDir = path.dirname(fromFile);
                const resolved = path.resolve(fromDir, importPath);
                // Try with different extensions
                const extensions = ['.ts', '.tsx', '.js', '.jsx'];
                for (const ext of extensions) {
                    const withExt = resolved + ext;
                    if (fs.existsSync(withExt)) {
                        return this.normalizePath(withExt);
                    }
                }
                // Try index files
                for (const ext of extensions) {
                    const indexFile = path.join(resolved, `index${ext}`);
                    if (fs.existsSync(indexFile)) {
                        return this.normalizePath(indexFile);
                    }
                }
            }
            return null;
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Scan project files to extract imports and exports
     */
    scanProjectFiles(projectPath) {
        // This would be implemented to scan all TypeScript files
        // For now, we'll rely on the registration methods being called during generation
    }
}
exports.ImportExportManager = ImportExportManager;
exports.default = ImportExportManager;
//# sourceMappingURL=ImportExportManager.js.map
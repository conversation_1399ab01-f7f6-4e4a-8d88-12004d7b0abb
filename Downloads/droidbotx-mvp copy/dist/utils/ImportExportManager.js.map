{"version": 3, "file": "ImportExportManager.js", "sourceRoot": "", "sources": ["../../src/utils/ImportExportManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,2CAAwC;AAyBxC;;;GAGG;AACH,MAAa,mBAAmB;IAO9B;QANQ,sBAAiB,GAA6B,IAAI,GAAG,EAAE,CAAC;QACxD,sBAAiB,GAA6B,IAAI,GAAG,EAAE,CAAC;QACxD,qBAAgB,GAAmC,IAAI,GAAG,EAAE,CAAC;QAC7D,qBAAgB,GAAmC,IAAI,GAAG,EAAE,CAAC;QAInE,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,QAAgB,EAAE,UAAkB,EAAE,QAAgB,EAAE,YAAqB,KAAK;QACtG,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAExD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,UAAU,IAAI,kBAAkB,IAAI,SAAS,EAAE,CAAC;QACrE,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAE,CAAC;QAEhE,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,UAAU,SAAS,QAAQ,OAAO,QAAQ,EAAE,CAAC,CAAC;YAC7F,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEvB,oCAAoC;QACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAE,CAAC,IAAI,CAAC;YAClD,UAAU;YACV,QAAQ,EAAE,kBAAkB;YAC5B,SAAS;YACT,WAAW,EAAE,KAAK;YAClB,QAAQ,EAAE,kBAAkB;SAC7B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,QAAgB,EAAE,UAAkB,EAAE,YAAqB,KAAK;QACpF,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAExD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,UAAU,IAAI,SAAS,EAAE,CAAC;QAC/C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE/D,oCAAoC;QACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAE,CAAC,IAAI,CAAC;YAClD,UAAU;YACV,SAAS;YACT,QAAQ,EAAE,kBAAkB;SAC7B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,QAAgB,EAAE,UAAkB,EAAE,QAAgB,EAAE,YAAqB,KAAK;QACjG,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAE/D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,UAAU,IAAI,kBAAkB,IAAI,SAAS,EAAE,CAAC;QACrE,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,QAAgB;QAC9C,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;QAEpE,+BAA+B;QAC/B,MAAM,YAAY,GAAG,IAAI,GAAG,EAA6B,CAAC;QAE1D,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC;YACD,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,6BAA6B;QAC7B,YAAY,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,QAAQ,EAAE,EAAE;YACjD,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACpE,MAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACvF,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAExE,IAAI,UAAU,GAAG,SAAS,CAAC;YAC3B,MAAM,KAAK,GAAa,EAAE,CAAC;YAE3B,sBAAsB;YACtB,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YAC3C,CAAC;YAED,oBAAoB;YACpB,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5E,KAAK,CAAC,IAAI,CAAC,KAAK,gBAAgB,IAAI,CAAC,CAAC;YACxC,CAAC;YAED,wBAAwB;YACxB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,KAAK,CAAC,IAAI,CAAC,QAAQ,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;YACvD,CAAC;YAED,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,UAAU,IAAI,UAAU,QAAQ,IAAI,CAAC;YAErC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC,IAAI,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,WAAmB;QAC5C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,gBAAgB,GAAsB,EAAE,CAAC;QAC/C,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,MAAM,aAAa,GAAa,EAAE,CAAC;QAEnC,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAEnC,2CAA2C;YAC3C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;gBAClD,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;gBAC/B,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBACpB,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;oBAChD,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;wBAClB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC3B,MAAM,CAAC,IAAI,CAAC,qBAAqB,GAAG,CAAC,UAAU,WAAW,GAAG,CAAC,QAAQ,QAAQ,QAAQ,EAAE,CAAC,CAAC;oBAC5F,CAAC;oBACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,4BAA4B;YAC5B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;gBAClD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBACpB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;oBACnF,IAAI,cAAc,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;wBACrF,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,SAAS,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;wBAC9D,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,UAAU,WAAW,GAAG,CAAC,QAAQ,QAAQ,QAAQ,8BAA8B,CAAC,CAAC;oBAC9G,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;gBAC5B,MAAM;gBACN,QAAQ;gBACR,gBAAgB;gBAChB,cAAc;gBACd,aAAa;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,CAAC,+CAA+C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjH,QAAQ;gBACR,gBAAgB;gBAChB,cAAc;gBACd,aAAa;aACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,QAAgB,EAAE,OAAe;QAC1D,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,WAAW,GAAG,wGAAwG,CAAC;YAC7H,MAAM,OAAO,GAAG,IAAI,GAAG,EAAuB,CAAC;YAC/C,MAAM,cAAc,GAAG,IAAI,GAAG,EAAkB,CAAC;YAEjD,IAAI,KAAK,CAAC;YACV,MAAM,WAAW,GAAa,EAAE,CAAC;YAEjC,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBACpD,MAAM,CAAC,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,AAAD,EAAG,QAAQ,CAAC,GAAG,KAAK,CAAC;gBACnE,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAE5B,IAAI,aAAa,EAAE,CAAC;oBAClB,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;gBAC9C,CAAC;gBAED,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC3B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;oBACnC,CAAC;oBACD,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;wBACpC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;oBACzC,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,0BAA0B;YAC1B,IAAI,YAAY,GAAG,OAAO,CAAC;YAC3B,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACzB,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,yCAAyC;YACzC,MAAM,cAAc,GAAa,EAAE,CAAC;YAEpC,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,QAAQ,EAAE,EAAE;gBACzC,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACnD,IAAI,UAAU,GAAG,SAAS,CAAC;gBAE3B,IAAI,aAAa,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;oBAC3C,UAAU,IAAI,GAAG,aAAa,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC/E,CAAC;qBAAM,IAAI,aAAa,EAAE,CAAC;oBACzB,UAAU,IAAI,aAAa,CAAC;gBAC9B,CAAC;qBAAM,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;oBACjC,UAAU,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC7D,CAAC;gBAED,UAAU,IAAI,UAAU,QAAQ,IAAI,CAAC;gBACrC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,qCAAqC;YACrC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,QAAQ,EAAE,EAAE;gBACjD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC3B,cAAc,CAAC,IAAI,CAAC,UAAU,aAAa,UAAU,QAAQ,IAAI,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,kBAAkB,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAChD,IAAI,CAAC,IAAI,EAAE;gBACX,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC;gBAClC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;gBAC7B,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAC9B,CAAC;YAEF,IAAI,kBAAkB,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC9B,KAAK,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,EAAE,GAAG,cAAc,EAAE,EAAE,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,OAAO,CAAC,GAAG,cAAc,EAAE,EAAE,CAAC,CAAC;YACvC,CAAC;YAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACtI,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAgB;QACpC,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,QAAgB,EAAE,UAAkB,EAAE,SAAkB;QACxE,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,UAAU,IAAI,SAAS,EAAE,CAAC;QAC/C,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAAkB,EAAE,QAAgB,EAAE,WAAmB;QACjF,IAAI,CAAC;YACH,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,kBAAkB;gBAClB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBAEnD,gCAAgC;gBAChC,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBAClD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;oBAC7B,MAAM,OAAO,GAAG,QAAQ,GAAG,GAAG,CAAC;oBAC/B,IAAI,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBACrC,CAAC;gBACH,CAAC;gBAED,kBAAkB;gBAClB,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;oBAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC;oBACrD,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC7B,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;oBACvC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,WAAmB;QAC1C,yDAAyD;QACzD,iFAAiF;IACnF,CAAC;CACF;AAlWD,kDAkWC;AAED,kBAAe,mBAAmB,CAAC"}
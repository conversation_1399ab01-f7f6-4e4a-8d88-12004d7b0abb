export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    fixesApplied: string[];
}
export interface ImportInfo {
    identifier: string;
    path: string;
    line: number;
    fullLine: string;
}
/**
 * Pre-compilation validator to prevent common issues before they cause compilation failures
 */
export declare class PreCompilationValidator {
    private logger;
    constructor();
    /**
     * Validate and fix common pre-compilation issues
     */
    validateAndFix(projectPath: string): Promise<ValidationResult>;
    /**
     * Validate and fix duplicate imports across all files
     */
    private validateDuplicateImports;
    /**
     * Validate import paths for consistency and correctness
     */
    private validateImportPaths;
    /**
     * Validate service instantiation patterns
     */
    private validateServicePatterns;
    /**
     * Validate export consistency
     */
    private validateExportConsistency;
    /**
     * Extract import information from file lines
     */
    private extractImports;
    /**
     * Find duplicate imports based on normalized identifiers and paths
     */
    private findDuplicateImports;
    /**
     * Remove duplicate imports from content
     */
    private removeDuplicateImports;
    /**
     * Normalize import identifier for comparison
     */
    private normalizeImportIdentifier;
    /**
     * Normalize import path for comparison
     */
    private normalizeImportPath;
    /**
     * Find all TypeScript files recursively
     */
    private findTypeScriptFiles;
}
export default PreCompilationValidator;
//# sourceMappingURL=PreCompilationValidator.d.ts.map
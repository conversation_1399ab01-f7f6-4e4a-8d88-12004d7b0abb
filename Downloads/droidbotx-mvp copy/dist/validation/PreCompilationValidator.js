"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreCompilationValidator = void 0;
const Logger_1 = require("../core/Logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Pre-compilation validator to prevent common issues before they cause compilation failures
 */
class PreCompilationValidator {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Validate and fix common pre-compilation issues
     */
    async validateAndFix(projectPath) {
        const result = {
            isValid: true,
            errors: [],
            warnings: [],
            fixesApplied: []
        };
        try {
            this.logger.info('Starting pre-compilation validation', { projectPath });
            // Validate duplicate imports
            await this.validateDuplicateImports(projectPath, result);
            // Validate import paths
            await this.validateImportPaths(projectPath, result);
            // Validate service instantiation patterns
            await this.validateServicePatterns(projectPath, result);
            // Validate export consistency
            await this.validateExportConsistency(projectPath, result);
            result.isValid = result.errors.length === 0;
            this.logger.info('Pre-compilation validation completed', {
                isValid: result.isValid,
                errors: result.errors.length,
                warnings: result.warnings.length,
                fixesApplied: result.fixesApplied.length
            });
        }
        catch (error) {
            result.isValid = false;
            result.errors.push(`Pre-compilation validation failed: ${error instanceof Error ? error.message : String(error)}`);
        }
        return result;
    }
    /**
     * Validate and fix duplicate imports across all files
     */
    async validateDuplicateImports(projectPath, result) {
        const backendSrcPath = path.join(projectPath, 'backend/src');
        if (!fs.existsSync(backendSrcPath))
            return;
        const tsFiles = this.findTypeScriptFiles(backendSrcPath);
        for (const filePath of tsFiles) {
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                const lines = content.split('\n');
                const imports = this.extractImports(lines);
                // Check for duplicates
                const duplicates = this.findDuplicateImports(imports);
                if (duplicates.length > 0) {
                    // Fix duplicates
                    const fixedContent = this.removeDuplicateImports(content, duplicates);
                    fs.writeFileSync(filePath, fixedContent);
                    duplicates.forEach(dup => {
                        result.fixesApplied.push(`Removed duplicate import in ${path.basename(filePath)}: ${dup.identifier} from ${dup.path}`);
                    });
                }
            }
            catch (error) {
                result.warnings.push(`Could not validate imports in ${path.basename(filePath)}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    /**
     * Validate import paths for consistency and correctness
     */
    async validateImportPaths(projectPath, result) {
        const backendSrcPath = path.join(projectPath, 'backend/src');
        if (!fs.existsSync(backendSrcPath))
            return;
        const tsFiles = this.findTypeScriptFiles(backendSrcPath);
        for (const filePath of tsFiles) {
            try {
                let content = fs.readFileSync(filePath, 'utf8');
                let modified = false;
                // Fix .js extensions in TypeScript imports
                const jsExtensionRegex = /from\s+['"](.+?)\.js['"]/g;
                content = content.replace(jsExtensionRegex, (match, importPath) => {
                    modified = true;
                    result.fixesApplied.push(`Fixed .js extension in ${path.basename(filePath)}: ${importPath}`);
                    return `from '${importPath}'`;
                });
                // Fix missing file extensions for relative imports
                const relativeImportRegex = /from\s+['"](\.\/.+?)['"]/g;
                content = content.replace(relativeImportRegex, (match, importPath) => {
                    if (!importPath.endsWith('.ts') && !importPath.endsWith('.js')) {
                        const targetPath = path.resolve(path.dirname(filePath), importPath + '.ts');
                        if (fs.existsSync(targetPath)) {
                            // Don't add extension for TypeScript imports
                            return match;
                        }
                    }
                    return match;
                });
                if (modified) {
                    fs.writeFileSync(filePath, content);
                }
            }
            catch (error) {
                result.warnings.push(`Could not validate import paths in ${path.basename(filePath)}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    /**
     * Validate service instantiation patterns
     */
    async validateServicePatterns(projectPath, result) {
        const servicesPath = path.join(projectPath, 'backend/src/services');
        if (!fs.existsSync(servicesPath))
            return;
        const serviceFiles = fs.readdirSync(servicesPath).filter(f => f.endsWith('.ts'));
        for (const serviceFile of serviceFiles) {
            const servicePath = path.join(servicesPath, serviceFile);
            try {
                let content = fs.readFileSync(servicePath, 'utf8');
                let modified = false;
                // Check for static method patterns that should be instance methods
                if (content.includes('static async') || content.includes('static ')) {
                    // Convert static methods to instance methods
                    content = content.replace(/static\s+async\s+/g, 'async ');
                    content = content.replace(/static\s+/g, '');
                    modified = true;
                    result.fixesApplied.push(`Converted static methods to instance methods in ${serviceFile}`);
                }
                // Ensure proper export pattern
                const className = serviceFile.replace('.ts', '');
                if (!content.includes(`export class ${className}`)) {
                    result.warnings.push(`Service ${serviceFile} may have incorrect export pattern`);
                }
                if (modified) {
                    fs.writeFileSync(servicePath, content);
                }
            }
            catch (error) {
                result.warnings.push(`Could not validate service patterns in ${serviceFile}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    /**
     * Validate export consistency
     */
    async validateExportConsistency(projectPath, result) {
        const backendSrcPath = path.join(projectPath, 'backend/src');
        if (!fs.existsSync(backendSrcPath))
            return;
        const tsFiles = this.findTypeScriptFiles(backendSrcPath);
        for (const filePath of tsFiles) {
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                const fileName = path.basename(filePath, '.ts');
                // Check if file has proper exports
                if (!content.includes('export') && !fileName.includes('.test') && !fileName.includes('.spec')) {
                    result.warnings.push(`File ${fileName}.ts has no exports`);
                }
                // Check for default export consistency
                const hasDefaultExport = content.includes('export default');
                const hasNamedExports = content.includes('export class') || content.includes('export interface') || content.includes('export const');
                if (hasDefaultExport && hasNamedExports) {
                    // This is fine, mixed exports are allowed
                }
                else if (!hasDefaultExport && !hasNamedExports) {
                    result.warnings.push(`File ${fileName}.ts has no exports`);
                }
            }
            catch (error) {
                result.warnings.push(`Could not validate exports in ${path.basename(filePath)}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    /**
     * Extract import information from file lines
     */
    extractImports(lines) {
        const imports = [];
        lines.forEach((line, index) => {
            const trimmed = line.trim();
            if (trimmed.startsWith('import') && trimmed.includes('from')) {
                const match = trimmed.match(/import\s+(.+?)\s+from\s+['"](.+?)['"]/);
                if (match) {
                    const [, identifier, path] = match;
                    imports.push({
                        identifier: identifier.trim(),
                        path: path.trim(),
                        line: index + 1,
                        fullLine: line
                    });
                }
            }
        });
        return imports;
    }
    /**
     * Find duplicate imports based on normalized identifiers and paths
     */
    findDuplicateImports(imports) {
        const seen = new Map();
        const duplicates = [];
        for (const importInfo of imports) {
            const normalizedIdentifier = this.normalizeImportIdentifier(importInfo.identifier);
            const normalizedPath = this.normalizeImportPath(importInfo.path);
            const key = `${normalizedIdentifier}:${normalizedPath}`;
            if (seen.has(key)) {
                duplicates.push(importInfo);
            }
            else {
                seen.set(key, importInfo);
            }
        }
        return duplicates;
    }
    /**
     * Remove duplicate imports from content
     */
    removeDuplicateImports(content, duplicates) {
        const lines = content.split('\n');
        const duplicateLines = new Set(duplicates.map(d => d.line - 1)); // Convert to 0-based index
        return lines.filter((_, index) => !duplicateLines.has(index)).join('\n');
    }
    /**
     * Normalize import identifier for comparison
     */
    normalizeImportIdentifier(identifier) {
        const cleaned = identifier.trim().replace(/\s+/g, ' ');
        if (cleaned.includes('{')) {
            const match = cleaned.match(/\{(.+?)\}/);
            if (match) {
                const namedImports = match[1].split(',').map(item => item.trim()).sort();
                return `{${namedImports.join(', ')}}`;
            }
        }
        return cleaned;
    }
    /**
     * Normalize import path for comparison
     */
    normalizeImportPath(importPath) {
        let normalized = importPath.replace(/\.(ts|js)$/, '');
        if (normalized.includes('/routes/')) {
            normalized = normalized.replace(/\.routes$/, '');
        }
        normalized = normalized.replace(/^\.\//, '');
        return normalized;
    }
    /**
     * Find all TypeScript files recursively
     */
    findTypeScriptFiles(dirPath) {
        const tsFiles = [];
        try {
            const items = fs.readdirSync(dirPath);
            for (const item of items) {
                const itemPath = path.join(dirPath, item);
                const stat = fs.statSync(itemPath);
                if (stat.isDirectory()) {
                    tsFiles.push(...this.findTypeScriptFiles(itemPath));
                }
                else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                    tsFiles.push(itemPath);
                }
            }
        }
        catch (error) {
            // Directory doesn't exist or can't be read
        }
        return tsFiles;
    }
}
exports.PreCompilationValidator = PreCompilationValidator;
exports.default = PreCompilationValidator;
//# sourceMappingURL=PreCompilationValidator.js.map
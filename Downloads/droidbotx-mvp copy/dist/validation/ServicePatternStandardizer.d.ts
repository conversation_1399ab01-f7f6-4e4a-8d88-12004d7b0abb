export interface ServiceStandardizationResult {
    success: boolean;
    fixesApplied: string[];
    errors: string[];
    servicesProcessed: number;
}
/**
 * Standardizes service instantiation patterns across the codebase
 * Resolves static vs instance method conflicts and ensures consistent patterns
 */
export declare class ServicePatternStandardizer {
    private logger;
    constructor();
    /**
     * Standardize all service patterns in the project
     */
    standardizeServicePatterns(projectPath: string): Promise<ServiceStandardizationResult>;
    /**
     * Standardize service classes to use consistent instance patterns
     * Priority 4 Fix: Ensure all services have consistent method signatures
     */
    private standardizeServiceClasses;
    /**
     * Ensure service has standard CRUD methods for consistent testing
     * Priority 4 Fix: Service Method Existence Validation
     */
    private ensureStandardCRUDMethods;
    /**
     * Standardize method signatures for consistent error handling
     */
    private standardizeMethodSignatures;
    /**
     * Generate validation code for CRUD methods
     */
    private generateValidationCode;
    /**
     * Update controllers to use standardized service patterns
     */
    private updateControllerServiceUsage;
    /**
     * Update routes to use standardized service patterns
     */
    private updateRouteServiceUsage;
    /**
     * Standardize service imports and exports
     */
    private standardizeServiceImportsExports;
    /**
     * Find all TypeScript files recursively
     */
    private findTypeScriptFiles;
}
export default ServicePatternStandardizer;
//# sourceMappingURL=ServicePatternStandardizer.d.ts.map
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicePatternStandardizer = void 0;
const Logger_1 = require("../core/Logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Standardizes service instantiation patterns across the codebase
 * Resolves static vs instance method conflicts and ensures consistent patterns
 */
class ServicePatternStandardizer {
    constructor() {
        this.logger = Logger_1.Logger.getInstance();
    }
    /**
     * Standardize all service patterns in the project
     */
    async standardizeServicePatterns(projectPath) {
        const result = {
            success: true,
            fixesApplied: [],
            errors: [],
            servicesProcessed: 0
        };
        try {
            this.logger.info('Starting service pattern standardization', { projectPath });
            // Step 1: Standardize service classes
            await this.standardizeServiceClasses(projectPath, result);
            // Step 2: Update controllers to use standardized services
            await this.updateControllerServiceUsage(projectPath, result);
            // Step 3: Update routes to use standardized services
            await this.updateRouteServiceUsage(projectPath, result);
            // Step 4: Fix service imports and exports
            await this.standardizeServiceImportsExports(projectPath, result);
            result.success = result.errors.length === 0;
            this.logger.info('Service pattern standardization completed', {
                success: result.success,
                fixesApplied: result.fixesApplied.length,
                errors: result.errors.length,
                servicesProcessed: result.servicesProcessed
            });
        }
        catch (error) {
            result.success = false;
            result.errors.push(`Service standardization failed: ${error instanceof Error ? error.message : String(error)}`);
        }
        return result;
    }
    /**
     * Standardize service classes to use consistent instance patterns
     * Priority 4 Fix: Ensure all services have consistent method signatures
     */
    async standardizeServiceClasses(projectPath, result) {
        const servicesPath = path.join(projectPath, 'backend/src/services');
        if (!fs.existsSync(servicesPath))
            return;
        const serviceFiles = fs.readdirSync(servicesPath).filter(f => f.endsWith('.ts'));
        for (const serviceFile of serviceFiles) {
            const servicePath = path.join(servicesPath, serviceFile);
            try {
                let content = fs.readFileSync(servicePath, 'utf8');
                let modified = false;
                // Convert static methods to instance methods
                if (content.includes('static async') || content.includes('static ')) {
                    content = content.replace(/static\s+async\s+/g, 'async ');
                    content = content.replace(/static\s+(?!readonly)/g, '');
                    modified = true;
                    result.fixesApplied.push(`Converted static methods to instance methods in ${serviceFile}`);
                }
                // Ensure proper constructor
                const className = serviceFile.replace('.ts', '');
                if (!content.includes('constructor(')) {
                    const classMatch = content.match(/export class (\w+)/);
                    if (classMatch) {
                        content = content.replace(/export class (\w+)\s*{/, `export class $1 {
  constructor() {
    // Service initialization
  }`);
                        modified = true;
                        result.fixesApplied.push(`Added constructor to ${serviceFile}`);
                    }
                }
                // Priority 4 Fix: Ensure standard CRUD methods exist
                const standardMethods = this.ensureStandardCRUDMethods(content, className);
                if (standardMethods.modified) {
                    content = standardMethods.content;
                    modified = true;
                    result.fixesApplied.push(`Added missing CRUD methods to ${serviceFile}`);
                }
                // Ensure proper export pattern
                if (!content.includes(`export class ${className}`)) {
                    if (content.includes(`class ${className}`)) {
                        content = content.replace(`class ${className}`, `export class ${className}`);
                        modified = true;
                        result.fixesApplied.push(`Fixed export pattern in ${serviceFile}`);
                    }
                }
                // Add default export if missing
                if (!content.includes('export default')) {
                    content += `\n\nexport default ${className};`;
                    modified = true;
                    result.fixesApplied.push(`Added default export to ${serviceFile}`);
                }
                // Fix method signatures to ensure proper error handling
                content = this.standardizeMethodSignatures(content, serviceFile, result);
                if (modified) {
                    fs.writeFileSync(servicePath, content);
                }
                result.servicesProcessed++;
            }
            catch (error) {
                result.errors.push(`Error standardizing ${serviceFile}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    /**
     * Ensure service has standard CRUD methods for consistent testing
     * Priority 4 Fix: Service Method Existence Validation
     */
    ensureStandardCRUDMethods(content, className) {
        let modified = false;
        let newContent = content;
        const standardMethods = [
            {
                name: 'create',
                signature: 'async create(data: any): Promise<any>',
                implementation: `async create(data: any): Promise<any> {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid data provided');
    }

    try {
      const query = 'INSERT INTO ${className.toLowerCase().replace('service', '')}s (name) VALUES ($1) RETURNING *';
      const result = await pool.query(query, [data.name]);
      return result.rows[0];
    } catch (error) {
      throw new Error(\`Failed to create ${className.toLowerCase().replace('service', '')}: \${error.message}\`);
    }
  }`
            },
            {
                name: 'read',
                signature: 'async read(id: string): Promise<any>',
                implementation: `async read(id: string): Promise<any> {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw new Error('Invalid ID provided');
    }

    try {
      const query = 'SELECT * FROM ${className.toLowerCase().replace('service', '')}s WHERE id = $1';
      const result = await pool.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      throw new Error(\`Failed to read ${className.toLowerCase().replace('service', '')}: \${error.message}\`);
    }
  }`
            },
            {
                name: 'list',
                signature: 'async list(): Promise<any[]>',
                implementation: `async list(): Promise<any[]> {
    try {
      const query = 'SELECT * FROM ${className.toLowerCase().replace('service', '')}s ORDER BY created_at DESC';
      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      throw new Error(\`Failed to list ${className.toLowerCase().replace('service', '')}s: \${error.message}\`);
    }
  }`
            },
            {
                name: 'update',
                signature: 'async update(id: string, data: any): Promise<any>',
                implementation: `async update(id: string, data: any): Promise<any> {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw new Error('Invalid ID provided');
    }
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid data provided');
    }

    try {
      const query = 'UPDATE ${className.toLowerCase().replace('service', '')}s SET name = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *';
      const result = await pool.query(query, [data.name, id]);
      return result.rows[0];
    } catch (error) {
      throw new Error(\`Failed to update ${className.toLowerCase().replace('service', '')}: \${error.message}\`);
    }
  }`
            },
            {
                name: 'delete',
                signature: 'async delete(id: string): Promise<boolean>',
                implementation: `async delete(id: string): Promise<boolean> {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw new Error('Invalid ID provided');
    }

    try {
      const query = 'DELETE FROM ${className.toLowerCase().replace('service', '')}s WHERE id = $1';
      const result = await pool.query(query, [id]);
      return result.rowCount > 0;
    } catch (error) {
      throw new Error(\`Failed to delete ${className.toLowerCase().replace('service', '')}: \${error.message}\`);
    }
  }`
            }
        ];
        // Check if each standard method exists
        for (const method of standardMethods) {
            const methodRegex = new RegExp(`\\b${method.name}\\s*\\(`, 'g');
            if (!methodRegex.test(newContent)) {
                // Method doesn't exist, add it
                const classEndRegex = /}\s*$/;
                const poolImportRegex = /import.*pool.*from/;
                // Add pool import if not present
                if (!poolImportRegex.test(newContent) && !newContent.includes('pool.query')) {
                    newContent = `import { pool } from '../config/database';\n${newContent}`;
                }
                // Add method before the closing brace of the class
                newContent = newContent.replace(classEndRegex, `
  ${method.implementation}
}`);
                modified = true;
            }
        }
        return { content: newContent, modified };
    }
    /**
     * Standardize method signatures for consistent error handling
     */
    standardizeMethodSignatures(content, serviceFile, result) {
        let modified = content;
        // Ensure all async methods have proper error handling
        const asyncMethodRegex = /async\s+(\w+)\s*\([^)]*\)\s*:\s*Promise<[^>]+>\s*{/g;
        modified = modified.replace(asyncMethodRegex, (match, methodName) => {
            if (!match.includes('try') && !match.includes('catch')) {
                return match.replace('{', `{
    try {`);
            }
            return match;
        });
        // Add validation for common CRUD methods
        const crudMethods = ['create', 'read', 'update', 'delete'];
        for (const method of crudMethods) {
            const methodRegex = new RegExp(`async\\s+${method}\\s*\\([^)]*\\)\\s*:\\s*Promise<[^>]+>\\s*{`, 'g');
            if (methodRegex.test(modified)) {
                // Add input validation
                const validationCode = this.generateValidationCode(method);
                modified = modified.replace(methodRegex, (match) => {
                    if (!match.includes('if (!') && !match.includes('throw new Error')) {
                        return match.replace('{', `{
${validationCode}`);
                    }
                    return match;
                });
                result.fixesApplied.push(`Added input validation to ${method} method in ${serviceFile}`);
            }
        }
        return modified;
    }
    /**
     * Generate validation code for CRUD methods
     */
    generateValidationCode(method) {
        switch (method) {
            case 'create':
                return `    if (!data || typeof data !== 'object') {
      throw new Error('Invalid data provided');
    }`;
            case 'read':
                return `    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw new Error('Invalid ID provided');
    }`;
            case 'update':
                return `    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw new Error('Invalid ID provided');
    }
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid data provided');
    }`;
            case 'delete':
                return `    if (!id || typeof id !== 'string' || id.trim() === '') {
      throw new Error('Invalid ID provided');
    }`;
            default:
                return '';
        }
    }
    /**
     * Update controllers to use standardized service patterns
     */
    async updateControllerServiceUsage(projectPath, result) {
        const controllersPath = path.join(projectPath, 'backend/src/controllers');
        if (!fs.existsSync(controllersPath))
            return;
        const controllerFiles = fs.readdirSync(controllersPath).filter(f => f.endsWith('.ts'));
        for (const controllerFile of controllerFiles) {
            const controllerPath = path.join(controllersPath, controllerFile);
            try {
                let content = fs.readFileSync(controllerPath, 'utf8');
                let modified = false;
                // Fix service instantiation patterns
                const serviceInstantiationRegex = /(\w+Service)\.(\w+)\(/g;
                content = content.replace(serviceInstantiationRegex, (match, serviceName, methodName) => {
                    // Convert static calls to instance calls
                    const instanceVar = serviceName.toLowerCase().replace('service', 'Service');
                    // Add service instantiation at the top of methods if not present
                    if (!content.includes(`const ${instanceVar} = new ${serviceName}()`)) {
                        modified = true;
                        result.fixesApplied.push(`Added service instantiation in ${controllerFile}`);
                    }
                    return `${instanceVar}.${methodName}(`;
                });
                // Ensure proper service imports
                const serviceImportRegex = /import\s*{\s*(\w+Service)\s*}\s*from\s*['"]([^'"]+)['"]/g;
                content = content.replace(serviceImportRegex, (match, serviceName, importPath) => {
                    // Ensure import path doesn't have .js extension
                    const cleanPath = importPath.replace(/\.js$/, '');
                    return `import ${serviceName} from '${cleanPath}'`;
                });
                if (modified) {
                    fs.writeFileSync(controllerPath, content);
                }
            }
            catch (error) {
                result.errors.push(`Error updating controller ${controllerFile}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    /**
     * Update routes to use standardized service patterns
     */
    async updateRouteServiceUsage(projectPath, result) {
        const routesPath = path.join(projectPath, 'backend/src/routes');
        if (!fs.existsSync(routesPath))
            return;
        const routeFiles = fs.readdirSync(routesPath).filter(f => f.endsWith('.ts'));
        for (const routeFile of routeFiles) {
            const routePath = path.join(routesPath, routeFile);
            try {
                let content = fs.readFileSync(routePath, 'utf8');
                let modified = false;
                // Fix direct service usage in routes
                const directServiceRegex = /(\w+Service)\.(\w+)\(/g;
                content = content.replace(directServiceRegex, (match, serviceName, methodName) => {
                    const instanceVar = serviceName.toLowerCase().replace('service', 'Service');
                    if (!content.includes(`const ${instanceVar} = new ${serviceName}()`)) {
                        // Add service instantiation
                        const routerMatch = content.match(/const\s+router\s*=\s*express\.Router\(\)/);
                        if (routerMatch) {
                            content = content.replace(routerMatch[0], `${routerMatch[0]}\nconst ${instanceVar} = new ${serviceName}();`);
                            modified = true;
                            result.fixesApplied.push(`Added service instantiation in route ${routeFile}`);
                        }
                    }
                    return `${instanceVar}.${methodName}(`;
                });
                if (modified) {
                    fs.writeFileSync(routePath, content);
                }
            }
            catch (error) {
                result.errors.push(`Error updating route ${routeFile}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    /**
     * Standardize service imports and exports
     */
    async standardizeServiceImportsExports(projectPath, result) {
        const backendSrcPath = path.join(projectPath, 'backend/src');
        if (!fs.existsSync(backendSrcPath))
            return;
        const tsFiles = this.findTypeScriptFiles(backendSrcPath);
        for (const filePath of tsFiles) {
            try {
                let content = fs.readFileSync(filePath, 'utf8');
                let modified = false;
                // Standardize service imports to use default imports
                const namedServiceImportRegex = /import\s*{\s*(\w+Service)\s*}\s*from\s*['"]([^'"]*services[^'"]*)['"]/g;
                content = content.replace(namedServiceImportRegex, (match, serviceName, importPath) => {
                    modified = true;
                    result.fixesApplied.push(`Standardized service import in ${path.basename(filePath)}`);
                    return `import ${serviceName} from '${importPath}'`;
                });
                // Remove .js extensions from TypeScript imports
                const jsExtensionRegex = /from\s+['"](.+?)\.js['"]/g;
                content = content.replace(jsExtensionRegex, (match, importPath) => {
                    modified = true;
                    result.fixesApplied.push(`Removed .js extension in ${path.basename(filePath)}`);
                    return `from '${importPath}'`;
                });
                if (modified) {
                    fs.writeFileSync(filePath, content);
                }
            }
            catch (error) {
                result.errors.push(`Error standardizing imports in ${path.basename(filePath)}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    }
    /**
     * Find all TypeScript files recursively
     */
    findTypeScriptFiles(dirPath) {
        const tsFiles = [];
        try {
            const items = fs.readdirSync(dirPath);
            for (const item of items) {
                const itemPath = path.join(dirPath, item);
                const stat = fs.statSync(itemPath);
                if (stat.isDirectory()) {
                    tsFiles.push(...this.findTypeScriptFiles(itemPath));
                }
                else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                    tsFiles.push(itemPath);
                }
            }
        }
        catch (error) {
            // Directory doesn't exist or can't be read
        }
        return tsFiles;
    }
}
exports.ServicePatternStandardizer = ServicePatternStandardizer;
exports.default = ServicePatternStandardizer;
//# sourceMappingURL=ServicePatternStandardizer.js.map
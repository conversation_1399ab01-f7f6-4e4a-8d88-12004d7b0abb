#!/bin/bash

# DroidBotX MVP End-to-End Testing Script
# This script validates the complete system functionality including:
# - System integrity and dependencies
# - Mock-based workflow testing
# - Generated application structure validation
# - Quality assurance checks

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_DIR="/tmp/droidbotx-e2e-tests"
PROJECT_DIR="/home/<USER>/droidbotx-mvp"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="$TEST_DIR/e2e_test_$TIMESTAMP.log"

# Create test directory
mkdir -p "$TEST_DIR"

# Logging function
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Test result tracking
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log "${BLUE}[TEST $TOTAL_TESTS] $test_name${NC}"
    
    if eval "$test_command" >> "$LOG_FILE" 2>&1; then
        log "${GREEN}✅ PASSED: $test_name${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log "${RED}❌ FAILED: $test_name${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Header
log "${BLUE}========================================${NC}"
log "${BLUE}DroidBotX MVP End-to-End Testing Suite${NC}"
log "${BLUE}========================================${NC}"
log "Test started at: $(date)"
log "Test directory: $TEST_DIR"
log "Log file: $LOG_FILE"
log ""

# Change to project directory
cd "$PROJECT_DIR"

# Test 1: System Dependencies
run_test "System Dependencies Check" "npm ci --silent"

# Test 2: TypeScript Compilation
run_test "TypeScript Compilation" "npm run build --silent"

# Test 3: Unit Tests
run_test "Unit Test Suite" "npm test --silent"

# Test 4: CLI Help Command
run_test "CLI Help Command" "npx ts-node src/cli/app.ts --help"

# Test 5: Project Structure Validation
run_test "Project Structure Validation" "
    test -f package.json &&
    test -f tsconfig.json &&
    test -d src/core &&
    test -d src/agents &&
    test -d src/orchestration &&
    test -d src/cli &&
    test -f src/agents/PlanningAgent.ts &&
    test -f src/agents/CodingAgent.ts &&
    test -f src/agents/DeploymentAgent.ts
"

# Test 6: Core Module Imports
run_test "Core Module Import Validation" "
    node -e \"
        const { BaseAgent } = require('./dist/core/BaseAgent');
        const { ConfigManager } = require('./dist/core/ConfigManager');
        const { Logger } = require('./dist/core/Logger');
        const { LLMProviderSystem } = require('./dist/core/LLMProviderSystem');
        const { ToolManager } = require('./dist/core/ToolManager');
        console.log('All core modules imported successfully');
    \"
"

# Test 7: Agent Module Imports
run_test "Agent Module Import Validation" "
    node -e \"
        const { PlanningAgent } = require('./dist/agents/PlanningAgent');
        const { CodingAgent } = require('./dist/agents/CodingAgent');
        const { DeploymentAgent } = require('./dist/agents/DeploymentAgent');
        console.log('All agent modules imported successfully');
    \"
"

# Test 8: Orchestrator Module Import
run_test "Orchestrator Module Import Validation" "
    node -e \"
        const { Orchestrator } = require('./dist/orchestration/Orchestrator');
        console.log('Orchestrator module imported successfully');
    \"
"

# Test 9: Mock Application Generation Structure
create_mock_app() {
    local app_dir="$TEST_DIR/mock-todo-app"
    mkdir -p "$app_dir"
    
    # Create mock frontend structure
    mkdir -p "$app_dir/frontend/src/components"
    mkdir -p "$app_dir/frontend/src/pages"
    mkdir -p "$app_dir/frontend/src/contexts"
    mkdir -p "$app_dir/frontend/src/services"
    mkdir -p "$app_dir/frontend/src/types"
    mkdir -p "$app_dir/frontend/public"
    
    # Create mock backend structure
    mkdir -p "$app_dir/backend/src/routes"
    mkdir -p "$app_dir/backend/src/controllers"
    mkdir -p "$app_dir/backend/src/models"
    mkdir -p "$app_dir/backend/src/middleware"
    mkdir -p "$app_dir/backend/src/config"
    mkdir -p "$app_dir/backend/src/utils"
    
    # Create mock configuration files
    cat > "$app_dir/frontend/package.json" << 'EOF'
{
  "name": "todo-frontend",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "axios": "^1.3.0",
    "typescript": "^4.9.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "devDependencies": {
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.16.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0"
  }
}
EOF

    cat > "$app_dir/backend/package.json" << 'EOF'
{
  "name": "todo-backend",
  "version": "1.0.0",
  "main": "dist/index.js",
  "dependencies": {
    "express": "^4.18.0",
    "cors": "^2.8.5",
    "helmet": "^6.0.0",
    "jsonwebtoken": "^9.0.0",
    "bcryptjs": "^2.4.3",
    "pg": "^8.8.0",
    "dotenv": "^16.0.0",
    "typescript": "^4.9.0"
  },
  "scripts": {
    "start": "node dist/index.js",
    "dev": "ts-node src/index.ts",
    "build": "tsc",
    "test": "jest"
  },
  "devDependencies": {
    "@types/express": "^4.17.0",
    "@types/cors": "^2.8.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/bcryptjs": "^2.4.0",
    "@types/pg": "^8.6.0",
    "jest": "^29.0.0",
    "supertest": "^6.3.0",
    "ts-node": "^10.9.0"
  }
}
EOF

    # Create Docker files
    cat > "$app_dir/docker-compose.yml" << 'EOF'
version: '3.8'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5000
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/todoapp
      - JWT_SECRET=your-secret-key
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=todoapp
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
EOF

    # Create basic TypeScript files
    cat > "$app_dir/frontend/src/App.tsx" << 'EOF'
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/" element={<Dashboard />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
EOF

    cat > "$app_dir/backend/src/index.ts" << 'EOF'
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import authRoutes from './routes/auth';
import todoRoutes from './routes/todos';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/todos', todoRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

export default app;
EOF

    # Create README
    cat > "$app_dir/README.md" << 'EOF'
# Todo List Application

A full-stack todo list application with user authentication.

## Features

- User registration and authentication
- Create, read, update, delete todos
- Responsive web interface
- RESTful API backend
- PostgreSQL database

## Getting Started

1. Clone the repository
2. Run `docker-compose up -d`
3. Access the application at http://localhost:3000

## API Endpoints

- POST /api/auth/register - User registration
- POST /api/auth/login - User login
- GET /api/todos - Get user todos
- POST /api/todos - Create new todo
- PUT /api/todos/:id - Update todo
- DELETE /api/todos/:id - Delete todo

## Technology Stack

- Frontend: React, TypeScript, Tailwind CSS
- Backend: Express.js, TypeScript
- Database: PostgreSQL
- Authentication: JWT
- Deployment: Docker
EOF

    echo "$app_dir"
}

run_test "Mock Application Structure Generation" "
    MOCK_APP_DIR=\$(create_mock_app)
    test -d \"\$MOCK_APP_DIR/frontend\" &&
    test -d \"\$MOCK_APP_DIR/backend\" &&
    test -f \"\$MOCK_APP_DIR/frontend/package.json\" &&
    test -f \"\$MOCK_APP_DIR/backend/package.json\" &&
    test -f \"\$MOCK_APP_DIR/docker-compose.yml\" &&
    test -f \"\$MOCK_APP_DIR/README.md\"
"

# Test 10: Mock Package.json Validation
run_test "Mock Package.json Validation" "
    MOCK_APP_DIR=\"$TEST_DIR/mock-todo-app\"
    node -e \"
        const frontendPkg = require('\$MOCK_APP_DIR/frontend/package.json');
        const backendPkg = require('\$MOCK_APP_DIR/backend/package.json');
        
        // Validate frontend dependencies
        if (!frontendPkg.dependencies.react) throw new Error('Missing React dependency');
        if (!frontendPkg.dependencies.typescript) throw new Error('Missing TypeScript dependency');
        if (!frontendPkg.devDependencies['@testing-library/react']) throw new Error('Missing testing library');
        
        // Validate backend dependencies
        if (!backendPkg.dependencies.express) throw new Error('Missing Express dependency');
        if (!backendPkg.dependencies.jsonwebtoken) throw new Error('Missing JWT dependency');
        if (!backendPkg.dependencies.pg) throw new Error('Missing PostgreSQL dependency');
        
        console.log('Package.json files validated successfully');
    \"
"

# Test 11: Docker Configuration Validation
run_test "Docker Configuration Validation" "
    MOCK_APP_DIR=\"$TEST_DIR/mock-todo-app\"
    # Check if docker-compose.yml has required services
    grep -q 'frontend:' \"\$MOCK_APP_DIR/docker-compose.yml\" &&
    grep -q 'backend:' \"\$MOCK_APP_DIR/docker-compose.yml\" &&
    grep -q 'db:' \"\$MOCK_APP_DIR/docker-compose.yml\" &&
    grep -q 'postgres:15' \"\$MOCK_APP_DIR/docker-compose.yml\"
"

# Test 12: TypeScript Configuration Validation
run_test "TypeScript File Structure Validation" "
    MOCK_APP_DIR=\"$TEST_DIR/mock-todo-app\"
    # Validate TypeScript files exist and have basic structure
    test -f \"\$MOCK_APP_DIR/frontend/src/App.tsx\" &&
    test -f \"\$MOCK_APP_DIR/backend/src/index.ts\" &&
    grep -q 'React' \"\$MOCK_APP_DIR/frontend/src/App.tsx\" &&
    grep -q 'express' \"\$MOCK_APP_DIR/backend/src/index.ts\"
"

# Test 13: API Endpoint Structure Validation
run_test "API Endpoint Structure Validation" "
    MOCK_APP_DIR=\"$TEST_DIR/mock-todo-app\"
    # Check if backend has proper API structure
    grep -q '/api/auth' \"\$MOCK_APP_DIR/backend/src/index.ts\" &&
    grep -q '/api/todos' \"\$MOCK_APP_DIR/backend/src/index.ts\" &&
    grep -q '/health' \"\$MOCK_APP_DIR/backend/src/index.ts\"
"

# Test 14: Security Configuration Validation
run_test "Security Configuration Validation" "
    MOCK_APP_DIR=\"$TEST_DIR/mock-todo-app\"
    # Check if security packages are included
    grep -q 'helmet' \"\$MOCK_APP_DIR/backend/package.json\" &&
    grep -q 'cors' \"\$MOCK_APP_DIR/backend/package.json\" &&
    grep -q 'bcryptjs' \"\$MOCK_APP_DIR/backend/package.json\" &&
    grep -q 'jsonwebtoken' \"\$MOCK_APP_DIR/backend/package.json\"
"

# Test 15: Documentation Quality Validation
run_test "Documentation Quality Validation" "
    MOCK_APP_DIR=\"$TEST_DIR/mock-todo-app\"
    # Check if README has essential sections
    grep -q 'Features' \"\$MOCK_APP_DIR/README.md\" &&
    grep -q 'Getting Started' \"\$MOCK_APP_DIR/README.md\" &&
    grep -q 'API Endpoints' \"\$MOCK_APP_DIR/README.md\" &&
    grep -q 'Technology Stack' \"\$MOCK_APP_DIR/README.md\"
"

# Test 16: Environment Configuration Validation
run_test "Environment Configuration Validation" "
    # Check if .env.example exists in project
    test -f '.env.example' &&
    grep -q 'OPENROUTER_API_KEY' '.env.example'
"

# Test 17: Logging System Validation
run_test "Logging System Validation" "
    # Check if logs directory exists and log file is created
    test -d 'logs' &&
    test -f 'logs/droidbotx.log'
"

# Test 18: CLI Error Handling Validation
run_test "CLI Error Handling Validation" "
    # Test CLI with invalid arguments (should fail gracefully)
    if npx ts-node src/cli/app.ts --invalid-option 2>/dev/null; then
        false  # Should fail
    else
        true   # Expected to fail gracefully
    fi
"

# Test 19: Quality Gates Configuration Validation
run_test "Quality Gates Configuration Validation" "
    # Check if quality gates are properly configured in the orchestrator
    grep -q 'qualityGates' src/orchestration/Orchestrator.ts &&
    grep -q 'validatePlanningOutput' src/orchestration/Orchestrator.ts &&
    grep -q 'validateCodingOutput' src/orchestration/Orchestrator.ts &&
    grep -q 'validateDeploymentOutput' src/orchestration/Orchestrator.ts
"

# Test 20: Agent Registration Validation
run_test "Agent Registration Validation" "
    # Verify all agents are properly registered
    node -e \"
        const { Orchestrator } = require('./dist/orchestration/Orchestrator');
        const { PlanningAgent } = require('./dist/agents/PlanningAgent');
        const { CodingAgent } = require('./dist/agents/CodingAgent');
        const { DeploymentAgent } = require('./dist/agents/DeploymentAgent');
        
        const orchestrator = new Orchestrator();
        const planningAgent = new PlanningAgent();
        const codingAgent = new CodingAgent();
        const deploymentAgent = new DeploymentAgent();
        
        orchestrator.registerAgent(planningAgent);
        orchestrator.registerAgent(codingAgent);
        orchestrator.registerAgent(deploymentAgent);
        
        console.log('All agents registered successfully');
    \"
"

# Performance and Load Testing
run_test "System Performance Validation" "
    # Test system can handle multiple concurrent operations
    for i in {1..5}; do
        node -e \"
            const { ConfigManager } = require('./dist/core/ConfigManager');
            const config = ConfigManager.getInstance();
            console.log('Config instance created: ' + i);
        \" &
    done
    wait
"

# Final Results
log ""
log "${BLUE}========================================${NC}"
log "${BLUE}End-to-End Testing Results${NC}"
log "${BLUE}========================================${NC}"
log "Total Tests: $TOTAL_TESTS"
log "${GREEN}Passed: $TESTS_PASSED${NC}"
log "${RED}Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    log "${GREEN}🎉 ALL TESTS PASSED! System is fully functional.${NC}"
    exit 0
else
    log "${RED}❌ Some tests failed. Check the log file for details: $LOG_FILE${NC}"
    exit 1
fi

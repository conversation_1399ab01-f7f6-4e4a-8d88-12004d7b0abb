# Todo List Application Specification

## Description
A simple todo list application with user authentication and task management capabilities.

## Requirements
- User registration and authentication
- Create, read, update, delete tasks
- Task categories and priorities
- Responsive web interface
- REST API backend
- PostgreSQL database

## Expected Features

### Authentication
- User registration with email and password
- User login with JWT token authentication
- Password hashing with bcrypt
- Protected routes requiring authentication

### Task Management
- Create new tasks with title, description, and priority
- Mark tasks as complete/incomplete
- Edit existing tasks
- Delete tasks
- Filter tasks by status (all, completed, pending)
- Search tasks by title or description

### User Interface
- Responsive design for mobile and desktop
- Clean, modern interface
- Real-time updates
- Loading states and error handling
- Form validation

### API Endpoints
- POST /api/auth/register - User registration
- POST /api/auth/login - User login
- GET /api/auth/profile - Get user profile
- GET /api/todos - Get user's todos
- POST /api/todos - Create new todo
- PUT /api/todos/:id - Update todo
- DELETE /api/todos/:id - Delete todo

### Database Schema
- Users table (id, email, password_hash, created_at, updated_at)
- Todos table (id, user_id, title, description, completed, priority, created_at, updated_at)

### Technology Stack
- Frontend: React with TypeScript, Tailwind CSS
- Backend: Express.js with TypeScript
- Database: PostgreSQL
- Authentication: JWT
- Testing: Jest, React Testing Library, Supertest
- Deployment: Docker, docker-compose

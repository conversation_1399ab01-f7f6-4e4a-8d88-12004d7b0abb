#!/usr/bin/env node

/**
 * qrXcode Application Generation Script
 * Uses enhanced DroidBotX with all Phase 1-3 improvements
 */

const { Orchestrator } = require('./dist/orchestration/Orchestrator');
const { PlanningAgent } = require('./dist/agents/PlanningAgent');
const { BusinessLogicAgent } = require('./dist/agents/BusinessLogicAgent');
const { CodingAgent } = require('./dist/agents/CodingAgent');
const { DatabaseAgent } = require('./dist/agents/DatabaseAgent');
const { TestingAgent } = require('./dist/agents/TestingAgent');
const { DeploymentAgent } = require('./dist/agents/DeploymentAgent');
const { Logger } = require('./dist/core/Logger');
const fs = require('fs');
const path = require('path');

class QRXcodeGenerator {
  constructor() {
    this.logger = Logger.getInstance();
    this.orchestrator = new Orchestrator();
    this.projectPath = path.join(__dirname, 'qrXcode');
    this.qualityMetrics = {
      testPassRate: 0,
      codeQuality: 0,
      securityScore: 0,
      performanceScore: 0
    };
  }

  async initialize() {
    this.logger.info('🚀 Starting qrXcode application generation with enhanced DroidBotX');
    
    // Register all agents
    await this.registerAgents();
    
    // Load project configuration
    const configPath = path.join(this.projectPath, 'project-config.json');
    this.projectConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    
    this.logger.info('✅ DroidBotX initialized with 6-agent architecture');
  }

  async registerAgents() {
    // Register agents in execution order
    this.orchestrator.registerAgent('planning', new PlanningAgent());
    this.orchestrator.registerAgent('business-logic', new BusinessLogicAgent());
    this.orchestrator.registerAgent('database', new DatabaseAgent());
    this.orchestrator.registerAgent('coding', new CodingAgent());
    this.orchestrator.registerAgent('testing', new TestingAgent());
    this.orchestrator.registerAgent('deployment', new DeploymentAgent());
    
    this.logger.info('✅ All 6 core agents registered successfully');
  }

  async generateApplication() {
    try {
      this.logger.info('🎯 Phase 1: Planning and Architecture');
      await this.executePlanningPhase();
      
      this.logger.info('🎯 Phase 2: Business Logic and API Design');
      await this.executeBusinessLogicPhase();
      
      this.logger.info('🎯 Phase 3: Database Schema and Synchronization');
      await this.executeDatabasePhase();
      
      this.logger.info('🎯 Phase 4: AI-Driven Code Generation');
      await this.executeCodingPhase();
      
      this.logger.info('🎯 Phase 5: Comprehensive Testing');
      await this.executeTestingPhase();
      
      this.logger.info('🎯 Phase 6: Production Deployment');
      await this.executeDeploymentPhase();
      
      this.logger.info('🎉 qrXcode application generation completed successfully!');
      await this.generateReport();
      
    } catch (error) {
      this.logger.error('❌ Application generation failed', { error: error.message });
      throw error;
    }
  }

  async executePlanningPhase() {
    const requirements = {
      projectName: 'qrXcode',
      description: 'Comprehensive QR code generator and reader application',
      features: this.projectConfig.features,
      architecture: this.projectConfig.architecture,
      requirements: this.projectConfig.requirements
    };

    const planningResult = await this.orchestrator.executeWorkflow({
      phase: 'PLAN',
      requirements,
      projectPath: this.projectPath
    });

    this.technicalSpec = planningResult.data;
    this.logger.info('✅ Technical specification generated', {
      components: Object.keys(this.technicalSpec.components || {}).length,
      apis: Object.keys(this.technicalSpec.apis || {}).length
    });
  }

  async executeBusinessLogicPhase() {
    const businessLogicResult = await this.orchestrator.executeWorkflow({
      phase: 'BUSINESS_LOGIC',
      technicalSpec: this.technicalSpec,
      projectPath: this.projectPath,
      dataModels: this.projectConfig.dataModels,
      apiEndpoints: this.projectConfig.apiEndpoints
    });

    this.businessLogic = businessLogicResult.data;
    this.logger.info('✅ Business logic and APIs generated', {
      domainAPIs: Object.keys(this.businessLogic.domainAPIs || {}).length,
      databaseTables: this.businessLogic.databaseSchema?.tables?.length || 0,
      openAPISpec: !!this.businessLogic.openAPISpec
    });
  }

  async executeDatabasePhase() {
    const databaseResult = await this.orchestrator.executeWorkflow({
      phase: 'DATABASE',
      businessLogic: this.businessLogic,
      projectPath: this.projectPath,
      synchronization: true,
      typeGeneration: true
    });

    this.databaseSetup = databaseResult.data;
    this.logger.info('✅ Database schema and synchronization completed', {
      tables: this.databaseSetup.tables?.length || 0,
      interfaces: this.databaseSetup.interfaces?.length || 0,
      migrations: this.databaseSetup.migrations?.length || 0
    });
  }

  async executeCodingPhase() {
    const codingResult = await this.orchestrator.executeWorkflow({
      phase: 'GENERATE',
      technicalSpec: this.technicalSpec,
      businessLogic: this.businessLogic,
      databaseSetup: this.databaseSetup,
      projectPath: this.projectPath,
      aiDriven: true,
      qualityGates: true
    });

    this.generatedCode = codingResult.data;
    this.logger.info('✅ AI-driven code generation completed', {
      files: Object.keys(this.generatedCode.files || {}).length,
      frontend: !!this.generatedCode.frontend,
      backend: !!this.generatedCode.backend,
      qualityScore: this.generatedCode.qualityScore || 0
    });

    this.qualityMetrics.codeQuality = this.generatedCode.qualityScore || 0;
  }

  async executeTestingPhase() {
    const testingResult = await this.orchestrator.executeWorkflow({
      phase: 'TESTING',
      generatedCode: this.generatedCode,
      businessLogic: this.businessLogic,
      databaseSetup: this.databaseSetup,
      projectPath: this.projectPath,
      comprehensive: true,
      coverage: 90
    });

    this.testResults = testingResult.data;
    this.logger.info('✅ Comprehensive testing completed', {
      testSuites: this.testResults.testSuites?.length || 0,
      coverage: this.testResults.coverage || 0,
      passed: this.testResults.passed || 0,
      failed: this.testResults.failed || 0
    });

    this.qualityMetrics.testPassRate = this.testResults.passRate || 0;
  }

  async executeDeploymentPhase() {
    const deploymentResult = await this.orchestrator.executeWorkflow({
      phase: 'DEPLOY',
      generatedCode: this.generatedCode,
      testResults: this.testResults,
      projectPath: this.projectPath,
      environment: 'production',
      cicd: true,
      monitoring: true,
      security: true
    });

    this.deploymentSetup = deploymentResult.data;
    this.logger.info('✅ Production deployment infrastructure completed', {
      containers: this.deploymentSetup.containers?.length || 0,
      manifests: this.deploymentSetup.manifests?.length || 0,
      pipelines: this.deploymentSetup.pipelines?.length || 0,
      monitoring: !!this.deploymentSetup.monitoring
    });

    this.qualityMetrics.securityScore = this.deploymentSetup.securityScore || 0;
    this.qualityMetrics.performanceScore = this.deploymentSetup.performanceScore || 0;
  }

  async generateReport() {
    const report = {
      project: {
        name: 'qrXcode',
        description: 'QR Code Generator and Reader Application',
        generatedAt: new Date().toISOString(),
        version: '1.0.0'
      },
      architecture: {
        agents: 6,
        phases: ['PLAN', 'BUSINESS_LOGIC', 'DATABASE', 'GENERATE', 'TESTING', 'DEPLOY'],
        improvements: ['Phase 1: Foundation', 'Phase 2: Integration', 'Phase 3: Production']
      },
      metrics: this.qualityMetrics,
      artifacts: {
        technicalSpec: !!this.technicalSpec,
        businessLogic: !!this.businessLogic,
        databaseSetup: !!this.databaseSetup,
        generatedCode: !!this.generatedCode,
        testResults: !!this.testResults,
        deploymentSetup: !!this.deploymentSetup
      },
      files: {
        total: this.countGeneratedFiles(),
        frontend: this.countFrontendFiles(),
        backend: this.countBackendFiles(),
        tests: this.countTestFiles(),
        deployment: this.countDeploymentFiles()
      },
      success: this.qualityMetrics.testPassRate >= 90
    };

    const reportPath = path.join(this.projectPath, 'generation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.logger.info('📊 Generation report created', {
      reportPath,
      success: report.success,
      metrics: this.qualityMetrics
    });

    return report;
  }

  countGeneratedFiles() {
    // Count all generated files
    return this.countFilesRecursively(this.projectPath);
  }

  countFrontendFiles() {
    const frontendPath = path.join(this.projectPath, 'frontend');
    return fs.existsSync(frontendPath) ? this.countFilesRecursively(frontendPath) : 0;
  }

  countBackendFiles() {
    const backendPath = path.join(this.projectPath, 'backend');
    return fs.existsSync(backendPath) ? this.countFilesRecursively(backendPath) : 0;
  }

  countTestFiles() {
    const testsPath = path.join(this.projectPath, 'tests');
    return fs.existsSync(testsPath) ? this.countFilesRecursively(testsPath) : 0;
  }

  countDeploymentFiles() {
    const deployPath = path.join(this.projectPath, 'deployment');
    return fs.existsSync(deployPath) ? this.countFilesRecursively(deployPath) : 0;
  }

  countFilesRecursively(dir) {
    if (!fs.existsSync(dir)) return 0;
    
    let count = 0;
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        count += this.countFilesRecursively(fullPath);
      } else {
        count++;
      }
    }
    
    return count;
  }
}

// Main execution
async function main() {
  const generator = new QRXcodeGenerator();
  
  try {
    await generator.initialize();
    await generator.generateApplication();
    
    console.log('\n🎉 qrXcode application generated successfully!');
    console.log('📁 Project location:', generator.projectPath);
    console.log('📊 Quality metrics:', generator.qualityMetrics);
    
  } catch (error) {
    console.error('\n❌ Generation failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { QRXcodeGenerator };

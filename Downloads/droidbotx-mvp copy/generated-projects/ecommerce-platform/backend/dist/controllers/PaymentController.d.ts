import { Payment } from '../models/Payment';
import { PaymentRepository } from '../repositories/PaymentRepository';
interface PaymentService {
    defaultOperation(paymentId: number): Promise<Payment>;
}
declare class PaymentServiceImpl implements PaymentService {
    private paymentRepository;
    private logger;
    constructor(paymentRepository: PaymentRepository);
    defaultOperation(paymentId: number): Promise<Payment>;
}
export { PaymentService, PaymentServiceImpl };
//# sourceMappingURL=PaymentController.d.ts.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentServiceImpl = void 0;
const CustomError_1 = require("../errors/CustomError");
const Logger_1 = require("../utils/Logger");
class PaymentServiceImpl {
    constructor(paymentRepository) {
        this.paymentRepository = paymentRepository;
        this.logger = new Logger_1.Logger();
    }
    async defaultOperation(paymentId) {
        try {
            this.logger.info(`Fetching payment with ID: ${paymentId}`);
            const payment = await this.paymentRepository.findById(paymentId);
            if (!payment) {
                this.logger.error(`Payment with ID ${paymentId} not found`);
                throw new CustomError_1.CustomError('Payment not found', 404);
            }
            this.logger.info(`Payment with ID ${paymentId} fetched successfully`);
            return payment;
        }
        catch (error) {
            this.logger.error(`Error fetching payment with ID ${paymentId}: ${error.message}`);
            throw new CustomError_1.CustomError('Failed to fetch payment', 500);
        }
    }
}
exports.PaymentServiceImpl = PaymentServiceImpl;
//# sourceMappingURL=PaymentController.js.map
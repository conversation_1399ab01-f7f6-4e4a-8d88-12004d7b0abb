import { Logger } from 'winston';
import { Product } from '../models/Product';
import { ProductRepository } from '../repositories/ProductRepository';
interface ProductService {
    getProductById(id: number): Promise<Product>;
}
export declare class ProductServiceImplementation implements ProductService {
    private readonly logger;
    private readonly productRepository;
    constructor(logger: Logger, productRepository: ProductRepository);
    getProductById(id: number): Promise<Product>;
    private validateProductId;
}
export {};
//# sourceMappingURL=ProductController.d.ts.map
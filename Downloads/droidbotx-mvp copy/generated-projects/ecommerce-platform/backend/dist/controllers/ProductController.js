"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductServiceImplementation = void 0;
const ProductNotFoundException_1 = require("../exceptions/ProductNotFoundException");
const ProductValidationException_1 = require("../exceptions/ProductValidationException");
class ProductServiceImplementation {
    constructor(logger, productRepository) {
        this.logger = logger;
        this.productRepository = productRepository;
    }
    async getProductById(id) {
        this.logger.debug(`Fetching product with ID: ${id}`);
        this.validateProductId(id);
        try {
            const product = await this.productRepository.findById(id);
            if (!product) {
                throw new ProductNotFoundException_1.ProductNotFoundException(`Product with ID ${id} not found`);
            }
            this.logger.debug(`Product found: ${JSON.stringify(product)}`);
            return product;
        }
        catch (error) {
            this.logger.error(`Error fetching product with ID ${id}: ${error.message}`);
            throw error;
        }
    }
    validateProductId(id) {
        if (id <= 0) {
            this.logger.error(`Invalid product ID: ${id}`);
            throw new ProductValidationException_1.ProductValidationException(`Product ID must be a positive number`);
        }
    }
}
exports.ProductServiceImplementation = ProductServiceImplementation;
//# sourceMappingURL=ProductController.js.map
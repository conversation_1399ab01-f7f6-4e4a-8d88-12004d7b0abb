/**
 * Jest Setup File for DroidBotX Phase 3 Testing
 * Configures Jest globals and mocks for proper test execution
 */

// Set required environment variables for tests
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error';
process.env.OPENROUTER_API_KEY = 'test-api-key';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';

// Configure Jest globals for test environment
global.jest = require('jest');

// Mock common modules that might not be available in test environment
jest.mock('fs', () => ({
  promises: {
    writeFile: jest.fn().mockResolvedValue(undefined),
    readFile: jest.fn().mockResolvedValue(''),
    mkdir: jest.fn().mockResolvedValue(undefined),
    access: jest.fn().mockResolvedValue(undefined)
  },
  existsSync: jest.fn().mockReturnValue(true),
  writeFileSync: jest.fn(),
  readFileSync: jest.fn().mockReturnValue(''),
  mkdirSync: jest.fn()
}));

jest.mock('path', () => ({
  join: jest.fn((...args) => args.join('/')),
  resolve: jest.fn((...args) => '/' + args.join('/')),
  dirname: jest.fn((path) => path.split('/').slice(0, -1).join('/')),
  basename: jest.fn((path, ext) => {
    const name = path.split('/').pop() || '';
    return ext ? name.replace(ext, '') : name;
  }),
  extname: jest.fn((path) => {
    const parts = path.split('.');
    return parts.length > 1 ? '.' + parts.pop() : '';
  })
}));

// Mock database connections
jest.mock('pg', () => ({
  Pool: jest.fn().mockImplementation(() => ({
    query: jest.fn().mockResolvedValue({ rows: [], rowCount: 0 }),
    connect: jest.fn().mockResolvedValue({
      query: jest.fn().mockResolvedValue({ rows: [], rowCount: 0 }),
      release: jest.fn()
    }),
    end: jest.fn().mockResolvedValue(undefined)
  }))
}));

// Mock axios for HTTP requests
const mockAxiosInstance = {
  post: jest.fn().mockResolvedValue({
    data: {
      choices: [{
        message: {
          content: '{"projectName": "test-app", "description": "Test application", "features": ["test"], "dependencies": {"frontend": ["react"], "backend": ["express"]}, "architecture": {"type": "monolith"}}'
        }
      }]
    }
  }),
  get: jest.fn().mockResolvedValue({ data: { success: true } }),
  put: jest.fn().mockResolvedValue({ data: { success: true } }),
  delete: jest.fn().mockResolvedValue({ data: { success: true } })
};

jest.mock('axios', () => ({
  __esModule: true,
  default: {
    create: jest.fn(() => mockAxiosInstance),
    post: jest.fn().mockResolvedValue({ data: { success: true } }),
    get: jest.fn().mockResolvedValue({ data: { success: true } }),
    put: jest.fn().mockResolvedValue({ data: { success: true } }),
    delete: jest.fn().mockResolvedValue({ data: { success: true } })
  }
}));

// Mock TypeScript compiler to prevent Jest issues
jest.mock('typescript', () => ({
  createProgram: jest.fn(),
  getPreEmitDiagnostics: jest.fn().mockReturnValue([]),
  formatDiagnosticsWithColorAndContext: jest.fn().mockReturnValue(''),
  sys: {
    newLine: '\n',
    useCaseSensitiveFileNames: true
  },
  ScriptTarget: {
    ES2020: 7
  },
  ModuleKind: {
    CommonJS: 1
  }
}));

// Configure test timeout
jest.setTimeout(30000);

// Global test utilities
global.createMockEntity = () => ({
  name: 'TestEntity',
  description: 'Test entity for unit testing',
  fields: [
    { name: 'id', type: 'uuid', required: true, description: 'Entity ID' },
    { name: 'name', type: 'string', required: true, description: 'Entity name' }
  ],
  relationships: [],
  operations: [
    { name: 'create', type: 'create', description: 'Create entity', parameters: [], businessRules: [] },
    { name: 'findById', type: 'read', description: 'Find by ID', parameters: [], businessRules: [] }
  ],
  validations: []
});

global.createMockSemanticAnalysis = () => ({
  domain: 'test-domain',
  confidence: 0.9,
  entities: [global.createMockEntity()],
  workflows: [],
  userRoles: ['user', 'admin'],
  technicalRequirements: [],
  codeGenerationContext: {
    primaryFrameworks: { frontend: 'React', backend: 'Express', database: 'PostgreSQL' },
    architecturalPatterns: ['MVC'],
    securityRequirements: ['JWT'],
    performanceRequirements: [],
    integrationRequirements: []
  }
});

global.createMockTechnicalSpec = () => ({
  projectName: 'test-project',
  businessDomain: 'test-domain',
  architecture: { type: 'microservices' }
});

// Console override for cleaner test output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: originalConsole.error // Keep error for debugging
};

// Restore console after tests if needed
afterAll(() => {
  global.console = originalConsole;
});

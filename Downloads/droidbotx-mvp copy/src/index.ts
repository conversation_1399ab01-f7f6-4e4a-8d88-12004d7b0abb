
import { ConfigManager } from './core/ConfigManager';
import { Logger } from './core/Logger';
import { LLMProviderSystem } from './core/LLMProviderSystem';
import { ToolManager } from './core/ToolManager';
import { Orchestrator } from './orchestration/Orchestrator';
import { PlanningAgent, CodingAgent, DeploymentAgent } from './agents';
import { TestingAgent } from './agents/TestingAgent';
import { BusinessLogicAgent } from './agents/BusinessLogicAgent';
import { DatabaseAgent } from './agents/DatabaseAgent';

export class DroidBotX {
  private config: ConfigManager;
  private logger: Logger;
  private llmProvider: LLMProviderSystem;
  private toolManager: ToolManager;
  private orchestrator: Orchestrator;

  constructor() {
    this.config = ConfigManager.getInstance();
    this.logger = Logger.getInstance();
    this.llmProvider = LLMProviderSystem.getInstance();
    this.toolManager = ToolManager.getInstance();
    this.orchestrator = Orchestrator.getInstance();
  }

  public async initialize(): Promise<void> {
    this.logger.info('Initializing DroidBotX MVP...');

    try {
      // Test LLM connection
      await this.testLLMConnection();

      // Register core agents (simplified from 10 to 6)
      const planningAgent = new PlanningAgent();
      const businessLogicAgent = new BusinessLogicAgent();
      const codingAgent = new CodingAgent();
      const databaseAgent = new DatabaseAgent();
      const testingAgent = new TestingAgent();
      const deploymentAgent = new DeploymentAgent();

      this.orchestrator.registerAgent(planningAgent);
      this.orchestrator.registerAgent(businessLogicAgent);
      this.orchestrator.registerAgent(codingAgent);
      this.orchestrator.registerAgent(databaseAgent);
      this.orchestrator.registerAgent(testingAgent);
      this.orchestrator.registerAgent(deploymentAgent);

      this.logger.info('Core agents registered successfully', {
        agents: [
          planningAgent.getName(),
          businessLogicAgent.getName(),
          codingAgent.getName(),
          databaseAgent.getName(),
          testingAgent.getName(),
          deploymentAgent.getName()
        ],
      });

      this.logger.info('DroidBotX MVP initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize DroidBotX MVP', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  private async testLLMConnection(): Promise<void> {
    try {
      const response = await this.llmProvider.generateSingleResponse(
        'Hello! Please respond with "Connection successful" to confirm the LLM is working.',
        { maxTokens: 50 }
      );

      this.logger.info('LLM connection test completed', {
        response: response.substring(0, 100),
      });

    } catch (error) {
      throw new Error(`LLM connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public getOrchestrator(): Orchestrator {
    return this.orchestrator;
  }

  public getConfig(): ConfigManager {
    return this.config;
  }
}

// Export main classes for external use
export {
  ConfigManager,
  Logger,
  LLMProviderSystem,
  ToolManager,
  BaseAgent,
} from './core';

export {
  Orchestrator,
} from './orchestration';

export {
  PlanningAgent,
  BusinessLogicAgent,
  CodingAgent,
  TestingAgent,
  DeploymentAgent,
} from './agents';

export type {
  WorkflowPhase,
} from './orchestration';

export type {
  TechnicalSpecification,
  GeneratedCode,
  DeploymentConfiguration,
} from './agents';

// Main entry point for testing
async function main(): Promise<void> {
  try {
    const droidBotX = new DroidBotX();
    await droidBotX.initialize();

    console.log('DroidBotX MVP is ready!');
    console.log('Available configuration:');
    console.log('- OpenRouter Model:', droidBotX.getConfig().openRouter.defaultModel);
    console.log('- Log Level:', droidBotX.getConfig().logging.level);
    console.log('- Environment:', droidBotX.getConfig().application.nodeEnv);

  } catch (error) {
    console.error('Failed to start DroidBotX MVP:', error);
    process.exit(1);
  }
}

// Run main function if this file is executed directly
if (require.main === module) {
  main();
}

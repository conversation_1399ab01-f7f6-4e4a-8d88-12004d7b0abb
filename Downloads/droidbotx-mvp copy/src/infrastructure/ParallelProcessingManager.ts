import { Logger } from '../core/Logger';
import { WorkflowPhase } from '../orchestration/Orchestrator';
import { AgentTask, AgentResult } from '../core/BaseAgent';

export interface ParallelTask {
  id: string;
  phase: WorkflowPhase;
  task: AgentTask;
  dependencies: WorkflowPhase[];
  priority: number;
  estimatedDuration: number;
  canRunInParallel: boolean;
}

export interface ParallelExecutionPlan {
  batches: ParallelTask[][];
  totalEstimatedTime: number;
  parallelizationOpportunities: number;
  sequentialFallback: ParallelTask[];
}

export interface ParallelExecutionResult {
  results: Map<WorkflowPhase, AgentResult>;
  executionTime: number;
  parallelEfficiency: number;
  errors: Array<{ phase: WorkflowPhase; error: string }>;
}

export class ParallelProcessingManager {
  private static instance: ParallelProcessingManager;
  private logger: Logger;
  private maxConcurrentTasks: number = 3;
  private taskQueue: ParallelTask[] = [];
  private runningTasks: Map<string, Promise<AgentResult>> = new Map();
  private completedTasks: Map<WorkflowPhase, AgentResult> = new Map();

  // Define which phases can run in parallel
  private parallelizationRules = {
    // Phases that can run independently within themselves
    independentPhases: [
      WorkflowPhase.DATABASE
    ],

    // Phases that can run in parallel with others if dependencies are met
    conditionalParallel: new Map<WorkflowPhase, WorkflowPhase[]>([
      [WorkflowPhase.DATABASE, [WorkflowPhase.GENERATE]], // Database can run with Generate
    ]),

    // Strict sequential dependencies
    strictSequential: new Map<WorkflowPhase, WorkflowPhase[]>([
      [WorkflowPhase.BUSINESS_LOGIC, [WorkflowPhase.PLAN]],
      [WorkflowPhase.GENERATE, [WorkflowPhase.BUSINESS_LOGIC]],
      [WorkflowPhase.TESTING, [WorkflowPhase.GENERATE, WorkflowPhase.DATABASE]],
      [WorkflowPhase.DEPLOY, [WorkflowPhase.TESTING]]
    ])
  };

  private constructor() {
    this.logger = Logger.getInstance();
  }

  public static getInstance(): ParallelProcessingManager {
    if (!ParallelProcessingManager.instance) {
      ParallelProcessingManager.instance = new ParallelProcessingManager();
    }
    return ParallelProcessingManager.instance;
  }

  /**
   * Create execution plan with parallel processing opportunities
   */
  public createExecutionPlan(tasks: ParallelTask[]): ParallelExecutionPlan {
    const batches: ParallelTask[][] = [];
    const remainingTasks = [...tasks];
    const completedPhases = new Set<WorkflowPhase>();

    // Sort tasks by priority and dependencies
    remainingTasks.sort((a, b) => {
      // First by dependency depth, then by priority
      const aDeps = this.getDependencyDepth(a.phase);
      const bDeps = this.getDependencyDepth(b.phase);
      
      if (aDeps !== bDeps) {
        return aDeps - bDeps;
      }
      
      return b.priority - a.priority;
    });

    while (remainingTasks.length > 0) {
      const currentBatch: ParallelTask[] = [];
      const batchTasks = [...remainingTasks];

      for (const task of batchTasks) {
        // Check if all dependencies are satisfied
        const canExecute = this.canExecuteTask(task, completedPhases);
        
        // Check if we can add to current batch
        const canAddToBatch = canExecute && 
          (currentBatch.length === 0 || this.canRunInParallel(task, currentBatch));

        if (canAddToBatch && currentBatch.length < this.maxConcurrentTasks) {
          currentBatch.push(task);
          remainingTasks.splice(remainingTasks.indexOf(task), 1);
        }
      }

      if (currentBatch.length === 0) {
        // If no tasks can be executed, take the first one (dependency issue)
        const forcedTask = remainingTasks.shift();
        if (forcedTask) {
          currentBatch.push(forcedTask);
          this.logger.warn('Forced sequential execution due to dependencies', {
            phase: forcedTask.phase,
            dependencies: forcedTask.dependencies
          });
        }
      }

      if (currentBatch.length > 0) {
        batches.push(currentBatch);
        
        // Mark phases as completed for dependency checking
        for (const task of currentBatch) {
          completedPhases.add(task.phase);
        }
      }
    }

    const totalEstimatedTime = this.calculateTotalTime(batches);
    const parallelizationOpportunities = batches.filter(batch => batch.length > 1).length;

    return {
      batches,
      totalEstimatedTime,
      parallelizationOpportunities,
      sequentialFallback: tasks
    };
  }

  /**
   * Execute tasks in parallel according to the execution plan
   */
  public async executeParallelPlan(
    plan: ParallelExecutionPlan,
    executor: (task: AgentTask) => Promise<AgentResult>
  ): Promise<ParallelExecutionResult> {
    const startTime = Date.now();
    const results = new Map<WorkflowPhase, AgentResult>();
    const errors: Array<{ phase: WorkflowPhase; error: string }> = [];

    this.logger.info('Starting parallel execution plan', {
      totalBatches: plan.batches.length,
      parallelizationOpportunities: plan.parallelizationOpportunities,
      estimatedTime: plan.totalEstimatedTime
    });

    for (let batchIndex = 0; batchIndex < plan.batches.length; batchIndex++) {
      const batch = plan.batches[batchIndex];
      
      this.logger.info(`Executing batch ${batchIndex + 1}/${plan.batches.length}`, {
        tasksInBatch: batch.length,
        phases: batch.map(t => t.phase)
      });

      // Execute all tasks in the current batch in parallel
      const batchPromises = batch.map(async (parallelTask) => {
        try {
          const result = await executor(parallelTask.task);
          results.set(parallelTask.phase, result);
          
          this.logger.info('Parallel task completed', {
            phase: parallelTask.phase,
            success: result.success
          });
          
          return { phase: parallelTask.phase, result };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          errors.push({ phase: parallelTask.phase, error: errorMessage });
          
          this.logger.error('Parallel task failed', {
            phase: parallelTask.phase,
            error: errorMessage
          });
          
          throw error;
        }
      });

      // Wait for all tasks in the batch to complete
      try {
        await Promise.all(batchPromises);
      } catch (error) {
        // Continue with next batch even if some tasks fail
        this.logger.warn('Some tasks in batch failed, continuing with next batch', {
          batchIndex,
          errors: errors.length
        });
      }
    }

    const executionTime = Date.now() - startTime;
    const sequentialTime = plan.sequentialFallback.reduce((sum, task) => sum + task.estimatedDuration, 0);
    const parallelEfficiency = sequentialTime > 0 ? (sequentialTime - executionTime) / sequentialTime : 0;

    this.logger.info('Parallel execution completed', {
      executionTime,
      parallelEfficiency,
      tasksCompleted: results.size,
      errors: errors.length
    });

    return {
      results,
      executionTime,
      parallelEfficiency,
      errors
    };
  }

  /**
   * Identify parallel processing opportunities within a single agent
   */
  public identifyIntraAgentParallelism(phase: WorkflowPhase, taskData: any): string[] {
    const opportunities: string[] = [];

    switch (phase) {
      case WorkflowPhase.GENERATE:
        if (taskData?.businessLogic?.domainAPIs) {
          const apiCount = Object.keys(taskData.businessLogic.domainAPIs).length;
          if (apiCount > 3) {
            opportunities.push(`Generate ${apiCount} APIs in parallel`);
          }
        }
        if (taskData?.businessLogic?.databaseSchema?.tables) {
          const tableCount = taskData.businessLogic.databaseSchema.tables.length;
          if (tableCount > 2) {
            opportunities.push(`Generate ${tableCount} database models in parallel`);
          }
        }
        break;

      case WorkflowPhase.TESTING:
        if (taskData?.allPreviousResults) {
          const phases = Object.keys(taskData.allPreviousResults);
          if (phases.length > 2) {
            opportunities.push(`Run tests for ${phases.length} components in parallel`);
          }
        }
        break;

      case WorkflowPhase.DATABASE:
        opportunities.push('Run schema validation and migration generation in parallel');
        break;
    }

    return opportunities;
  }

  /**
   * Check if a task can be executed given completed phases
   */
  private canExecuteTask(task: ParallelTask, completedPhases: Set<WorkflowPhase>): boolean {
    // Check strict sequential dependencies
    const strictDeps = this.parallelizationRules.strictSequential.get(task.phase) || [];
    for (const dep of strictDeps) {
      if (!completedPhases.has(dep)) {
        return false;
      }
    }

    // Check task-specific dependencies
    for (const dep of task.dependencies) {
      if (!completedPhases.has(dep)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Check if a task can run in parallel with other tasks in the batch
   */
  private canRunInParallel(task: ParallelTask, currentBatch: ParallelTask[]): boolean {
    if (!task.canRunInParallel) {
      return currentBatch.length === 0;
    }

    // Check if this phase can run with phases in current batch
    const conditionalParallel = this.parallelizationRules.conditionalParallel.get(task.phase) || [];
    const batchPhases = currentBatch.map(t => t.phase);

    // If no conditional rules, check if it's an independent phase
    if (conditionalParallel.length === 0) {
      return this.parallelizationRules.independentPhases.includes(task.phase) ||
             currentBatch.every(t => this.parallelizationRules.independentPhases.includes(t.phase));
    }

    // Check conditional parallel rules
    return batchPhases.every(phase => conditionalParallel.includes(phase));
  }

  /**
   * Get dependency depth for a phase
   */
  private getDependencyDepth(phase: WorkflowPhase): number {
    const visited = new Set<WorkflowPhase>();
    
    const calculateDepth = (currentPhase: WorkflowPhase): number => {
      if (visited.has(currentPhase)) {
        return 0; // Avoid cycles
      }
      
      visited.add(currentPhase);
      const deps = this.parallelizationRules.strictSequential.get(currentPhase) || [];
      
      if (deps.length === 0) {
        return 0;
      }
      
      return 1 + Math.max(...deps.map(dep => calculateDepth(dep)));
    };

    return calculateDepth(phase);
  }

  /**
   * Calculate total estimated time for execution plan
   */
  private calculateTotalTime(batches: ParallelTask[][]): number {
    return batches.reduce((total, batch) => {
      // Time for a batch is the maximum time of tasks in that batch
      const batchTime = Math.max(...batch.map(task => task.estimatedDuration));
      return total + batchTime;
    }, 0);
  }

  /**
   * Create parallel task from agent task
   */
  public createParallelTask(
    phase: WorkflowPhase,
    task: AgentTask,
    dependencies: WorkflowPhase[] = [],
    priority: number = 1,
    estimatedDuration: number = 60000 // Default 1 minute
  ): ParallelTask {
    return {
      id: task.id,
      phase,
      task,
      dependencies,
      priority,
      estimatedDuration,
      canRunInParallel: this.isPhaseParallelizable(phase)
    };
  }

  /**
   * Check if a phase can be parallelized
   */
  private isPhaseParallelizable(phase: WorkflowPhase): boolean {
    return this.parallelizationRules.independentPhases.includes(phase) ||
           this.parallelizationRules.conditionalParallel.has(phase);
  }

  /**
   * Get parallel processing recommendations
   */
  public getParallelizationRecommendations(tasks: ParallelTask[]): string[] {
    const recommendations: string[] = [];
    const plan = this.createExecutionPlan(tasks);

    if (plan.parallelizationOpportunities === 0) {
      recommendations.push('No parallel processing opportunities found - consider breaking down large tasks');
    } else {
      recommendations.push(`${plan.parallelizationOpportunities} parallel processing opportunities identified`);
    }

    const sequentialTime = tasks.reduce((sum, task) => sum + task.estimatedDuration, 0);
    const potentialSavings = sequentialTime - plan.totalEstimatedTime;
    const savingsPercentage = Math.round((potentialSavings / sequentialTime) * 100);

    if (savingsPercentage > 0) {
      recommendations.push(`Potential time savings: ${savingsPercentage}% (${Math.round(potentialSavings / 1000)}s)`);
    }

    // Check for bottlenecks
    const longTasks = tasks.filter(task => task.estimatedDuration > 120000); // > 2 minutes
    if (longTasks.length > 0) {
      recommendations.push(`Consider optimizing ${longTasks.length} long-running tasks: ${longTasks.map(t => t.phase).join(', ')}`);
    }

    return recommendations;
  }

  /**
   * Set maximum concurrent tasks
   */
  public setMaxConcurrentTasks(max: number): void {
    this.maxConcurrentTasks = Math.max(1, Math.min(max, 10)); // Limit between 1-10
    this.logger.info('Max concurrent tasks updated', { maxConcurrentTasks: this.maxConcurrentTasks });
  }

  /**
   * Get current parallel processing statistics
   */
  public getParallelProcessingStats() {
    return {
      maxConcurrentTasks: this.maxConcurrentTasks,
      runningTasks: this.runningTasks.size,
      queuedTasks: this.taskQueue.length,
      completedTasks: this.completedTasks.size,
      independentPhases: this.parallelizationRules.independentPhases,
      conditionalParallelRules: Object.fromEntries(this.parallelizationRules.conditionalParallel),
      strictSequentialRules: Object.fromEntries(this.parallelizationRules.strictSequential)
    };
  }
}

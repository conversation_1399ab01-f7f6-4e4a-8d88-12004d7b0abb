import { Logger } from '../core/Logger';
import { WorkflowPhase } from './Orchestrator';
import { AgentResult } from '../core/BaseAgent';

export interface DependencyRule {
  phase: WorkflowPhase;
  requiredPhases: WorkflowPhase[];
  requiredData: string[];
  optionalData: string[];
  validationRules: ValidationRule[];
}

export interface ValidationRule {
  name: string;
  description: string;
  validator: (data: any) => boolean;
  severity: 'critical' | 'high' | 'medium' | 'low';
  errorMessage: string;
}

export interface DependencyValidationResult {
  success: boolean;
  issues: string[];
  warnings: string[];
  missingDependencies: string[];
  score: number;
}

export interface PhaseDataRequirement {
  key: string;
  type: 'required' | 'optional';
  validator?: (value: any) => boolean;
  description: string;
}

export class DependencyValidator {
  private logger: Logger;
  private dependencyRules: Map<WorkflowPhase, DependencyRule>;

  constructor() {
    this.logger = Logger.getInstance();
    this.dependencyRules = new Map();
    this.initializeDependencyRules();
  }

  /**
   * Validate dependencies for a phase
   */
  public async validateDependencies(
    phase: WorkflowPhase,
    previousResult?: AgentResult,
    sharedState?: Map<string, any>
  ): Promise<DependencyValidationResult> {
    this.logger.debug('Validating dependencies', { phase });

    const rule = this.dependencyRules.get(phase);
    if (!rule) {
      return {
        success: true,
        issues: [],
        warnings: [`No dependency rules defined for phase: ${phase}`],
        missingDependencies: [],
        score: 100
      };
    }

    const issues: string[] = [];
    const warnings: string[] = [];
    const missingDependencies: string[] = [];

    // Validate required phases have completed
    const phaseValidation = this.validateRequiredPhases(rule, sharedState);
    issues.push(...phaseValidation.issues);
    warnings.push(...phaseValidation.warnings);

    // Validate required data is present
    const dataValidation = this.validateRequiredData(rule, previousResult, sharedState);
    issues.push(...dataValidation.issues);
    warnings.push(...dataValidation.warnings);
    missingDependencies.push(...dataValidation.missing);

    // Run custom validation rules
    const customValidation = await this.runCustomValidations(rule, previousResult, sharedState);
    issues.push(...customValidation.issues);
    warnings.push(...customValidation.warnings);

    // Calculate score
    const score = this.calculateValidationScore(issues, warnings);

    const result: DependencyValidationResult = {
      success: issues.length === 0,
      issues,
      warnings,
      missingDependencies,
      score
    };

    this.logger.debug('Dependency validation completed', {
      phase,
      success: result.success,
      issues: issues.length,
      warnings: warnings.length,
      score
    });

    return result;
  }

  /**
   * Initialize dependency rules for all phases
   */
  private initializeDependencyRules(): void {
    // PLAN phase - no dependencies
    this.dependencyRules.set(WorkflowPhase.PLAN, {
      phase: WorkflowPhase.PLAN,
      requiredPhases: [],
      requiredData: [],
      optionalData: [],
      validationRules: [
        {
          name: 'request-validation',
          description: 'Validate request has description and requirements',
          validator: (data) => data?.request?.description && data?.request?.requirements?.length > 0,
          severity: 'critical',
          errorMessage: 'Request must have description and requirements'
        }
      ]
    });

    // BUSINESS_LOGIC phase
    this.dependencyRules.set(WorkflowPhase.BUSINESS_LOGIC, {
      phase: WorkflowPhase.BUSINESS_LOGIC,
      requiredPhases: [WorkflowPhase.PLAN],
      requiredData: ['technicalSpec', 'projectName'],
      optionalData: ['features', 'businessDomain'],
      validationRules: [
        {
          name: 'technical-spec-validation',
          description: 'Technical specification must be complete',
          validator: (data) => data?.technicalSpec?.projectName && data?.technicalSpec?.description,
          severity: 'critical',
          errorMessage: 'Technical specification must have project name and description'
        },
        {
          name: 'business-domain-validation',
          description: 'Business domain should be identified',
          validator: (data) => data?.technicalSpec?.businessDomain,
          severity: 'medium',
          errorMessage: 'Business domain not identified'
        }
      ]
    });

    // GENERATE phase
    this.dependencyRules.set(WorkflowPhase.GENERATE, {
      phase: WorkflowPhase.GENERATE,
      requiredPhases: [WorkflowPhase.PLAN, WorkflowPhase.BUSINESS_LOGIC],
      requiredData: ['domainAPIs', 'databaseSchema', 'applicationFlow'],
      optionalData: ['enhancedComponents', 'businessServices'],
      validationRules: [
        {
          name: 'domain-apis-validation',
          description: 'Domain APIs must be generated',
          validator: (data) => data?.domainAPIs && Object.keys(data.domainAPIs).length > 0,
          severity: 'critical',
          errorMessage: 'No domain APIs generated'
        },
        {
          name: 'database-schema-validation',
          description: 'Database schema must be defined',
          validator: (data) => data?.databaseSchema?.tables?.length > 0,
          severity: 'critical',
          errorMessage: 'No database tables defined'
        },
        {
          name: 'application-flow-validation',
          description: 'Application flow must be defined',
          validator: (data) => data?.applicationFlow?.routes?.length > 0,
          severity: 'critical',
          errorMessage: 'No application routes defined'
        }
      ]
    });





    // DATABASE phase
    this.dependencyRules.set(WorkflowPhase.DATABASE, {
      phase: WorkflowPhase.DATABASE,
      requiredPhases: [WorkflowPhase.GENERATE],
      requiredData: ['files', 'projectPath'],
      optionalData: ['databaseResult', 'schemaFixes'],
      validationRules: [
        {
          name: 'database-files-validation',
          description: 'Database files must be present',
          validator: (data) => {
            const files = data?.files || {};
            return Object.keys(files).some(path => path.includes('init.sql') || path.includes('schema'));
          },
          severity: 'critical',
          errorMessage: 'No database files found'
        }
      ]
    });



    // TESTING phase
    this.dependencyRules.set(WorkflowPhase.TESTING, {
      phase: WorkflowPhase.TESTING,
      requiredPhases: [WorkflowPhase.GENERATE, WorkflowPhase.DATABASE],
      requiredData: ['files', 'projectPath'],
      optionalData: ['testResults', 'coverageReport'],
      validationRules: [
        {
          name: 'package-json-validation',
          description: 'Package.json files must be present for testing',
          validator: (data) => {
            const files = data?.files || {};
            return Object.keys(files).some(path => path.endsWith('package.json'));
          },
          severity: 'critical',
          errorMessage: 'No package.json files found for testing'
        }
      ]
    });



    // DEPLOY phase
    this.dependencyRules.set(WorkflowPhase.DEPLOY, {
      phase: WorkflowPhase.DEPLOY,
      requiredPhases: [WorkflowPhase.TESTING],
      requiredData: ['files', 'projectPath'],
      optionalData: ['deploymentConfig', 'dockerFiles'],
      validationRules: [
        {
          name: 'deployment-files-validation',
          description: 'Deployment configuration files must be present',
          validator: (data) => {
            const files = data?.files || {};
            return Object.keys(files).some(path => 
              path.includes('docker') || path.includes('Dockerfile') || path.includes('docker-compose')
            );
          },
          severity: 'high',
          errorMessage: 'No deployment configuration files found'
        }
      ]
    });
  }

  /**
   * Validate that required phases have completed
   */
  private validateRequiredPhases(
    rule: DependencyRule,
    sharedState?: Map<string, any>
  ): { issues: string[]; warnings: string[] } {
    const issues: string[] = [];
    const warnings: string[] = [];

    if (!sharedState) {
      if (rule.requiredPhases.length > 0) {
        issues.push('No shared state available to validate required phases');
      }
      return { issues, warnings };
    }

    for (const requiredPhase of rule.requiredPhases) {
      const phaseResult = sharedState.get(`${requiredPhase}_result`);
      if (!phaseResult) {
        issues.push(`Required phase ${requiredPhase} has not completed`);
      } else if (!phaseResult.success) {
        issues.push(`Required phase ${requiredPhase} failed`);
      }
    }

    return { issues, warnings };
  }

  /**
   * Validate that required data is present
   */
  private validateRequiredData(
    rule: DependencyRule,
    previousResult?: AgentResult,
    sharedState?: Map<string, any>
  ): { issues: string[]; warnings: string[]; missing: string[] } {
    const issues: string[] = [];
    const warnings: string[] = [];
    const missing: string[] = [];

    const data = previousResult?.data || {};

    for (const requiredKey of rule.requiredData) {
      if (!this.hasDataKey(data, requiredKey) && !this.hasSharedStateKey(sharedState, requiredKey)) {
        issues.push(`Required data missing: ${requiredKey}`);
        missing.push(requiredKey);
      }
    }

    for (const optionalKey of rule.optionalData) {
      if (!this.hasDataKey(data, optionalKey) && !this.hasSharedStateKey(sharedState, optionalKey)) {
        warnings.push(`Optional data missing: ${optionalKey}`);
      }
    }

    return { issues, warnings, missing };
  }

  /**
   * Run custom validation rules
   */
  private async runCustomValidations(
    rule: DependencyRule,
    previousResult?: AgentResult,
    sharedState?: Map<string, any>
  ): Promise<{ issues: string[]; warnings: string[] }> {
    const issues: string[] = [];
    const warnings: string[] = [];

    const validationData = {
      ...previousResult?.data,
      sharedState: sharedState ? Object.fromEntries(sharedState) : {}
    };

    for (const validationRule of rule.validationRules) {
      try {
        const isValid = validationRule.validator(validationData);
        if (!isValid) {
          if (validationRule.severity === 'critical' || validationRule.severity === 'high') {
            issues.push(validationRule.errorMessage);
          } else {
            warnings.push(validationRule.errorMessage);
          }
        }
      } catch (error) {
        warnings.push(`Validation rule '${validationRule.name}' failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return { issues, warnings };
  }

  /**
   * Check if data has a specific key (supports nested keys)
   */
  private hasDataKey(data: any, key: string): boolean {
    if (!data) return false;

    const keys = key.split('.');
    let current = data;

    for (const k of keys) {
      if (current === null || current === undefined || !(k in current)) {
        return false;
      }
      current = current[k];
    }

    return current !== null && current !== undefined;
  }

  /**
   * Check if shared state has a specific key
   */
  private hasSharedStateKey(sharedState: Map<string, any> | undefined, key: string): boolean {
    if (!sharedState) return false;
    return sharedState.has(key) && sharedState.get(key) !== null && sharedState.get(key) !== undefined;
  }

  /**
   * Calculate validation score based on issues and warnings
   */
  private calculateValidationScore(issues: string[], warnings: string[]): number {
    const issueWeight = 10;
    const warningWeight = 2;
    const maxScore = 100;

    const penalty = (issues.length * issueWeight) + (warnings.length * warningWeight);
    return Math.max(0, maxScore - penalty);
  }

  /**
   * Add custom dependency rule
   */
  public addDependencyRule(rule: DependencyRule): void {
    this.dependencyRules.set(rule.phase, rule);
    this.logger.debug('Added custom dependency rule', { phase: rule.phase });
  }

  /**
   * Get dependency rule for a phase
   */
  public getDependencyRule(phase: WorkflowPhase): DependencyRule | undefined {
    return this.dependencyRules.get(phase);
  }

  /**
   * Get all dependency rules
   */
  public getAllDependencyRules(): Map<WorkflowPhase, DependencyRule> {
    return new Map(this.dependencyRules);
  }
}

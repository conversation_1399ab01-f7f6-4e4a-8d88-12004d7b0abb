import { BaseAgent, AgentTask, AgentResult } from '../core/BaseAgent';
import { Logger } from '../core/Logger';
import { WorkflowPhase, WorkflowRequest, WorkflowResult } from './Orchestrator';
import { IntelligentCacheManager } from './IntelligentCacheManager';
import { QualityGatesFramework } from '../core/QualityGatesFramework';
import { PerformanceOptimization } from '../core/PerformanceOptimization';
import {
  StreamingCodeGenerator,
  ContractFirstCoordination,
  SharedStateManager,
  DependencyValidator,
  ProactiveQualitySystem,
  ComprehensiveCrossLayerValidator,
  IntelligentErrorRecovery,
  ParallelLLMManager,
  OptimizedFileManager
} from './stubs';

export interface EnhancedWorkflowMetrics {
  totalDuration: number;
  phaseCompletionRate: number;
  crossLayerConsistencyScore: number;
  compilationSuccessRate: number;
  cacheHitRate: number;
  memoryUsageReduction: number;
  errorRecoverySuccessRate: number;
}

export interface PhaseExecutionContext {
  phase: WorkflowPhase;
  request: WorkflowRequest;
  previousResult?: AgentResult;
  planResult?: AgentResult;
  businessLogicResult?: AgentResult;
  sharedState: Map<string, any>;
  qualityPlan: any;
  contracts: any;
}

export class EnhancedSequentialOrchestrator {
  private agents: Map<string, BaseAgent> = new Map();
  private logger: Logger;
  private cacheManager: IntelligentCacheManager;
  private streamingGenerator: StreamingCodeGenerator;
  private contractCoordination: ContractFirstCoordination;
  private sharedStateManager: SharedStateManager;
  private dependencyValidator: DependencyValidator;
  private proactiveQuality: ProactiveQualitySystem;
  private crossLayerValidator: ComprehensiveCrossLayerValidator;
  private errorRecovery: IntelligentErrorRecovery;
  private llmManager: ParallelLLMManager;
  private fileManager: OptimizedFileManager;
  private qualityGates: QualityGatesFramework;
  private performanceOptimization: PerformanceOptimization;
  private metrics: EnhancedWorkflowMetrics = {
    totalDuration: 0,
    phaseCompletionRate: 0,
    crossLayerConsistencyScore: 0,
    compilationSuccessRate: 0,
    cacheHitRate: 0,
    memoryUsageReduction: 0,
    errorRecoverySuccessRate: 0
  };

  constructor() {
    this.logger = Logger.getInstance();
    this.cacheManager = new IntelligentCacheManager();
    this.streamingGenerator = new StreamingCodeGenerator();
    this.contractCoordination = new ContractFirstCoordination();
    this.sharedStateManager = new SharedStateManager();
    this.dependencyValidator = new DependencyValidator();
    this.proactiveQuality = new ProactiveQualitySystem();
    this.crossLayerValidator = new ComprehensiveCrossLayerValidator();
    this.errorRecovery = new IntelligentErrorRecovery();
    this.llmManager = new ParallelLLMManager();
    this.fileManager = new OptimizedFileManager();
    this.qualityGates = new QualityGatesFramework();
    this.performanceOptimization = new PerformanceOptimization();
    this.initializeMetrics();
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalDuration: 0,
      phaseCompletionRate: 0,
      crossLayerConsistencyScore: 0,
      compilationSuccessRate: 0,
      cacheHitRate: 0,
      memoryUsageReduction: 0,
      errorRecoverySuccessRate: 0
    };
  }

  public registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.getName(), agent);
    this.logger.info(`Enhanced orchestrator registered agent: ${agent.getName()}`);
  }

  /**
   * Execute enhanced sequential workflow with all optimizations
   */
  public async executeEnhancedWorkflow(request: WorkflowRequest): Promise<WorkflowResult> {
    const startTime = Date.now();
    this.logger.info('Starting Enhanced Sequential Workflow execution', {
      sessionId: request.context.sessionId,
      description: request.description
    });

    const result: WorkflowResult = {
      success: false,
      phases: this.initializePhaseResults(),
      metadata: {
        orchestrator: 'EnhancedSequentialOrchestrator',
        startTime,
        optimizations: ['caching', 'streaming', 'proactive-quality', 'error-recovery']
      }
    };

    try {
      // Initialize shared state for the workflow
      await this.sharedStateManager.initializeSession(request.context.sessionId);

      // Execute phases with enhanced coordination
      const phaseResults = await this.executeSequentialPhasesWithOptimizations(request);

      // Update result with phase outcomes
      Object.assign(result.phases, phaseResults.phases);
      result.success = phaseResults.success;
      result.finalOutput = phaseResults.finalOutput;

      // Calculate final metrics
      this.metrics.totalDuration = Date.now() - startTime;
      this.metrics.phaseCompletionRate = this.calculatePhaseCompletionRate(result);
      this.metrics.crossLayerConsistencyScore = await this.calculateConsistencyScore(result);

      result.metadata = {
        ...result.metadata,
        metrics: this.metrics,
        endTime: Date.now(),
        duration: this.metrics.totalDuration
      };

      this.logger.info('Enhanced Sequential Workflow completed', {
        success: result.success,
        duration: this.metrics.totalDuration,
        phaseCompletionRate: this.metrics.phaseCompletionRate,
        consistencyScore: this.metrics.crossLayerConsistencyScore
      });

      return result;

    } catch (error) {
      this.logger.error('Enhanced Sequential Workflow failed', {
        error: error instanceof Error ? error.message : String(error),
        sessionId: request.context.sessionId,
        duration: Date.now() - startTime
      });

      result.metadata = {
        ...result.metadata,
        error: error instanceof Error ? error.message : String(error),
        endTime: Date.now(),
        duration: Date.now() - startTime
      };

      return result;
    } finally {
      // Cleanup session state
      await this.sharedStateManager.cleanupSession(request.context.sessionId);
    }
  }

  private initializePhaseResults(): WorkflowResult['phases'] {
    const phases: any = {};
    Object.values(WorkflowPhase).forEach(phase => {
      phases[phase] = { completed: false };
    });
    return phases as WorkflowResult['phases'];
  }

  /**
   * Execute sequential phases with all optimizations applied
   */
  private async executeSequentialPhasesWithOptimizations(request: WorkflowRequest): Promise<WorkflowResult> {
    const phaseOrder = [
      WorkflowPhase.PLAN,
      WorkflowPhase.BUSINESS_LOGIC,
      WorkflowPhase.GENERATE,
      WorkflowPhase.DATABASE,
      WorkflowPhase.TESTING,
      WorkflowPhase.DEPLOY
    ];

    const result: WorkflowResult = {
      success: true,
      phases: this.initializePhaseResults()
    };

    let previousResult: AgentResult | undefined;
    let planResult: AgentResult | undefined;
    let businessLogicResult: AgentResult | undefined;

    for (const phase of phaseOrder) {
      try {
        this.logger.info(`Executing enhanced phase: ${phase}`);

        // Create execution context
        const context: PhaseExecutionContext = {
          phase,
          request,
          previousResult,
          planResult,
          businessLogicResult,
          sharedState: this.sharedStateManager.getSessionState(request.context.sessionId),
          qualityPlan: await this.proactiveQuality.generateQualityPlan(phase, request),
          contracts: await this.contractCoordination.getContracts(request.context.sessionId)
        };

        // Pre-phase validation for critical phases
        const validationResult = await this.validatePhasePrerequisites(phase, context);
        if (!validationResult.isValid) {
          throw new Error(`Phase ${phase} prerequisites not met: ${validationResult.errors.join(', ')}`);
        }

        // Execute phase with all optimizations
        const phaseResult = await this.executePhaseWithOptimizations(context);

        // Update phase tracking
        result.phases[phase] = { completed: phaseResult.success, result: phaseResult };

        if (!phaseResult.success) {
          // Attempt intelligent error recovery
          const recoveryResult = await this.errorRecovery.handlePhaseError(
            phase,
            new Error(phaseResult.error || 'Phase failed'),
            context
          );

          if (recoveryResult.success && recoveryResult.result) {
            result.phases[phase] = { completed: true, result: recoveryResult.result };
            phaseResult.success = true;
            phaseResult.data = recoveryResult.result.data;
          } else {
            // Mark phase as failed but continue with remaining phases
            result.phases[phase] = {
              completed: false,
              error: phaseResult.error || 'Phase failed',
              attempted: true
            };

            // Critical phases that must succeed for workflow to continue
            const criticalPhases = [
              WorkflowPhase.PLAN,
              WorkflowPhase.BUSINESS_LOGIC,
              WorkflowPhase.GENERATE,
              WorkflowPhase.DATABASE  // DatabaseAgent is now critical for data consistency
            ];

            if (criticalPhases.includes(phase)) {
              this.logger.error(`Critical phase ${phase} failed, stopping workflow`, {
                error: phaseResult.error,
                sessionId: request.context.sessionId
              });
              result.success = false;
              break;
            }

            // For other phases, log the error and continue
            this.logger.warn(`Phase ${phase} failed, continuing with remaining phases`, {
              error: phaseResult.error,
              sessionId: request.context.sessionId
            });
          }
        }

        // Update state for next phase
        previousResult = phaseResult;
        if (phase === WorkflowPhase.PLAN) planResult = phaseResult;
        if (phase === WorkflowPhase.BUSINESS_LOGIC) businessLogicResult = phaseResult;

        // Preserve project path in shared state for all subsequent phases
        if (phaseResult.data && phaseResult.data.projectPath) {
          await this.sharedStateManager.updateSharedState(
            'projectPath',
            phaseResult.data.projectPath,
            request.context.sessionId
          );
          this.logger.info(`Project path stored in shared state from ${phase} phase`, {
            projectPath: phaseResult.data.projectPath,
            sessionId: request.context.sessionId
          });
        }

        // Update shared state
        await this.sharedStateManager.updateSharedState(
          `${phase}_result`,
          phaseResult.data,
          request.context.sessionId
        );

      } catch (error) {
        this.logger.error(`Phase ${phase} failed with error`, {
          error: error instanceof Error ? error.message : String(error),
          phase,
          sessionId: request.context.sessionId
        });

        result.phases[phase] = {
          completed: false,
          error: error instanceof Error ? error.message : String(error),
          attempted: true
        };

        // Only stop for critical phases (planning, business_logic)
        if (phase === WorkflowPhase.PLAN || phase === WorkflowPhase.BUSINESS_LOGIC) {
          result.success = false;
          break;
        }

        // For other phases, log the error and continue
        this.logger.warn(`Phase ${phase} failed with exception, continuing with remaining phases`, {
          error: error instanceof Error ? error.message : String(error),
          sessionId: request.context.sessionId
        });
      }
    }

    result.finalOutput = previousResult?.data;
    return result;
  }

  private calculatePhaseCompletionRate(result: WorkflowResult): number {
    const totalPhases = Object.keys(result.phases).length;
    const completedPhases = Object.values(result.phases).filter(p => p.completed).length;
    return Math.round((completedPhases / totalPhases) * 100);
  }

  private async calculateConsistencyScore(result: WorkflowResult): Promise<number> {
    if (!result.finalOutput) return 0;

    try {
      const validationResult = await this.crossLayerValidator.validateFullStackIntegration({
        generatedCode: result.finalOutput,
        sessionId: result.metadata?.sessionId || 'unknown'
      });

      return validationResult.score || 0;
    } catch (error) {
      this.logger.warn('Failed to calculate consistency score', {
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * Execute individual phase with all optimizations
   */
  private async executePhaseWithOptimizations(context: PhaseExecutionContext): Promise<AgentResult> {
    const { phase, request } = context;

    // Step 1: Check cache for similar executions
    const cacheResult = await this.cacheManager.getCachedResult(phase, request, context.previousResult);
    if (cacheResult) {
      this.metrics.cacheHitRate++;
      this.logger.info(`Cache hit for phase ${phase}`);
      return cacheResult;
    }

    // Step 2: Validate dependencies before execution
    const dependencyValidation = await this.dependencyValidator.validateDependencies(
      phase,
      context.previousResult,
      context.sharedState
    );

    if (!dependencyValidation.success) {
      throw new Error(`Dependency validation failed for ${phase}: ${dependencyValidation.issues.join(', ')}`);
    }

    // Step 2.5: Apply performance optimizations to request data
    const optimizedRequest = this.performanceOptimization.optimizeAgentCommunication(
      request,
      phase
    );

    // Step 3: Find and prepare agent
    const agent = this.findAgentForPhase(phase);
    if (!agent) {
      throw new Error(`No agent found for phase: ${phase}`);
    }

    // Step 4: Create optimized task with data flow adaptation
    const adaptedParameters = this.adaptDataFlow(phase, {
      request,
      previousResult: context.previousResult,
      planResult: context.planResult,
      businessLogicResult: context.businessLogicResult,
      qualityPlan: context.qualityPlan,
      contracts: context.contracts,
      sharedState: context.sharedState,
      streamingGenerator: this.streamingGenerator,
      llmManager: this.llmManager,
      fileManager: this.fileManager
    });

    const task: AgentTask = {
      id: `${phase}-${Date.now()}`,
      type: phase,
      description: `Enhanced ${phase} execution with optimizations`,
      parameters: adaptedParameters,
      context: request.context
    };

    // Step 5: Execute with proactive quality monitoring and memory management
    const startTime = Date.now();
    const initialMemory = process.memoryUsage().heapUsed;

    const result = await agent.execute(task);

    const executionTime = Date.now() - startTime;
    const finalMemory = process.memoryUsage().heapUsed;
    const memoryUsed = finalMemory - initialMemory;

    // Force garbage collection if memory usage is high
    if (memoryUsed > 100 * 1024 * 1024) { // 100MB threshold
      if (global.gc) {
        global.gc();
        this.logger.debug('Forced garbage collection after high memory usage', {
          phase,
          memoryUsed: Math.round(memoryUsed / 1024 / 1024) + 'MB'
        });
      }
    }

    // Step 6: Validate result quality with Phase 3 Quality Gates
    const qualityGateValidation = this.qualityGates.validatePhase(phase, result.data);
    if (!qualityGateValidation.passed) {
      this.logger.warn(`Quality gate validation failed for ${phase}`, {
        score: qualityGateValidation.overallScore,
        criticalIssues: qualityGateValidation.criticalIssues.length,
        warnings: qualityGateValidation.warnings.length
      });

      // Add quality gate results to metadata
      result.metadata = {
        ...result.metadata,
        qualityGateScore: qualityGateValidation.overallScore,
        qualityGateIssues: qualityGateValidation.criticalIssues,
        qualityGateWarnings: qualityGateValidation.warnings
      };
    }

    // Step 6.5: Legacy quality validation
    const qualityValidation = await this.proactiveQuality.validateResult(phase, result, context.qualityPlan);
    if (!qualityValidation.success) {
      this.logger.warn(`Legacy quality validation failed for ${phase}`, qualityValidation.issues);
      // Apply quality corrections if possible
      const correctedResult = await this.proactiveQuality.applyQualityCorrections(result, qualityValidation);
      if (correctedResult.success) {
        result.data = correctedResult.data;
        result.metadata = { ...result.metadata, qualityCorrected: true };
      }
    }

    // Step 7: Cache successful result
    if (result.success) {
      await this.cacheManager.cacheResult(phase, request, context.previousResult, result);
    }

    // Step 8: Update metrics
    result.metadata = {
      ...result.metadata,
      executionTime,
      optimizations: {
        cached: false,
        qualityValidated: true,
        dependencyValidated: true
      }
    };

    return result;
  }

  private findAgentForPhase(phase: WorkflowPhase): BaseAgent | undefined {
    // Map phases to agent names
    const phaseAgentMap: Record<WorkflowPhase, string> = {
      [WorkflowPhase.PLAN]: 'PlanningAgent',
      [WorkflowPhase.BUSINESS_LOGIC]: 'BusinessLogicAgent',
      [WorkflowPhase.GENERATE]: 'CodingAgent',
      [WorkflowPhase.DATABASE]: 'DatabaseAgent',
      [WorkflowPhase.TESTING]: 'TestingAgent',
      [WorkflowPhase.DEPLOY]: 'DeploymentAgent'
    };

    const agentName = phaseAgentMap[phase];
    return agentName ? this.agents.get(agentName) : undefined;
  }

  /**
   * Get current workflow metrics
   */
  public getMetrics(): EnhancedWorkflowMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics for new workflow execution
   */
  public resetMetrics(): void {
    this.initializeMetrics();
  }

  /**
   * Adapt data flow between phases to ensure compatibility
   */
  private adaptDataFlow(phase: WorkflowPhase, parameters: any): any {
    const adapted = { ...parameters };

    // Always preserve requirements from the original request
    if (parameters.request?.requirements && !adapted.requirements) {
      adapted.requirements = parameters.request.requirements;
    }

    // Adapt data flow based on phase requirements
    switch (phase) {
      case WorkflowPhase.BUSINESS_LOGIC:
        // BusinessLogicAgent expects technicalSpec from PlanningAgent
        if (adapted.planResult?.data && !adapted.technicalSpec) {
          adapted.technicalSpec = adapted.planResult.data;
        }
        // Ensure requirements are available
        if (!adapted.requirements && parameters.request?.requirements) {
          adapted.requirements = parameters.request.requirements;
        }
        break;

      case WorkflowPhase.GENERATE:
        // CodingAgent expects technicalSpec, businessLogic, and semanticAnalysis from previous phases
        if (adapted.planResult?.data && !adapted.technicalSpec) {
          adapted.technicalSpec = adapted.planResult.data;
        }
        if (adapted.businessLogicResult?.data && !adapted.businessLogic) {
          adapted.businessLogic = adapted.businessLogicResult.data;
        }
        // Extract semantic analysis from BusinessLogicAgent metadata
        if (adapted.businessLogicResult?.metadata?.domainAnalysis && !adapted.semanticAnalysis) {
          adapted.semanticAnalysis = adapted.businessLogicResult.metadata.domainAnalysis;
        }
        // Ensure requirements are available
        if (!adapted.requirements && parameters.request?.requirements) {
          adapted.requirements = parameters.request.requirements;
        }
        // Also ensure previousResult has the right structure
        if (adapted.previousResult && adapted.businessLogicResult?.data) {
          adapted.previousResult = {
            ...adapted.previousResult,
            technicalSpec: adapted.planResult?.data,
            businessLogic: adapted.businessLogicResult.data,
            semanticAnalysis: adapted.businessLogicResult.metadata?.domainAnalysis
          };
        }
        break;

      case WorkflowPhase.DATABASE:
      case WorkflowPhase.TESTING:
      case WorkflowPhase.DEPLOY:
        // These phases expect GeneratedCode from CodingAgent
        // First, try to get the GeneratedCode from the generate phase result
        const generateResult = parameters.sharedState?.get('generate_result');
        if (generateResult && generateResult.projectPath) {
          // Use the generate phase result as the GeneratedCode object
          adapted.previousResult = generateResult;
          adapted.projectPath = generateResult.projectPath;
          adapted.files = generateResult.files;
          adapted.packageJsons = generateResult.packageJsons;
          adapted.scripts = generateResult.scripts;
          adapted.documentation = generateResult.documentation;
          this.logger.info(`Using generate phase result for ${phase}`, {
            projectPath: generateResult.projectPath,
            fileCount: generateResult.files ? Object.keys(generateResult.files).length : 0
          });
        } else if (adapted.previousResult?.data) {
          // Fallback: Store the original data
          const generatedCodeData = adapted.previousResult.data;

          // These agents expect the previousResult to be the GeneratedCode object directly
          adapted.previousResult = generatedCodeData;

          // Also provide individual properties for compatibility
          adapted.files = generatedCodeData.files;
          adapted.projectPath = generatedCodeData.projectPath;
          adapted.packageJsons = generatedCodeData.packageJsons;
          adapted.scripts = generatedCodeData.scripts;
          adapted.documentation = generatedCodeData.documentation;
        } else if (adapted.previousResult) {
          // Fallback: if previousResult doesn't have data property, use it directly
          adapted.files = adapted.previousResult.files;
          adapted.projectPath = adapted.previousResult.projectPath;
          adapted.packageJsons = adapted.previousResult.packageJsons;
          adapted.scripts = adapted.previousResult.scripts;
          adapted.documentation = adapted.previousResult.documentation;
        }

        // Always ensure project path is available from shared state
        if (!adapted.projectPath) {
          const sharedProjectPath = parameters.sharedState?.get('projectPath');
          if (sharedProjectPath) {
            adapted.projectPath = sharedProjectPath;
            this.logger.info(`Using project path from shared state for ${phase}: ${sharedProjectPath}`);
          } else {
            this.logger.warn(`No project path found in shared state for ${phase}`, {
              sharedStateKeys: parameters.sharedState ? Array.from(parameters.sharedState.keys()) : [],
              hasSharedState: !!parameters.sharedState
            });
          }
        } else {
          this.logger.info(`Project path already available for ${phase}: ${adapted.projectPath}`);
        }

        // Ensure requirements are available for DevOps and Deploy phases
        if (!adapted.requirements && parameters.request?.requirements) {
          adapted.requirements = parameters.request.requirements;
        }

        // Ensure description is available for DevOps phase
        if (!adapted.description && parameters.request?.description) {
          adapted.description = parameters.request.description;
        }

        // Ensure technical spec is available from plan phase
        if (!adapted.technicalSpec && adapted.planResult?.data) {
          adapted.technicalSpec = adapted.planResult.data;
        }

        // Ensure business logic is available from business logic phase
        if (!adapted.businessLogic && adapted.businessLogicResult?.data) {
          adapted.businessLogic = adapted.businessLogicResult.data;
        }
        break;
    }

    return adapted;
  }

  /**
   * Validate prerequisites for critical phases to ensure they have required data
   */
  private async validatePhasePrerequisites(
    phase: WorkflowPhase,
    context: PhaseExecutionContext
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    switch (phase) {


      case WorkflowPhase.TESTING:
        // TestingAgent requires project path and generated code
        const generateResult = context.sharedState?.get('generate_result');
        if (!generateResult?.projectPath) {
          errors.push('TestingAgent requires project path from generate phase');
        }
        break;



      case WorkflowPhase.DEPLOY:
        // DeploymentAgent requires project path
        const deployGenerateResult = context.sharedState?.get('generate_result');
        if (!deployGenerateResult?.projectPath) {
          errors.push('DeploymentAgent requires project path from generate phase');
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}


import { BaseAgent, AgentTask, AgentResult, AgentContext } from '../core/BaseAgent';
import { Logger } from '../core/Logger';
import { CompilationQualityGate } from '../quality/CompilationQualityGate';
import { FeedbackManager, AgentFeedback } from '../coordination/FeedbackManager';
import { CoordinatedFixingStrategy } from '../coordination/CoordinatedFixingStrategy';
import { BusinessLogicIntegrationValidator } from '../coordination/BusinessLogicIntegrationValidator';
import { EnhancedSequentialOrchestrator } from './EnhancedSequentialOrchestrator';
import { PerformanceManager } from '../infrastructure/PerformanceManager';
import { ParallelProcessingManager, ParallelTask } from '../infrastructure/ParallelProcessingManager';
import { AdvancedQualityGateFramework, QualityGateResult } from '../quality/AdvancedQualityGateFramework';
import { SelfHealingManager } from '../intelligence/SelfHealingManager';
import { ErrorRecoveryWorkflow } from '../intelligence/ErrorRecoveryWorkflow';

export enum WorkflowPhase {
  PLAN = 'plan',
  BUSINESS_LOGIC = 'business_logic',
  GENERATE = 'generate',
  DATABASE = 'database',
  TESTING = 'testing',
  DEPLOY = 'deploy',
}

export interface WorkflowRequest {
  description: string;
  requirements: string[];
  context: AgentContext;
}

export interface WorkflowResult {
  success: boolean;
  phases: {
    [key in WorkflowPhase]: {
      completed: boolean;
      result?: AgentResult;
      error?: string;
      attempted?: boolean;
    };
  };
  finalOutput?: any;
  metadata?: Record<string, any>;
}

export interface QualityGate {
  phase: WorkflowPhase;
  validate(result: AgentResult): Promise<boolean>;
  getErrorMessage(): string;
}

export interface ValidationResult {
  success: boolean;
  issues: string[];
  warnings: string[];
  score: number;
  metadata?: Record<string, any>;
}

export interface EnhancedTaskParameters {
  requirements: string[];
  previousResult?: any;
  businessLogic?: any;
  technicalSpec?: any;
  sessionId: string;
  allPhaseResults: Map<WorkflowPhase, AgentResult>;
  crossPhaseContext?: Record<string, any>;
  validationResults?: ValidationResult[];
}

export class Orchestrator {
  private static instance: Orchestrator;
  private agents: Map<string, BaseAgent>;
  private qualityGates: Map<WorkflowPhase, QualityGate[]>;
  private logger: Logger;
  private feedbackManager: FeedbackManager;
  private coordinatedFixingStrategy: CoordinatedFixingStrategy;
  private businessLogicValidator: BusinessLogicIntegrationValidator;
  private enhancedOrchestrator: EnhancedSequentialOrchestrator;
  private useEnhancedMode: boolean = true;
  private sessionPhaseResults: Map<string, Map<WorkflowPhase, AgentResult>> = new Map();
  private crossPhaseValidators: Map<string, (data: any) => Promise<ValidationResult>> = new Map();
  private performanceManager: PerformanceManager;
  private parallelProcessingManager: ParallelProcessingManager;
  private enableParallelProcessing: boolean = true;
  private advancedQualityGates: AdvancedQualityGateFramework;
  private enableAdvancedQualityGates: boolean = true;
  private selfHealingManager: SelfHealingManager;
  private errorRecoveryWorkflow: ErrorRecoveryWorkflow;
  private enableZeroManualIntervention: boolean = true;
  private autoFixSuccessRateTarget: number = 90; // Target >90% success rate

  private constructor() {
    this.agents = new Map();
    this.qualityGates = new Map();
    this.logger = Logger.getInstance();
    this.feedbackManager = new FeedbackManager();
    this.coordinatedFixingStrategy = new CoordinatedFixingStrategy(this.feedbackManager);
    this.businessLogicValidator = new BusinessLogicIntegrationValidator();
    this.enhancedOrchestrator = new EnhancedSequentialOrchestrator();
    this.performanceManager = PerformanceManager.getInstance();
    this.parallelProcessingManager = ParallelProcessingManager.getInstance();
    this.advancedQualityGates = AdvancedQualityGateFramework.getInstance();
    this.selfHealingManager = SelfHealingManager.getInstance();
    this.errorRecoveryWorkflow = ErrorRecoveryWorkflow.getInstance();
    this.initializeQualityGates();
  }

  public static getInstance(): Orchestrator {
    if (!Orchestrator.instance) {
      Orchestrator.instance = new Orchestrator();
    }
    return Orchestrator.instance;
  }

  private initializeQualityGates(): void {
    // Initialize comprehensive quality gates for each phase
    this.qualityGates.set(WorkflowPhase.PLAN, [
      new PlanValidationGate()
    ]);
    this.qualityGates.set(WorkflowPhase.BUSINESS_LOGIC, [
      new BusinessLogicValidationGate()
    ]);
    this.qualityGates.set(WorkflowPhase.GENERATE, [
      new CodeGenerationValidationGate(),
      new CompilationQualityGate()
    ]);

    this.qualityGates.set(WorkflowPhase.DATABASE, [
      new DatabaseValidationGate()
    ]);
    this.qualityGates.set(WorkflowPhase.TESTING, [
      new TestingValidationGate()
    ]);
    this.qualityGates.set(WorkflowPhase.DEPLOY, []);
  }

  public registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.getName(), agent);
    this.logger.info(`Agent registered: ${agent.getName()}`, {
      description: agent.getDescription(),
    });
  }

  public getRegisteredAgents(): string[] {
    return Array.from(this.agents.keys());
  }

  public addQualityGate(gate: QualityGate): void {
    const gates = this.qualityGates.get(gate.phase) || [];
    gates.push(gate);
    this.qualityGates.set(gate.phase, gates);
    this.logger.info(`Quality gate added for phase: ${gate.phase}`);
  }

  public async executeWorkflow(request: WorkflowRequest): Promise<WorkflowResult> {
    this.logger.info('Starting workflow execution', {
      description: request.description,
      requirements: request.requirements,
      sessionId: request.context.sessionId,
      enhancedMode: this.useEnhancedMode
    });

    // Register agents with enhanced orchestrator
    if (this.useEnhancedMode) {
      for (const [name, agent] of this.agents.entries()) {
        this.enhancedOrchestrator.registerAgent(agent);
      }

      // Execute with enhanced orchestrator
      try {
        const enhancedResult = await this.enhancedOrchestrator.executeEnhancedWorkflow(request);

        this.logger.info('Enhanced workflow execution completed', {
          success: enhancedResult.success,
          sessionId: request.context.sessionId,
          metrics: enhancedResult.metadata?.metrics
        });

        return enhancedResult;

      } catch (error) {
        this.logger.error('Enhanced workflow execution failed, falling back to legacy mode', {
          error: error instanceof Error ? error.message : String(error),
          sessionId: request.context.sessionId
        });

        // Fall back to legacy workflow execution
        this.useEnhancedMode = false;
      }
    }

    // Legacy workflow execution
    const result: WorkflowResult = {
      success: false,
      phases: {
        [WorkflowPhase.PLAN]: { completed: false },
        [WorkflowPhase.BUSINESS_LOGIC]: { completed: false },
        [WorkflowPhase.GENERATE]: { completed: false },
        [WorkflowPhase.DATABASE]: { completed: false },
        [WorkflowPhase.TESTING]: { completed: false },
        [WorkflowPhase.DEPLOY]: { completed: false },
      },
    };

    this.logger.info('Starting legacy workflow execution with feedback loops', {
      description: request.description,
      requirements: request.requirements,
      sessionId: request.context.sessionId,
    });

    // Clear any previous feedback for this session
    this.feedbackManager.clearFeedbackHistory(request.context.sessionId);

    try {
      // Execute workflow with enhanced coordination and feedback loops
      const workflowResult = await this.executeEnhancedWorkflow(request, result);
      return workflowResult;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Workflow execution failed', {
        error: errorMessage,
        sessionId: request.context.sessionId,
      });

      // Mark the current phase as failed
      for (const phase of Object.values(WorkflowPhase)) {
        if (!result.phases[phase].completed) {
          result.phases[phase].error = errorMessage;
          break;
        }
      }
    }

    return result;
  }

  /**
   * Enhanced workflow execution with feedback loops and retry logic
   */
  private async executeEnhancedWorkflow(request: WorkflowRequest, result: WorkflowResult): Promise<WorkflowResult> {
    let businessLogicResult: AgentResult | undefined;
    let generateResult: AgentResult | undefined;

    try {
      // Phase 1: Plan
      const planResult = await this.executePhase(WorkflowPhase.PLAN, request);
      result.phases[WorkflowPhase.PLAN] = { completed: true, result: planResult };

      if (!planResult.success) {
        throw new Error(`Plan phase failed: ${planResult.error}`);
      }

      // Phase 2: Business Logic
      businessLogicResult = await this.executePhase(WorkflowPhase.BUSINESS_LOGIC, request, planResult);
      result.phases[WorkflowPhase.BUSINESS_LOGIC] = { completed: true, result: businessLogicResult };

      if (!businessLogicResult.success) {
        throw new Error(`Business logic phase failed: ${businessLogicResult.error}`);
      }

      // Phase 3: Generate (enhanced with business logic integration validation)
      generateResult = await this.executePhaseWithIntegrationValidation(
        WorkflowPhase.GENERATE, request, businessLogicResult, planResult
      );
      result.phases[WorkflowPhase.GENERATE] = { completed: true, result: generateResult };

      if (!generateResult.success) {
        throw new Error(`Generate phase failed: ${generateResult.error}`);
      }

      // Continue with remaining phases using coordinated execution
      const remainingPhases = [
        WorkflowPhase.DATABASE,
        WorkflowPhase.TESTING,
        WorkflowPhase.DEPLOY
      ];

      let previousResult = generateResult;
      for (const phase of remainingPhases) {
        const phaseResult = await this.executePhaseWithCoordination(
          phase, request, previousResult, planResult, businessLogicResult
        );
        result.phases[phase] = { completed: true, result: phaseResult };

        if (!phaseResult.success) {
          // Check if we should retry based on feedback
          const feedback = this.feedbackManager.analyzeFeedback(request.context.sessionId);
          if (feedback.shouldRetry && feedback.retryPhase) {
            this.logger.info(`Retrying from phase ${feedback.retryPhase} based on feedback analysis`);
            this.feedbackManager.incrementRetryAttempts(request.context.sessionId);

            // Retry from the recommended phase
            return await this.retryFromPhase(feedback.retryPhase, request, result, planResult, businessLogicResult);
          }

          throw new Error(`${phase} phase failed: ${phaseResult.error}`);
        }

        previousResult = phaseResult;
      }

      result.success = true;
      result.finalOutput = previousResult.data;

      this.logger.info('Enhanced workflow completed successfully', {
        sessionId: request.context.sessionId,
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Enhanced workflow execution failed', {
        error: errorMessage,
        sessionId: request.context.sessionId,
      });

      // Generate feedback for failed workflow
      if (generateResult && businessLogicResult) {
        const feedback: AgentFeedback = {
          sourcePhase: WorkflowPhase.TESTING,
          targetPhase: WorkflowPhase.GENERATE,
          issues: [errorMessage],
          suggestedFixes: ['Apply enhanced coordination fixes'],
          severity: 'critical',
          timestamp: new Date(),
          sessionId: request.context.sessionId
        };
        await this.feedbackManager.provideFeedback(feedback);
      }

      // Mark the current phase as failed
      for (const phase of Object.values(WorkflowPhase)) {
        if (!result.phases[phase].completed) {
          result.phases[phase].error = errorMessage;
          break;
        }
      }

      return result;
    }
  }

  private async executePhase(
    phase: WorkflowPhase,
    request: WorkflowRequest,
    previousResult?: AgentResult,
    planResult?: AgentResult
  ): Promise<AgentResult> {
    // Start performance monitoring
    const performanceId = this.performanceManager.startPhaseMonitoring(request.context.sessionId, phase);

    // Pre-phase quality gate
    const prePhaseValidation = await this.validatePrePhaseConditions(phase, previousResult);
    if (!prePhaseValidation.success) {
      this.logger.warn(`Pre-phase validation failed for ${phase}:`, prePhaseValidation.issues);
      // Continue with phase but log the issues
    }

    this.logger.info(`Executing phase: ${phase}`, {
      sessionId: request.context.sessionId,
      performanceId
    });

    // Get business logic result from stored results
    const allResults = this.getPhaseResults(request.context.sessionId);
    const businessLogicResult = allResults.get(WorkflowPhase.BUSINESS_LOGIC);

    // Create enhanced task with comprehensive data flow
    const task: AgentTask = {
      id: `${request.context.sessionId}-${phase}`,
      type: phase,
      description: request.description,
      parameters: await this.buildEnhancedParameters(phase, {
        requirements: request.requirements,
        previousResult: previousResult?.data,
        businessLogic: businessLogicResult?.data,
        technicalSpec: planResult?.data,
        sessionId: request.context.sessionId,
        allPhaseResults: allResults
      }),
      context: request.context,
    };

    // Find appropriate agent for this phase
    const agent = this.findAgentForTask(task);
    if (!agent) {
      throw new Error(`No agent found for phase: ${phase}`);
    }

    // Execute the task with zero manual intervention capabilities
    let result = await agent.execute(task);

    // Apply zero manual intervention if enabled and task failed
    if (this.enableZeroManualIntervention && !result.success) {
      this.logger.info('Task failed, attempting zero manual intervention', {
        sessionId: request.context.sessionId,
        phase,
        originalError: result.metadata?.error
      });

      // Attempt self-healing first
      const healingResult = await this.selfHealingManager.attemptSelfHealing(
        result,
        phase,
        { sessionId: request.context.sessionId, task }
      );

      if (healingResult.success && healingResult.errorResolved) {
        this.logger.info('Self-healing successful', {
          sessionId: request.context.sessionId,
          phase,
          appliedFixes: healingResult.appliedFixes
        });

        // Update result with healing information
        result = {
          ...result,
          success: true,
          metadata: {
            ...result.metadata,
            selfHealed: true,
            healingResult,
            originalError: result.metadata?.error
          }
        };
      } else {
        // If self-healing failed, try comprehensive error recovery
        this.logger.info('Self-healing failed, attempting error recovery workflow', {
          sessionId: request.context.sessionId,
          phase
        });

        const recoveryResult = await this.errorRecoveryWorkflow.executeRecoveryWorkflow(
          request.context.sessionId,
          phase,
          task,
          result
        );

        if (recoveryResult.success && recoveryResult.recoveredResult) {
          this.logger.info('Error recovery successful', {
            sessionId: request.context.sessionId,
            phase,
            appliedSteps: recoveryResult.appliedSteps,
            confidence: recoveryResult.confidence
          });

          result = {
            ...recoveryResult.recoveredResult,
            metadata: {
              ...recoveryResult.recoveredResult.metadata,
              errorRecovered: true,
              recoveryResult,
              originalError: result.metadata?.error
            }
          };
        } else {
          // Even if recovery failed, enhance the result with recovery information
          result.metadata = {
            ...result.metadata,
            recoveryAttempted: true,
            recoveryResult,
            requiresManualIntervention: recoveryResult.requiresManualIntervention
          };

          this.logger.warn('Zero manual intervention failed', {
            sessionId: request.context.sessionId,
            phase,
            requiresManualIntervention: recoveryResult.requiresManualIntervention
          });
        }
      }
    }

    // Store the result for future phases
    this.storePhaseResult(request.context.sessionId, phase, result);

    // Run quality gates
    await this.runQualityGates(phase, result);

    // Advanced quality gate evaluation
    let qualityGateResult: QualityGateResult | null = null;
    if (this.enableAdvancedQualityGates) {
      qualityGateResult = await this.advancedQualityGates.evaluateQualityGates(
        phase,
        result,
        request.context.sessionId
      );

      // Handle rollback if required
      if (qualityGateResult.rollbackRequired) {
        this.logger.warn(`Quality gate failure requires rollback for ${phase}`, {
          sessionId: request.context.sessionId,
          overallScore: qualityGateResult.overallScore,
          criticalFailures: qualityGateResult.critical
        });

        // Attempt rollback to previous stable point
        const rollbackPoint = await this.advancedQualityGates.executeRollback(
          request.context.sessionId,
          this.getPreviousPhase(phase)
        );

        if (rollbackPoint) {
          this.logger.info(`Rolled back to ${rollbackPoint.phase}`, {
            sessionId: request.context.sessionId
          });

          // Return the rollback result with quality gate information
          return {
            ...rollbackPoint.result,
            metadata: {
              ...rollbackPoint.result.metadata,
              rolledBack: true,
              rollbackReason: 'Quality gate failure',
              qualityGateResult,
              originalPhase: phase,
              rollbackPhase: rollbackPoint.phase
            }
          };
        }
      }

      // Enhance result with quality gate information
      result.metadata = {
        ...result.metadata,
        qualityGateResult,
        productionReadiness: qualityGateResult.productionReadiness,
        qualityScore: qualityGateResult.overallScore
      };
    }

    // Post-phase quality validation (legacy)
    const postPhaseValidation = await this.validatePostPhaseQuality(phase, result);
    if (!postPhaseValidation.success) {
      this.logger.warn(`Post-phase validation issues for ${phase}:`, postPhaseValidation.issues);
      // Enhance result with validation feedback
      result.metadata = {
        ...result.metadata,
        qualityIssues: postPhaseValidation.issues,
        legacyQualityScore: postPhaseValidation.score
      };
    }

    // End performance monitoring
    const performanceMetrics = this.performanceManager.endPhaseMonitoring(
      request.context.sessionId,
      phase,
      result.success,
      result.metadata?.errorCount || 0
    );

    // Add performance metrics to result
    if (performanceMetrics) {
      result.metadata = {
        ...result.metadata,
        performanceMetrics: {
          duration: performanceMetrics.duration,
          memoryUsage: performanceMetrics.memoryUsage,
          cacheHits: performanceMetrics.cacheHits,
          cacheMisses: performanceMetrics.cacheMisses
        }
      };
    }

    return result;
  }

  private findAgentForTask(task: AgentTask): BaseAgent | undefined {
    for (const agent of this.agents.values()) {
      if (agent.canHandle(task)) {
        return agent;
      }
    }
    return undefined;
  }

  private async runQualityGates(phase: WorkflowPhase, result: AgentResult): Promise<void> {
    const gates = this.qualityGates.get(phase) || [];

    for (const gate of gates) {
      const isValid = await gate.validate(result);
      if (!isValid) {
        throw new Error(`Quality gate failed for ${phase}: ${gate.getErrorMessage()}`);
      }
    }

    this.logger.debug(`Quality gates passed for phase: ${phase}`, {
      gateCount: gates.length,
    });
  }

  /**
   * Execute phase with business logic integration validation
   */
  private async executePhaseWithIntegrationValidation(
    phase: WorkflowPhase,
    request: WorkflowRequest,
    businessLogicResult: AgentResult,
    planResult: AgentResult
  ): Promise<AgentResult> {
    const result = await this.executePhase(phase, request, businessLogicResult, planResult);

    if (result.success && phase === WorkflowPhase.GENERATE) {
      // Validate business logic integration
      const validationContext = {
        businessLogic: businessLogicResult.data,
        generatedCode: result.data,
        projectPath: result.data.projectPath || '/tmp/generated-project',
        sessionId: request.context.sessionId
      };

      const integrationResult = await this.businessLogicValidator.validateIntegration(validationContext);

      if (!integrationResult.isValid) {
        this.logger.warn('Business logic integration validation failed', {
          score: integrationResult.score,
          issues: integrationResult.issues.length,
          sessionId: request.context.sessionId
        });

        // Provide feedback for integration issues
        const feedback: AgentFeedback = {
          sourcePhase: WorkflowPhase.GENERATE,
          targetPhase: WorkflowPhase.BUSINESS_LOGIC,
          issues: integrationResult.issues,
          suggestedFixes: integrationResult.recommendations,
          severity: integrationResult.score < 50 ? 'critical' : 'high',
          timestamp: new Date(),
          sessionId: request.context.sessionId,
          metadata: { integrationScore: integrationResult.score }
        };

        await this.feedbackManager.provideFeedback(feedback);
      }
    }

    return result;
  }

  /**
   * Execute phase with coordinated fixing strategy
   */
  private async executePhaseWithCoordination(
    phase: WorkflowPhase,
    request: WorkflowRequest,
    previousResult: AgentResult,
    planResult?: AgentResult,
    businessLogicResult?: AgentResult
  ): Promise<AgentResult> {
    let result = await this.executePhase(phase, request, previousResult, planResult);

    // Apply coordinated fixes for Testing phase
    if (phase === WorkflowPhase.TESTING && !result.success) {
      this.logger.info('Applying coordinated fixing strategy for Testing phase');

      const fixingContext = {
        projectPath: previousResult.data?.projectPath || '/tmp/generated-project',
        sessionId: request.context.sessionId,
        phase,
        previousFixes: [],
        targetIssues: [result.error || 'Testing phase failed']
      };

      const fixingResult = await this.coordinatedFixingStrategy.applyCoordinatedFixes(fixingContext);

      if (fixingResult.success) {
        this.logger.info('Coordinated fixes applied successfully, retrying Testing phase');
        result = await this.executePhase(phase, request, previousResult, planResult);
      }
    }

    return result;
  }

  /**
   * Retry workflow from a specific phase
   */
  private async retryFromPhase(
    retryPhase: WorkflowPhase,
    request: WorkflowRequest,
    result: WorkflowResult,
    planResult: AgentResult,
    businessLogicResult?: AgentResult
  ): Promise<WorkflowResult> {
    this.logger.info(`Retrying workflow from phase: ${retryPhase}`);

    // Reset phases from retry point
    const phaseOrder = [
      WorkflowPhase.PLAN,
      WorkflowPhase.BUSINESS_LOGIC,
      WorkflowPhase.GENERATE,
      WorkflowPhase.DATABASE,
      WorkflowPhase.TESTING,
      WorkflowPhase.DEPLOY
    ];

    const retryIndex = phaseOrder.indexOf(retryPhase);
    for (let i = retryIndex; i < phaseOrder.length; i++) {
      result.phases[phaseOrder[i]].completed = false;
      delete result.phases[phaseOrder[i]].result;
      delete result.phases[phaseOrder[i]].error;
    }

    // Continue execution from retry phase
    let previousResult = retryPhase === WorkflowPhase.PLAN ? undefined :
      retryPhase === WorkflowPhase.BUSINESS_LOGIC ? planResult :
        businessLogicResult || planResult;

    for (let i = retryIndex; i < phaseOrder.length; i++) {
      const phase = phaseOrder[i];
      const phaseResult = await this.executePhaseWithCoordination(
        phase, request, previousResult!, planResult, businessLogicResult
      );

      result.phases[phase] = { completed: true, result: phaseResult };

      if (!phaseResult.success) {
        throw new Error(`${phase} phase failed on retry: ${phaseResult.error}`);
      }

      previousResult = phaseResult;
    }

    result.success = true;
    result.finalOutput = previousResult!.data;

    return result;
  }

  /**
   * Validate pre-phase conditions to prevent known issues
   */
  private async validatePrePhaseConditions(
    phase: WorkflowPhase,
    previousResult?: AgentResult
  ): Promise<{ success: boolean; issues: string[] }> {
    const issues: string[] = [];

    switch (phase) {
      case WorkflowPhase.TESTING:
        // Validate that code generation was successful
        if (!previousResult?.success) {
          issues.push('Previous phase failed - testing may encounter compilation issues');
        }

        // Check for common code generation issues
        if (previousResult?.data?.files) {
          const files = previousResult.data.files;

          // Check for TypeScript files without proper imports
          Object.entries(files).forEach(([filePath, content]) => {
            if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
              if (typeof content === 'string') {
                // Check for missing imports
                if (content.includes('Pool') && !content.includes('import.*Pool.*from.*pg')) {
                  issues.push(`Missing PostgreSQL Pool import in ${filePath}`);
                }

                // Check for incomplete functions
                if (content.includes('export const') && content.includes('=> {') &&
                  !content.includes('// TODO') && content.split('=>').length > content.split('}').length) {
                  issues.push(`Potentially incomplete function in ${filePath}`);
                }
              }
            }
          });
        }
        break;



      case WorkflowPhase.DEPLOY:
        // Validate that DevOps setup was successful
        if (!previousResult?.success) {
          issues.push('DevOps phase failed - deployment may fail');
        }
        break;
    }

    return {
      success: issues.length === 0,
      issues
    };
  }

  /**
   * Validate post-phase quality and provide feedback for improvements
   */
  private async validatePostPhaseQuality(
    phase: WorkflowPhase,
    result: AgentResult
  ): Promise<{ success: boolean; issues: string[]; score: number }> {
    const issues: string[] = [];
    let score = 100;

    if (!result.success) {
      issues.push('Phase execution failed');
      score -= 50;
    }

    switch (phase) {
      case WorkflowPhase.GENERATE:
        // Validate code generation quality
        if (result.data?.files) {
          const files = result.data.files;
          const fileCount = Object.keys(files).length;

          if (fileCount < 10) {
            issues.push('Low file count - may indicate incomplete generation');
            score -= 20;
          }

          // Check for essential files
          const essentialFiles = ['package.json', 'server.ts', 'App.tsx'];
          const missingFiles = essentialFiles.filter(file =>
            !Object.keys(files).some(f => f.includes(file))
          );

          if (missingFiles.length > 0) {
            issues.push(`Missing essential files: ${missingFiles.join(', ')}`);
            score -= 30;
          }
        }
        break;

      case WorkflowPhase.TESTING:
        // Validate testing quality
        if (result.data?.testingResult) {
          const testingResult = result.data.testingResult;

          if (!testingResult.success) {
            issues.push('Testing execution failed');
            score -= 40;
          }

          if (testingResult.metrics?.compilation?.errorCount > 0) {
            issues.push(`${testingResult.metrics.compilation.errorCount} compilation errors remain`);
            score -= 30;
          }

          if (testingResult.metrics?.tests?.passRate < 0.8) {
            issues.push('Low test pass rate - may indicate quality issues');
            score -= 20;
          }
        }
        break;


    }

    return {
      success: score >= 70,
      issues,
      score: Math.max(0, score)
    };
  }

  /**
   * Build enhanced parameters with comprehensive data flow
   */
  private async buildEnhancedParameters(
    phase: WorkflowPhase,
    baseParams: Partial<EnhancedTaskParameters>
  ): Promise<any> {
    const sessionId = baseParams.sessionId!;
    const allResults = this.getPhaseResults(sessionId);

    // Build cross-phase context
    const crossPhaseContext = await this.buildCrossPhaseContext(phase, allResults);

    // Perform cross-phase validation
    const validationResults = await this.performCrossPhaseValidation(phase, allResults);

    // Build phase-specific parameters
    const enhancedParams = {
      ...baseParams,
      allPhaseResults: allResults,
      crossPhaseContext,
      validationResults,
      // Phase-specific data passing
      ...this.getPhaseSpecificParameters(phase, allResults)
    };

    this.logger.info('Enhanced parameters built for phase', {
      phase,
      sessionId,
      contextKeys: Object.keys(crossPhaseContext).length,
      validationIssues: validationResults.reduce((sum, v) => sum + v.issues.length, 0)
    });

    return enhancedParams;
  }

  /**
   * Get phase results for a session
   */
  private getPhaseResults(sessionId: string): Map<WorkflowPhase, AgentResult> {
    return this.sessionPhaseResults.get(sessionId) || new Map();
  }

  /**
   * Store phase result
   */
  private storePhaseResult(sessionId: string, phase: WorkflowPhase, result: AgentResult): void {
    if (!this.sessionPhaseResults.has(sessionId)) {
      this.sessionPhaseResults.set(sessionId, new Map());
    }
    this.sessionPhaseResults.get(sessionId)!.set(phase, result);
  }

  /**
   * Build cross-phase context for enhanced coordination
   */
  private async buildCrossPhaseContext(
    currentPhase: WorkflowPhase,
    allResults: Map<WorkflowPhase, AgentResult>
  ): Promise<Record<string, any>> {
    const context: Record<string, any> = {};

    // Extract key information from completed phases
    for (const [phase, result] of allResults.entries()) {
      if (result.success && result.data) {
        switch (phase) {
          case WorkflowPhase.PLAN:
            context.technicalSpecification = result.data;
            context.projectName = result.data.projectName;
            context.businessDomain = result.data.businessDomain;
            context.architecture = result.data.architecture;
            break;

          case WorkflowPhase.BUSINESS_LOGIC:
            context.domainAPIs = result.data.domainAPIs;
            context.databaseSchema = result.data.databaseSchema;
            context.applicationFlow = result.data.applicationFlow;
            context.businessServices = result.data.businessServices;
            break;

          case WorkflowPhase.GENERATE:
            context.generatedCode = result.data;
            context.fileStructure = result.data.fileStructure;
            context.codeMetrics = result.metadata?.codeMetrics;
            break;



          case WorkflowPhase.DATABASE:
            context.databaseConfig = result.data.configuration;
            context.migrations = result.data.migrations;
            break;
        }
      }
    }

    return context;
  }

  /**
   * Perform cross-phase validation
   */
  private async performCrossPhaseValidation(
    currentPhase: WorkflowPhase,
    allResults: Map<WorkflowPhase, AgentResult>
  ): Promise<ValidationResult[]> {
    const validations: ValidationResult[] = [];

    // API Contract Validation
    if (currentPhase === WorkflowPhase.GENERATE &&
      allResults.has(WorkflowPhase.BUSINESS_LOGIC)) {
      const apiValidation = await this.validateAPIContracts(allResults);
      validations.push(apiValidation);
    }

    // Database Schema Validation
    if (currentPhase === WorkflowPhase.DATABASE &&
      allResults.has(WorkflowPhase.BUSINESS_LOGIC)) {
      const dbValidation = await this.validateDatabaseConsistency(allResults);
      validations.push(dbValidation);
    }

    // Frontend-Backend Alignment
    if (currentPhase === WorkflowPhase.TESTING &&
      allResults.has(WorkflowPhase.GENERATE)) {
      const alignmentValidation = await this.validateFrontendBackendAlignment(allResults);
      validations.push(alignmentValidation);
    }

    return validations;
  }

  /**
   * Get phase-specific parameters based on dependencies
   */
  private getPhaseSpecificParameters(
    phase: WorkflowPhase,
    allResults: Map<WorkflowPhase, AgentResult>
  ): Record<string, any> {
    const params: Record<string, any> = {};

    switch (phase) {
      case WorkflowPhase.BUSINESS_LOGIC:
        if (allResults.has(WorkflowPhase.PLAN)) {
          params.technicalSpec = allResults.get(WorkflowPhase.PLAN)!.data;
        }
        break;

      case WorkflowPhase.GENERATE:
        if (allResults.has(WorkflowPhase.PLAN)) {
          params.technicalSpec = allResults.get(WorkflowPhase.PLAN)!.data;
        }
        if (allResults.has(WorkflowPhase.BUSINESS_LOGIC)) {
          params.businessLogic = allResults.get(WorkflowPhase.BUSINESS_LOGIC)!.data;
        }
        break;

      case WorkflowPhase.DATABASE:
        if (allResults.has(WorkflowPhase.BUSINESS_LOGIC)) {
          params.businessLogic = allResults.get(WorkflowPhase.BUSINESS_LOGIC)!.data;
        }
        if (allResults.has(WorkflowPhase.GENERATE)) {
          params.generatedCode = allResults.get(WorkflowPhase.GENERATE)!.data;
        }
        break;

      case WorkflowPhase.TESTING:
        // Testing needs access to all previous results
        params.allPreviousResults = Object.fromEntries(allResults);
        break;

      case WorkflowPhase.DEPLOY:
        // Deployment needs comprehensive context
        params.allResults = Object.fromEntries(allResults);
        break;
    }

    return params;
  }

  /**
   * Validate API contracts between business logic and generated code
   */
  private async validateAPIContracts(allResults: Map<WorkflowPhase, AgentResult>): Promise<ValidationResult> {
    const businessLogic = allResults.get(WorkflowPhase.BUSINESS_LOGIC)?.data;
    const generatedCode = allResults.get(WorkflowPhase.GENERATE)?.data;

    const issues: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    if (!businessLogic?.domainAPIs || !generatedCode) {
      return {
        success: false,
        issues: ['Missing business logic or generated code for API validation'],
        warnings: [],
        score: 0
      };
    }

    // Check API endpoint consistency
    const businessAPIs = Object.keys(businessLogic.domainAPIs);
    const generatedAPIs = Object.keys(generatedCode.apis || {});

    const missingAPIs = businessAPIs.filter(api => !generatedAPIs.includes(api));
    const extraAPIs = generatedAPIs.filter(api => !businessAPIs.includes(api));

    if (missingAPIs.length > 0) {
      issues.push(`Missing API implementations: ${missingAPIs.join(', ')}`);
      score -= missingAPIs.length * 10;
    }

    if (extraAPIs.length > 0) {
      warnings.push(`Extra API implementations: ${extraAPIs.join(', ')}`);
      score -= extraAPIs.length * 5;
    }

    return {
      success: issues.length === 0,
      issues,
      warnings,
      score: Math.max(0, score),
      metadata: {
        businessAPIs: businessAPIs.length,
        generatedAPIs: generatedAPIs.length,
        missingCount: missingAPIs.length,
        extraCount: extraAPIs.length
      }
    };
  }

  /**
   * Validate database schema consistency
   */
  private async validateDatabaseConsistency(allResults: Map<WorkflowPhase, AgentResult>): Promise<ValidationResult> {
    const businessLogic = allResults.get(WorkflowPhase.BUSINESS_LOGIC)?.data;
    const databaseResult = allResults.get(WorkflowPhase.DATABASE)?.data;

    const issues: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    if (!businessLogic?.databaseSchema || !databaseResult) {
      return {
        success: false,
        issues: ['Missing business logic database schema or database configuration'],
        warnings: [],
        score: 0
      };
    }

    // Check table consistency
    const businessTables = businessLogic.databaseSchema.tables || [];
    const databaseTables = databaseResult.tables || [];

    const businessTableNames = businessTables.map((t: any) => t.name);
    const databaseTableNames = databaseTables.map((t: any) => t.name);

    const missingTables = businessTableNames.filter((name: string) => !databaseTableNames.includes(name));
    const extraTables = databaseTableNames.filter((name: string) => !businessTableNames.includes(name));

    if (missingTables.length > 0) {
      issues.push(`Missing database tables: ${missingTables.join(', ')}`);
      score -= missingTables.length * 15;
    }

    if (extraTables.length > 0) {
      warnings.push(`Extra database tables: ${extraTables.join(', ')}`);
      score -= extraTables.length * 5;
    }

    return {
      success: issues.length === 0,
      issues,
      warnings,
      score: Math.max(0, score),
      metadata: {
        businessTables: businessTableNames.length,
        databaseTables: databaseTableNames.length,
        missingCount: missingTables.length,
        extraCount: extraTables.length
      }
    };
  }

  /**
   * Validate frontend-backend alignment
   */
  private async validateFrontendBackendAlignment(allResults: Map<WorkflowPhase, AgentResult>): Promise<ValidationResult> {
    const generatedCode = allResults.get(WorkflowPhase.GENERATE)?.data;
    const testingResult = allResults.get(WorkflowPhase.TESTING)?.data;

    const issues: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    if (!generatedCode || !uiuxResult) {
      return {
        success: false,
        issues: ['Missing generated code or UI/UX data for alignment validation'],
        warnings: [],
        score: 0
      };
    }

    // Check component-API alignment
    const backendAPIs = Object.keys(generatedCode.apis || {});
    const frontendComponents = Object.keys(uiuxResult.components || {});

    // Basic alignment checks
    if (backendAPIs.length === 0) {
      issues.push('No backend APIs found for frontend integration');
      score -= 30;
    }

    if (frontendComponents.length === 0) {
      issues.push('No frontend components found for backend integration');
      score -= 30;
    }

    // Check for common patterns
    const hasAuthAPI = backendAPIs.some(api => api.toLowerCase().includes('auth'));
    const hasAuthComponent = frontendComponents.some(comp => comp.toLowerCase().includes('auth') || comp.toLowerCase().includes('login'));

    if (hasAuthAPI && !hasAuthComponent) {
      warnings.push('Authentication API exists but no corresponding frontend component found');
      score -= 10;
    }

    return {
      success: issues.length === 0,
      issues,
      warnings,
      score: Math.max(0, score),
      metadata: {
        backendAPIs: backendAPIs.length,
        frontendComponents: frontendComponents.length,
        hasAuthAlignment: hasAuthAPI && hasAuthComponent
      }
    };
  }

  /**
   * Execute workflow with parallel processing optimization
   */
  public async executeWorkflowWithParallelOptimization(request: WorkflowRequest): Promise<AgentResult> {
    if (!this.enableParallelProcessing) {
      return this.executeWorkflow(request);
    }

    this.logger.info('Starting workflow with parallel processing optimization', {
      sessionId: request.context.sessionId,
      requirements: request.requirements.length
    });

    try {
      // Create parallel tasks for all phases
      const parallelTasks = this.createParallelTasks(request);

      // Create execution plan
      const executionPlan = this.parallelProcessingManager.createExecutionPlan(parallelTasks);

      this.logger.info('Parallel execution plan created', {
        sessionId: request.context.sessionId,
        batches: executionPlan.batches.length,
        parallelizationOpportunities: executionPlan.parallelizationOpportunities,
        estimatedTime: executionPlan.totalEstimatedTime
      });

      // Execute with parallel processing
      const parallelResult = await this.parallelProcessingManager.executeParallelPlan(
        executionPlan,
        async (task: AgentTask) => {
          const agent = this.findAgentForTask(task);
          if (!agent) {
            throw new Error(`No agent found for task: ${task.type}`);
          }
          return agent.execute(task);
        }
      );

      // Generate performance report
      const performanceReport = this.performanceManager.getPerformanceReport(request.context.sessionId);

      // Return the final result (typically from DEPLOY phase)
      const finalResult = parallelResult.results.get(WorkflowPhase.DEPLOY) ||
        Array.from(parallelResult.results.values()).pop();

      if (finalResult) {
        finalResult.metadata = {
          ...finalResult.metadata,
          parallelProcessing: {
            enabled: true,
            executionTime: parallelResult.executionTime,
            parallelEfficiency: parallelResult.parallelEfficiency,
            errors: parallelResult.errors
          },
          performanceReport
        };
      }

      return finalResult || {
        success: false,
        data: null,
        metadata: {
          error: 'No final result from parallel execution',
          parallelResult,
          performanceReport
        }
      };

    } catch (error) {
      this.logger.error('Parallel workflow execution failed', {
        sessionId: request.context.sessionId,
        error: error instanceof Error ? error.message : String(error)
      });

      // Fallback to sequential execution
      this.logger.info('Falling back to sequential execution');
      return this.executeWorkflow(request);
    }
  }

  /**
   * Create parallel tasks from workflow request
   */
  private createParallelTasks(request: WorkflowRequest): ParallelTask[] {
    const phases = [
      WorkflowPhase.PLAN,
      WorkflowPhase.BUSINESS_LOGIC,
      WorkflowPhase.GENERATE,
      WorkflowPhase.DATABASE,
      WorkflowPhase.TESTING,
      WorkflowPhase.DEPLOY
    ];

    return phases.map(phase => {
      const task: AgentTask = {
        id: `${request.context.sessionId}-${phase}`,
        type: phase,
        description: request.description,
        parameters: {
          requirements: request.requirements,
          sessionId: request.context.sessionId
        },
        context: request.context
      };

      // Define dependencies and estimated durations
      const dependencies = this.getPhaseDependencies(phase);
      const estimatedDuration = this.getPhaseEstimatedDuration(phase);
      const priority = this.getPhasePriority(phase);

      return this.parallelProcessingManager.createParallelTask(
        phase,
        task,
        dependencies,
        priority,
        estimatedDuration
      );
    });
  }

  /**
   * Get phase dependencies
   */
  private getPhaseDependencies(phase: WorkflowPhase): WorkflowPhase[] {
    const dependencies: Record<WorkflowPhase, WorkflowPhase[]> = {
      [WorkflowPhase.PLAN]: [],
      [WorkflowPhase.BUSINESS_LOGIC]: [WorkflowPhase.PLAN],
      [WorkflowPhase.GENERATE]: [WorkflowPhase.BUSINESS_LOGIC],
      [WorkflowPhase.DATABASE]: [WorkflowPhase.BUSINESS_LOGIC],
      [WorkflowPhase.TESTING]: [WorkflowPhase.GENERATE, WorkflowPhase.DATABASE],
      [WorkflowPhase.DEPLOY]: [WorkflowPhase.TESTING]
    };

    return dependencies[phase] || [];
  }

  /**
   * Get estimated duration for phase
   */
  private getPhaseEstimatedDuration(phase: WorkflowPhase): number {
    const durations: Record<WorkflowPhase, number> = {
      [WorkflowPhase.PLAN]: 30000, // 30 seconds
      [WorkflowPhase.BUSINESS_LOGIC]: 60000, // 1 minute
      [WorkflowPhase.GENERATE]: 120000, // 2 minutes
      [WorkflowPhase.DATABASE]: 60000, // 1 minute
      [WorkflowPhase.TESTING]: 120000, // 2 minutes
      [WorkflowPhase.DEPLOY]: 30000 // 30 seconds
    };

    return durations[phase] || 60000;
  }

  /**
   * Get phase priority
   */
  private getPhasePriority(phase: WorkflowPhase): number {
    const priorities: Record<WorkflowPhase, number> = {
      [WorkflowPhase.PLAN]: 10,
      [WorkflowPhase.BUSINESS_LOGIC]: 9,
      [WorkflowPhase.GENERATE]: 8,
      [WorkflowPhase.DATABASE]: 6,
      [WorkflowPhase.TESTING]: 4,
      [WorkflowPhase.DEPLOY]: 2
    };

    return priorities[phase] || 1;
  }

  /**
   * Enable or disable parallel processing
   */
  public setParallelProcessingEnabled(enabled: boolean): void {
    this.enableParallelProcessing = enabled;
    this.logger.info('Parallel processing toggled', { enabled });
  }

  /**
   * Enable or disable advanced quality gates
   */
  public setAdvancedQualityGatesEnabled(enabled: boolean): void {
    this.enableAdvancedQualityGates = enabled;
    this.logger.info('Advanced quality gates toggled', { enabled });
  }

  /**
   * Enable or disable zero manual intervention
   */
  public setZeroManualInterventionEnabled(enabled: boolean): void {
    this.enableZeroManualIntervention = enabled;
    this.logger.info('Zero manual intervention toggled', { enabled });
  }

  /**
   * Set auto-fix success rate target
   */
  public setAutoFixSuccessRateTarget(target: number): void {
    this.autoFixSuccessRateTarget = Math.max(0, Math.min(100, target));
    this.logger.info('Auto-fix success rate target updated', { target: this.autoFixSuccessRateTarget });
  }

  /**
   * Get previous phase for rollback purposes
   */
  private getPreviousPhase(currentPhase: WorkflowPhase): WorkflowPhase {
    const phaseOrder = [
      WorkflowPhase.PLAN,
      WorkflowPhase.BUSINESS_LOGIC,
      WorkflowPhase.GENERATE,
      WorkflowPhase.DATABASE,
      WorkflowPhase.TESTING,
      WorkflowPhase.DEPLOY
    ];

    const currentIndex = phaseOrder.indexOf(currentPhase);
    return currentIndex > 0 ? phaseOrder[currentIndex - 1] : WorkflowPhase.PLAN;
  }

  /**
   * Get performance and parallel processing statistics
   */
  public getPerformanceStats(sessionId?: string): any {
    const stats: any = {
      parallelProcessing: this.parallelProcessingManager.getParallelProcessingStats(),
      cache: this.performanceManager.getCacheStats(),
      enableParallelProcessing: this.enableParallelProcessing,
      qualityGates: this.advancedQualityGates.getQualityGateStats(sessionId),
      enableAdvancedQualityGates: this.enableAdvancedQualityGates,
      selfHealing: this.selfHealingManager.getSelfHealingStats(),
      errorRecovery: this.errorRecoveryWorkflow.getErrorRecoveryStats(),
      enableZeroManualIntervention: this.enableZeroManualIntervention,
      autoFixSuccessRateTarget: this.autoFixSuccessRateTarget
    };

    if (sessionId) {
      stats.performanceReport = this.performanceManager.getPerformanceReport(sessionId);
      stats.rollbackPoints = this.advancedQualityGates.getRollbackPoints(sessionId);
    }

    return stats;
  }

  /**
   * Get production readiness assessment
   */
  public getProductionReadinessAssessment(sessionId: string): any {
    const rollbackPoints = this.advancedQualityGates.getRollbackPoints(sessionId);
    const performanceReport = this.performanceManager.getPerformanceReport(sessionId);

    // Calculate overall production readiness based on all phases
    let totalScore = 0;
    let phaseCount = 0;
    let criticalIssues: string[] = [];
    let recommendations: string[] = [];

    rollbackPoints.forEach(point => {
      if (point.result.metadata?.qualityGateResult) {
        const qgResult = point.result.metadata.qualityGateResult;
        totalScore += qgResult.productionReadiness.overall;
        phaseCount++;

        criticalIssues.push(...qgResult.productionReadiness.blockers);
        recommendations.push(...qgResult.recommendations);
      }
    });

    const overallReadiness = phaseCount > 0 ? Math.round(totalScore / phaseCount) : 0;
    const readyForProduction = overallReadiness >= 85 && criticalIssues.length === 0;

    return {
      overallReadiness,
      readyForProduction,
      phasesEvaluated: phaseCount,
      criticalIssues: [...new Set(criticalIssues)], // Remove duplicates
      recommendations: [...new Set(recommendations)], // Remove duplicates
      performanceMetrics: performanceReport,
      rollbackPointsAvailable: rollbackPoints.length,
      lastEvaluatedPhase: rollbackPoints[rollbackPoints.length - 1]?.phase
    };
  }

  /**
   * Get comprehensive zero manual intervention assessment
   */
  public getZeroManualInterventionAssessment(sessionId: string): any {
    const selfHealingStats = this.selfHealingManager.getSelfHealingStats();
    const errorRecoveryStats = this.errorRecoveryWorkflow.getErrorRecoveryStats();
    const productionReadiness = this.getProductionReadinessAssessment(sessionId);

    // Calculate auto-fix success rate
    const autoFixSuccessRate = selfHealingStats.overallSuccessRate;
    const meetsSuccessRateTarget = autoFixSuccessRate >= this.autoFixSuccessRateTarget;

    // Assess error patterns and learning
    const errorPatterns = this.selfHealingManager.getErrorPatterns();
    const fixStrategies = this.selfHealingManager.getFixStrategies();
    const learningData = this.selfHealingManager.getLearningData();

    // Calculate learning effectiveness
    const recentLearningData = learningData.filter(d =>
      Date.now() - d.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
    );
    const recentSuccessRate = recentLearningData.length > 0
      ? recentLearningData.filter(d => d.success).length / recentLearningData.length * 100
      : 0;

    // Assess intervention requirements
    const requiresManualIntervention = !meetsSuccessRateTarget ||
      productionReadiness.criticalIssues.length > 0 ||
      productionReadiness.overallReadiness < 85;

    // Generate improvement recommendations
    const improvementRecommendations: string[] = [];

    if (autoFixSuccessRate < this.autoFixSuccessRateTarget) {
      improvementRecommendations.push(`Improve auto-fix success rate from ${autoFixSuccessRate}% to ${this.autoFixSuccessRateTarget}%`);
    }

    if (errorPatterns.length > 20) {
      improvementRecommendations.push('High number of error patterns detected - consider pattern consolidation');
    }

    if (recentSuccessRate < 80) {
      improvementRecommendations.push('Recent learning effectiveness is low - review fix strategies');
    }

    if (fixStrategies.length < 5) {
      improvementRecommendations.push('Limited fix strategies available - consider expanding coverage');
    }

    return {
      zeroManualInterventionEnabled: this.enableZeroManualIntervention,
      autoFixSuccessRate,
      autoFixSuccessRateTarget: this.autoFixSuccessRateTarget,
      meetsSuccessRateTarget,
      requiresManualIntervention,

      // Self-healing metrics
      selfHealing: {
        totalFixStrategies: selfHealingStats.totalFixStrategies,
        errorPatternsLearned: selfHealingStats.errorPatternsLearned,
        totalFixAttempts: selfHealingStats.totalFixAttempts,
        successfulFixes: selfHealingStats.successfulFixes,
        overallSuccessRate: selfHealingStats.overallSuccessRate
      },

      // Error recovery metrics
      errorRecovery: {
        totalRecoverySteps: errorRecoveryStats.totalRecoverySteps,
        activeRecoveries: errorRecoveryStats.activeRecoveries,
        maxRecoveryTime: errorRecoveryStats.maxRecoveryTime,
        maxRecoveryAttempts: errorRecoveryStats.maxRecoveryAttempts
      },

      // Learning and adaptation
      learning: {
        totalErrorPatterns: errorPatterns.length,
        totalFixStrategies: fixStrategies.length,
        totalLearningData: learningData.length,
        recentLearningData: recentLearningData.length,
        recentSuccessRate: Math.round(recentSuccessRate),
        adaptiveLearningActive: true
      },

      // Production readiness integration
      productionReadiness: {
        overallReadiness: productionReadiness.overallReadiness,
        readyForProduction: productionReadiness.readyForProduction,
        criticalIssues: productionReadiness.criticalIssues.length,
        phasesEvaluated: productionReadiness.phasesEvaluated
      },

      // Recommendations for improvement
      improvementRecommendations,

      // Overall assessment
      assessment: {
        zeroManualInterventionReady: meetsSuccessRateTarget && !requiresManualIntervention,
        confidenceLevel: this.calculateZeroInterventionConfidence(
          autoFixSuccessRate,
          productionReadiness.overallReadiness,
          recentSuccessRate
        ),
        nextSteps: this.generateZeroInterventionNextSteps(
          meetsSuccessRateTarget,
          requiresManualIntervention,
          improvementRecommendations
        )
      }
    };
  }

  /**
   * Calculate zero intervention confidence level
   */
  private calculateZeroInterventionConfidence(
    autoFixRate: number,
    productionReadiness: number,
    recentSuccessRate: number
  ): number {
    const weights = {
      autoFix: 0.4,
      production: 0.4,
      learning: 0.2
    };

    const confidence = (
      autoFixRate * weights.autoFix +
      productionReadiness * weights.production +
      recentSuccessRate * weights.learning
    );

    return Math.round(confidence);
  }

  /**
   * Generate next steps for zero intervention readiness
   */
  private generateZeroInterventionNextSteps(
    meetsTarget: boolean,
    requiresIntervention: boolean,
    recommendations: string[]
  ): string[] {
    const nextSteps: string[] = [];

    if (!meetsTarget) {
      nextSteps.push('Focus on improving auto-fix success rate');
      nextSteps.push('Analyze failed fix attempts and enhance strategies');
    }

    if (requiresIntervention) {
      nextSteps.push('Address critical production readiness issues');
      nextSteps.push('Review and resolve manual intervention requirements');
    }

    if (recommendations.length > 0) {
      nextSteps.push('Implement improvement recommendations');
    }

    if (meetsTarget && !requiresIntervention) {
      nextSteps.push('Monitor and maintain zero manual intervention capabilities');
      nextSteps.push('Continue adaptive learning and optimization');
    }

    return nextSteps;
  }

  // TODO: Add workflow state persistence
  // TODO: Add workflow rollback capabilities
  // TODO: Add workflow templates and presets
}

// Quality Gate Implementations
class PlanValidationGate implements QualityGate {
  phase = WorkflowPhase.PLAN;

  async validate(result: AgentResult): Promise<boolean> {
    if (!result.success || !result.data) {
      console.log('PlanValidationGate: Result not successful or no data');
      return false;
    }

    const plan = result.data;
    console.log('PlanValidationGate: Validating plan structure:', {
      hasFeatures: !!plan.features,
      featuresType: typeof plan.features,
      featuresLength: Array.isArray(plan.features) ? plan.features.length : 'not array',
      hasProjectName: !!plan.projectName,
      hasArchitecture: !!plan.architecture
    });

    // Validate plan has required components (TechnicalSpecification structure)
    if (!plan.features || !Array.isArray(plan.features) || plan.features.length === 0) {
      console.log('PlanValidationGate: Features validation failed');
      return false;
    }

    // Validate essential TechnicalSpecification fields exist
    if (!plan.projectName || !plan.architecture) {
      console.log('PlanValidationGate: Missing projectName or architecture');
      return false;
    }

    console.log('PlanValidationGate: Validation passed');
    return true;
  }

  getErrorMessage(): string {
    return 'Plan validation failed: Missing required features, projectName, or architecture in TechnicalSpecification';
  }
}

class BusinessLogicValidationGate implements QualityGate {
  phase = WorkflowPhase.BUSINESS_LOGIC;

  async validate(result: AgentResult): Promise<boolean> {
    if (!result.success || !result.data) {
      console.log('BusinessLogicValidationGate: Result not successful or no data');
      return false;
    }

    const businessLogic = result.data;
    console.log('BusinessLogicValidationGate: Validating business logic structure:', {
      hasDomainAPIs: !!businessLogic.domainAPIs,
      domainAPIsType: typeof businessLogic.domainAPIs,
      domainAPIsCount: businessLogic.domainAPIs ? Object.keys(businessLogic.domainAPIs).length : 0,
      hasDatabaseSchema: !!businessLogic.databaseSchema,
      hasTablesArray: !!businessLogic.databaseSchema?.tables,
      tablesCount: businessLogic.databaseSchema?.tables?.length || 0
    });

    // Validate APIs are defined (BusinessLogicResult structure)
    if (!businessLogic.domainAPIs || typeof businessLogic.domainAPIs !== 'object' || Object.keys(businessLogic.domainAPIs).length === 0) {
      console.log('BusinessLogicValidationGate: Domain APIs validation failed');
      return false;
    }

    // Validate database tables are defined
    if (!businessLogic.databaseSchema || !businessLogic.databaseSchema.tables || !Array.isArray(businessLogic.databaseSchema.tables) || businessLogic.databaseSchema.tables.length === 0) {
      console.log('BusinessLogicValidationGate: Database schema validation failed');
      return false;
    }

    console.log('BusinessLogicValidationGate: Validation passed');
    return true;
  }

  getErrorMessage(): string {
    return 'Business logic validation failed: Missing domainAPIs or database schema tables';
  }
}

class CodeGenerationValidationGate implements QualityGate {
  phase = WorkflowPhase.GENERATE;

  async validate(result: AgentResult): Promise<boolean> {
    if (!result.success || !result.data) {
      console.log('CodeGenerationValidationGate: Result not successful or no data');
      return false;
    }

    const generatedCode = result.data;
    console.log('CodeGenerationValidationGate: Validating generated code structure:', {
      hasFiles: !!generatedCode.files,
      filesType: typeof generatedCode.files,
      filesCount: generatedCode.files ? Object.keys(generatedCode.files).length : 0,
      hasProjectPath: !!generatedCode.projectPath,
      sampleFiles: generatedCode.files ? Object.keys(generatedCode.files).slice(0, 5) : []
    });

    // Validate files were generated
    if (!generatedCode.files || Object.keys(generatedCode.files).length === 0) {
      console.log('CodeGenerationValidationGate: No files generated');
      return false;
    }

    // Validate essential files exist (check for key patterns)
    const fileKeys = Object.keys(generatedCode.files);
    const packageJsonKeys = generatedCode.packageJsons ? Object.keys(generatedCode.packageJsons) : [];

    const hasAppTsx = fileKeys.some(key => key.includes('App.tsx'));
    const hasServerTs = fileKeys.some(key => key.includes('server.ts'));
    const hasPackageJson = packageJsonKeys.some(key => key.includes('package.json')) ||
      fileKeys.some(key => key.includes('package.json'));

    console.log('CodeGenerationValidationGate: Essential files check:', {
      hasAppTsx,
      hasServerTs,
      hasPackageJson,
      totalFiles: fileKeys.length,
      packageJsonFiles: packageJsonKeys.length,
      packageJsonKeys: packageJsonKeys
    });

    if (!hasAppTsx || !hasServerTs || !hasPackageJson) {
      console.log('CodeGenerationValidationGate: Missing essential files');
      return false;
    }

    console.log('CodeGenerationValidationGate: Validation passed');
    return true;
  }

  getErrorMessage(): string {
    return 'Code generation validation failed: Missing essential files (App.tsx, server.ts, package.json) or empty generation';
  }
}



class DatabaseValidationGate implements QualityGate {
  phase = WorkflowPhase.DATABASE;

  async validate(result: AgentResult): Promise<boolean> {
    if (!result.success || !result.data) {
      return false;
    }

    const databaseResult = result.data.databaseResult;

    // Validate database consistency was achieved
    if (!databaseResult || !databaseResult.success) {
      return false;
    }

    // Validate schema consistency score is high
    if (databaseResult.consistencyScore < 90) {
      return false;
    }

    return true;
  }

  getErrorMessage(): string {
    return 'Database validation failed: Schema consistency issues not resolved';
  }
}

class TestingValidationGate implements QualityGate {
  phase = WorkflowPhase.TESTING;

  async validate(result: AgentResult): Promise<boolean> {
    if (!result.success || !result.data) {
      return false;
    }

    const testingResult = result.data.testingResult;

    // Validate testing was successful
    if (!testingResult || !testingResult.success) {
      return false;
    }

    // Validate no critical errors remain
    if (testingResult.metrics && testingResult.metrics.compilation) {
      if (!testingResult.metrics.compilation.backend || !testingResult.metrics.compilation.frontend) {
        return false;
      }
      if (testingResult.metrics.compilation.errorCount > 0) {
        return false;
      }
    }

    return true;
  }

  getErrorMessage(): string {
    return 'Testing validation failed: Critical compilation or functionality errors remain';
  }
}

// Enhanced Orchestrator Integration Methods - Moved to main Orchestrator class
